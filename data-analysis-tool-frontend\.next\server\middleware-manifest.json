{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2c2f7f9b._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_9e9e701a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "85e9d409afb030abbe5cba9d13cd1f60", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d2c2c50a6bad7a5169f37b97a1e39a35d01278425bb4ee9a744553ef82a622ba", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ca41f6c55bb1700235779ca1a873c92ddafdcf3210fb28ed65945bf65f164b87"}}}, "sortedMiddleware": ["/"], "functions": {}}
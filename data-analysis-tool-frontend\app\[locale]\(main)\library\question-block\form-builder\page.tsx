"use client";

import { FormPreview } from "@/components/form-preview";
import { useState } from "react";
import { Question } from "@/types/formBuilder";
import { useQuery } from "@tanstack/react-query";
import { fetchQuestionBlockQuestions } from "@/lib/api/form-builder";
import Spinner from "@/components/general/Spinner";
import { FormBuilder } from "@/components/form-builder/FormBuilder";
import { useAuth } from "@/hooks/useAuth";

const permissions = {
  viewForm: true,
  editForm: true,
  viewSubmissions: true,
  addSubmissions: true,
  deleteSubmissions: true,
  editSubmissions: true,
  manageProject: true,
  validateSubmissions: true,
};

export default function FormBuilderPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const { user } = useAuth();

  // We'll use the user ID as the context ID for question blocks
  // This is based on the schema where LibraryQuestionBlockQuestion is directly related to User
  const userId = user?.id;

  const {
    data: questionsData,
    isLoading: questionsLoading,
    isError: questionsError,
  } = useQuery<Question[]>({
    queryKey: ["questionBlockQuestions", userId],
    queryFn: () => fetchQuestionBlockQuestions(),
    enabled: !!userId,
    retry: 1
  });

  if (!userId) {
    return (
      <div className="p-6 text-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h1 className="text-xl font-semibold text-red-600 mb-2">Authentication Required</h1>
          <p className="text-neutral-700 mb-4">
            You need to be logged in to access the question block form builder.
          </p>
          <a href="/" className="inline-block px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark">
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  if (questionsLoading || !questionsData) {
    return <Spinner />;
  }

  if (questionsError) {
    return (
      <div className="p-4">
        <h2 className="text-lg font-semibold text-red-500">
          Error loading question block information
        </h2>
        <p className="text-sm text-red-500 mt-2">
          There was a problem fetching the questions. Please try refreshing the page.
        </p>
        <p className="text-sm text-gray-500 mt-2">
          If the problem persists, please check the browser console for more details.
        </p>
      </div>
    );
  }

  // Ensure questionsData is always an array
  const safeQuestionsData = Array.isArray(questionsData) ? questionsData : [];

  return (
    <div className="p-6">
      {isPreviewMode ? (
        <FormPreview
          questions={safeQuestionsData}
          questionGroups={[]}
          contextType="questionBlock"
          onClose={() => setIsPreviewMode(false)}
        />
      ) : (
        <FormBuilder
          setIsPreviewMode={setIsPreviewMode}
          contextType="questionBlock"
          contextId={userId}
          permissions={permissions}
        />
      )}
    </div>
  );
}

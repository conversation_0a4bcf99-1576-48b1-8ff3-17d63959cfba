module.exports = {

"[project]/lib/get-messages.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getMessages": (()=>getMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [app-rsc] (ecmascript) <export default as getRequestConfig>");
;
// Define supported locales
const locales = [
    'en',
    'ne'
];
async function getMessages(locale) {
    // Validate locale
    if (!locales.includes(locale)) {
        console.warn(`Unsupported locale: ${locale}, falling back to 'en'`);
        locale = 'en';
    }
    try {
        // Dynamic import of the messages file based on locale
        const messages = (await __turbopack_context__.f({
            "../messages/en.json": {
                id: ()=>"[project]/messages/en.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/messages/en.json (json, async loader)")(__turbopack_context__.i)
            },
            "../messages/ne.json": {
                id: ()=>"[project]/messages/ne.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/messages/ne.json (json, async loader)")(__turbopack_context__.i)
            }
        }).import(`../messages/${locale}.json`)).default;
        if (!messages || typeof messages !== 'object') {
            throw new Error(`Invalid messages format for locale: ${locale}`);
        }
        return messages;
    } catch (error) {
        console.error(`Failed to load messages for locale: ${locale}`, error);
        // Fallback to English if the requested locale fails
        if (locale !== 'en') {
            try {
                console.log('Falling back to English messages');
                return (await __turbopack_context__.r("[project]/messages/en.json (json, async loader)")(__turbopack_context__.i)).default;
            } catch (fallbackError) {
                console.error('Failed to load fallback English messages', fallbackError);
            }
        }
        // Return empty object as last resort
        return {};
    }
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // Ensure locale is a valid string
    const localeString = locale?.toString() || 'en';
    return {
        // The locale must be included in the return object
        locale: localeString,
        // Load messages for the locale
        messages: await getMessages(localeString),
        // Set timezone for date formatting
        timeZone: 'Asia/Kathmandu',
        // Default formats for dates, numbers, etc.
        formats: {
            dateTime: {
                short: {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric'
                },
                medium: {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                },
                long: {
                    weekday: 'long',
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                }
            },
            number: {
                currency: {
                    style: 'currency',
                    currency: 'NPR'
                }
            }
        }
    };
});
}}),
"[project]/app/[locale]/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LocaleLayout),
    "generateStaticParams": (()=>generateStaticParams)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$NextIntlClientProviderServer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__NextIntlClientProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js [app-rsc] (ecmascript) <export default as NextIntlClientProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$get$2d$messages$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/get-messages.ts [app-rsc] (ecmascript)");
;
;
;
;
// Define supported locales
const locales = [
    'en',
    'ne'
];
function generateStaticParams() {
    return locales.map((locale)=>({
            locale
        }));
}
async function LocaleLayout({ children, params }) {
    // Await the params before accessing its properties
    const { locale } = await params;
    // Validate that the incoming `locale` parameter is valid
    const isValidLocale = locales.includes(locale);
    if (!isValidLocale) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    // Load messages for the locale
    const messages = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$get$2d$messages$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMessages"])(locale);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$NextIntlClientProviderServer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__NextIntlClientProvider$3e$__["NextIntlClientProvider"], {
        locale: locale,
        messages: messages,
        children: children
    }, void 0, false, {
        fileName: "[project]/app/[locale]/layout.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=_e73d4873._.js.map
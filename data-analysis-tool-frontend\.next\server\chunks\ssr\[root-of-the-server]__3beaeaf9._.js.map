{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/slices/notificationSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\n\r\ntype NotificationType = \"\" | \"error\" | \"warning\" | \"success\";\r\n\r\ntype NotificationInitialState = {\r\n  message: string;\r\n  type: NotificationType;\r\n  visible: boolean;\r\n};\r\n\r\nconst initialState: NotificationInitialState = {\r\n  message: \"\",\r\n  type: \"\",\r\n  visible: false,\r\n};\r\n\r\nconst notificationSlice = createSlice({\r\n  name: \"notification\",\r\n  initialState,\r\n  reducers: {\r\n    showNotification: (\r\n      state,\r\n      action: PayloadAction<{ message: string; type: NotificationType }>\r\n    ) => {\r\n      state.message = action.payload.message;\r\n      state.type = action.payload.type;\r\n      state.visible = true;\r\n    },\r\n\r\n    hideNotification: (state) => {\r\n      state.message = \"\";\r\n      state.type = \"\";\r\n      state.visible = false;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { showNotification, hideNotification } = notificationSlice.actions;\r\nexport default notificationSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAUA,MAAM,eAAyC;IAC7C,SAAS;IACT,MAAM;IACN,SAAS;AACX;AAEA,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACpC,MAAM;IACN;IACA,UAAU;QACR,kBAAkB,CAChB,OACA;YAEA,MAAM,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO;YACtC,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,OAAO,GAAG;QAClB;QAEA,kBAAkB,CAAC;YACjB,MAAM,OAAO,GAAG;YAChB,MAAM,IAAI,GAAG;YACb,MAAM,OAAO,GAAG;QAClB;IACF;AACF;AAEO,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,kBAAkB,OAAO;uCAChE,kBAAkB,OAAO", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/slices/createProjectSlice.ts"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nconst createProjectSlice = createSlice({\r\n  name: \"createProject\",\r\n  initialState: { visible: false },\r\n  reducers: {\r\n    showCreateProjectModal: (state) => {\r\n      state.visible = true;\r\n    },\r\n\r\n    hideCreateProjectModal: (state) => {\r\n      state.visible = false;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { showCreateProjectModal, hideCreateProjectModal } =\r\n  createProjectSlice.actions;\r\nexport default createProjectSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,qBAAqB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACrC,MAAM;IACN,cAAc;QAAE,SAAS;IAAM;IAC/B,UAAU;QACR,wBAAwB,CAAC;YACvB,MAAM,OAAO,GAAG;QAClB;QAEA,wBAAwB,CAAC;YACvB,MAAM,OAAO,GAAG;QAClB;IACF;AACF;AAEO,MAAM,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,GAC7D,mBAAmB,OAAO;uCACb,mBAAmB,OAAO", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/slices/authSlice.tsx"], "sourcesContent": ["import { AuthState, UserSession } from \"@/types/authTypes\";\r\nimport { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\n\r\nconst initialState: AuthState = {\r\n  status: \"loading\",\r\n  user: null,\r\n  error: null,\r\n};\r\n\r\nconst authSlice = createSlice({\r\n  name: \"auth\",\r\n  initialState,\r\n  reducers: {\r\n    setAuthenticatedUser: (state, action: PayloadAction<UserSession>) => {\r\n      state.status = \"authenticated\";\r\n      state.user = action.payload;\r\n      state.error = null;\r\n    },\r\n    setUnauthenticated: (state) => {\r\n      state.status = \"unauthenticated\";\r\n      state.user = null;\r\n      state.error = null;\r\n    },\r\n    setAuthLoading: (state) => {\r\n      state.status = \"loading\";\r\n    },\r\n    setAuthError: (state, action: PayloadAction<string>) => {\r\n      state.status = \"unauthenticated\";\r\n      state.error = action.payload;\r\n      state.user = null;\r\n    },\r\n  },\r\n});\r\n\r\nexport const {\r\n  setAuthenticatedUser,\r\n  setUnauthenticated,\r\n  setAuthLoading,\r\n  setAuthError,\r\n} = authSlice.actions;\r\n\r\nexport default authSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;;AACA;;AAEA,MAAM,eAA0B;IAC9B,QAAQ;IACR,MAAM;IACN,OAAO;AACT;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,sBAAsB,CAAC,OAAO;YAC5B,MAAM,MAAM,GAAG;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,KAAK,GAAG;QAChB;QACA,oBAAoB,CAAC;YACnB,MAAM,MAAM,GAAG;YACf,MAAM,IAAI,GAAG;YACb,MAAM,KAAK,GAAG;QAChB;QACA,gBAAgB,CAAC;YACf,MAAM,MAAM,GAAG;QACjB;QACA,cAAc,CAAC,OAAO;YACpB,MAAM,MAAM,GAAG;YACf,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,IAAI,GAAG;QACf;IACF;AACF;AAEO,MAAM,EACX,oBAAoB,EACpB,kBAAkB,EAClB,cAAc,EACd,YAAY,EACb,GAAG,UAAU,OAAO;uCAEN,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/slices/createLibrarySlice.ts"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\ntype CreateLibraryState = {\r\n  visible: boolean;\r\n};\r\n\r\nconst initialState: CreateLibraryState = {\r\n  visible: false,\r\n};\r\n\r\nconst createLibrarySlice = createSlice({\r\n  name: \"createLibraryItem\",\r\n  initialState,\r\n  reducers: {\r\n    showCreateLibraryModal: (state) => {\r\n      state.visible = true;\r\n    },\r\n    hideCreateLibraryModal: (state) => {\r\n      state.visible = false;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { showCreateLibraryModal, hideCreateLibraryModal } =\r\n  createLibrarySlice.actions;\r\nexport default createLibrarySlice.reducer;\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAMA,MAAM,eAAmC;IACvC,SAAS;AACX;AAEA,MAAM,qBAAqB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACrC,MAAM;IACN;IACA,UAAU;QACR,wBAAwB,CAAC;YACvB,MAAM,OAAO,GAAG;QAClB;QACA,wBAAwB,CAAC;YACvB,MAAM,OAAO,GAAG;QAClB;IACF;AACF;AAEO,MAAM,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,GAC7D,mBAAmB,OAAO;uCACb,mBAAmB,OAAO", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/slices/createLibraryItemSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from \"@reduxjs/toolkit\";\r\n\r\nconst createLibraryItemSlice = createSlice({\r\n  initialState: {\r\n    visible: false,\r\n    option: \"\",\r\n  },\r\n  name: \"createLibraryItem\",\r\n  reducers: {\r\n    showCreateLibraryItemModal: (state, action: PayloadAction<string>) => {\r\n      state.visible = true;\r\n      state.option = action.payload;\r\n    },\r\n    hideCreateLibraryItemModal: (state) => {\r\n      state.visible = false;\r\n      state.option = \"\";\r\n    },\r\n  },\r\n});\r\n\r\nexport const { showCreateLibraryItemModal, hideCreateLibraryItemModal } =\r\n  createLibraryItemSlice.actions;\r\nexport default createLibraryItemSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,yBAAyB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACzC,cAAc;QACZ,SAAS;QACT,QAAQ;IACV;IACA,MAAM;IACN,UAAU;QACR,4BAA4B,CAAC,OAAO;YAClC,MAAM,OAAO,GAAG;YAChB,MAAM,MAAM,GAAG,OAAO,OAAO;QAC/B;QACA,4BAA4B,CAAC;YAC3B,MAAM,OAAO,GAAG;YAChB,MAAM,MAAM,GAAG;QACjB;IACF;AACF;AAEO,MAAM,EAAE,0BAA0B,EAAE,0BAA0B,EAAE,GACrE,uBAAuB,OAAO;uCACjB,uBAAuB,OAAO", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/redux/store.ts"], "sourcesContent": ["import { configureStore } from \"@reduxjs/toolkit\";\r\nimport notificationReducer from \"@/redux/slices/notificationSlice\";\r\nimport createProjectReducer from \"@/redux/slices/createProjectSlice\";\r\nimport authReducer from \"@/redux/slices/authSlice\";\r\nimport createLibraryReducer from \"./slices/createLibrarySlice\";\r\nimport createLibraryItemReducer from \"./slices/createLibraryItemSlice\";\r\n\r\nexport const store = configureStore({\r\n  reducer: {\r\n    notification: notificationReducer,\r\n    createProject: createProjectReducer,\r\n    auth: authReducer,\r\n    createLibrary: createLibraryReducer,\r\n    createLibraryItem: createLibraryItemReducer,\r\n  },\r\n});\r\n\r\nexport type RootState = ReturnType<typeof store.getState>;\r\nexport type AppDispatch = typeof store.dispatch;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,cAAc,oIAAA,CAAA,UAAmB;QACjC,eAAe,qIAAA,CAAA,UAAoB;QACnC,MAAM,6HAAA,CAAA,UAAW;QACjB,eAAe,qIAAA,CAAA,UAAoB;QACnC,mBAAmB,yIAAA,CAAA,UAAwB;IAC7C;AACF", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReduxProvider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { store } from \"@/redux/store\";\r\nimport React from \"react\";\r\nimport { Provider } from \"react-redux\";\r\n\r\nconst ReduxProvider = ({ children }: { children: React.ReactNode }) => {\r\n  return <Provider store={store}>{children}</Provider>;\r\n};\r\n\r\nexport { ReduxProvider };\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;IAChE,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,8GAAA,CAAA,QAAK;kBAAG;;;;;;AAClC", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Notification.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { hideNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { CircleAlert, CircleCheck, TriangleAlert } from \"lucide-react\";\r\n\r\nconst Notification: React.FC = () => {\r\n  const dispatch = useDispatch();\r\n  const { message, type, visible } = useSelector(\r\n    (state: RootState) => state.notification\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (visible) {\r\n      const timer = setTimeout(() => {\r\n        dispatch(hideNotification());\r\n      }, 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [visible, dispatch]);\r\n\r\n  //Different bg color for different notification types\r\n  const bgColor =\r\n    type === \"success\"\r\n      ? \"bg-green-500 hover:bg-green-600\"\r\n      : type === \"warning\"\r\n      ? \"bg-yellow-500 hover:bg-yellow-600\"\r\n      : \"bg-red-500 hover:bg-red-600\";\r\n\r\n  const icon: React.ReactNode =\r\n    type === \"success\" ? (\r\n      <CircleCheck />\r\n    ) : type === \"warning\" ? (\r\n      <TriangleAlert />\r\n    ) : (\r\n      <CircleAlert />\r\n    );\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {visible && (\r\n        <motion.div\r\n          className={`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${bgColor} transition-colors duration-300`}\r\n          onClick={() => dispatch(hideNotification())}\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -20 }}\r\n          transition={{ duration: 0.3, ease: \"easeIn\" }}\r\n        >\r\n          <span className=\"text-2xl\">{icon}</span>\r\n          <span className=\"break-words neutral-100space-normal\">{message}</span>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport { Notification };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAPA;;;;;;;AASA,MAAM,eAAyB;IAC7B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC3C,CAAC,QAAqB,MAAM,YAAY;IAG1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,QAAQ,WAAW;gBACvB,SAAS,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD;YAC1B,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAS;IAEtB,qDAAqD;IACrD,MAAM,UACJ,SAAS,YACL,oCACA,SAAS,YACT,sCACA;IAEN,MAAM,OACJ,SAAS,0BACP,8OAAC,oNAAA,CAAA,cAAW;;;;eACV,SAAS,0BACX,8OAAC,wNAAA,CAAA,gBAAa;;;;6BAEd,8OAAC,oNAAA,CAAA,cAAW;;;;;IAGhB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAC,oHAAoH,EAAE,QAAQ,+BAA+B,CAAC;YAC1K,SAAS,IAAM,SAAS,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD;YACvC,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAS;;8BAE5C,8OAAC;oBAAK,WAAU;8BAAY;;;;;;8BAC5B,8OAAC;oBAAK,WAAU;8BAAuC;;;;;;;;;;;;;;;;;AAKjE", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReactQueryProvider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { useState } from \"react\";\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\r\n\r\nconst ReactQueryProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            staleTime: 1000 * 60 * 5, // 5 minutes (adjust as needed)\r\n            refetchOnWindowFocus: false, // Optional: prevents refetching when switching tabs\r\n          },\r\n        },\r\n      })\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n      <ReactQueryDevtools initialIsOpen={false} />\r\n    </QueryClientProvider>\r\n  );\r\n};\r\n\r\nexport { ReactQueryProvider };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAAiC;IACrE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,WAAW,OAAO,KAAK;oBACvB,sBAAsB;gBACxB;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,8OAAC,oLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC", "debugId": null}}]}
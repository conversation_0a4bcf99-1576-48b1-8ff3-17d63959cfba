{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/addUser/AddUser.tsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { addProjectUser, checkUserExists } from \"@/lib/api/projects\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface AddUserProps {\r\n  onClose: () => void; // Callback when the modal/dialog closes\r\n  projectId?: number; // Project ID to which the user will be added\r\n  onUserAdded?: () => void; // Callback after successful addition\r\n}\r\n\r\nconst AddUser = ({ onClose, projectId, onUserAdded }: AddUserProps) => {\r\n  const t = useTranslations();\r\n  \r\n  // Move permissions array inside component where we can use t()\r\n  const permissions = [\r\n    { label: t('viewForm'), value: \"viewForm\" },\r\n    { label: t('editForm'), value: \"editForm\" },\r\n    { label: t('viewSubmissions'), value: \"viewSubmissions\" },\r\n    { label: t('editSubmissions'), value: \"editSubmissions\" },\r\n    { label: t('addSubmissions'), value: \"addSubmissions\" },\r\n    { label: t('deleteSubmissions'), value: \"deleteSubmissions\" },\r\n    { label: t('validateSubmissions'), value: \"validateSubmissions\" },\r\n    { label: t('manageProject'), value: \"manageProject\" },\r\n  ];\r\n\r\n  const [email, setEmail] = useState(\"\");\r\n  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [isVerifying, setIsVerifying] = useState(false);\r\n  const [userExists, setUserExists] = useState<boolean | null>(null);\r\n\r\n  const queryClient = useQueryClient(); // Used to invalidate cached project user list\r\n  const dispatch = useDispatch(); // Redux dispatch for showing notifications\r\n  const emailCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Debounce timer ref\r\n\r\n  // Email validation using basic but reliable regex\r\n  const isValidEmail = (email: string) => {\r\n    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/.test(email);\r\n  };\r\n\r\n  // Toggle permissions when checkboxes are clicked\r\n  const handlePermissionChange = (value: string) => {\r\n    setSelectedPermissions(\r\n      (prev) =>\r\n        prev.includes(value)\r\n          ? prev.filter((v) => v !== value) // remove if already selected\r\n          : [...prev, value] // add if not selected\r\n    );\r\n  };\r\n\r\n  // Mutation to verify if user with provided email exists\r\n  const checkUserExistsMutation = useMutation({\r\n    mutationFn: checkUserExists,\r\n    onSuccess: () => {\r\n      setUserExists(true);\r\n      setError(\"\");\r\n    },\r\n    onError: (error: any) => {\r\n      setUserExists(false);\r\n      const errorMessage =\r\n        error.response?.data?.message || t('userNotFound');\r\n      setError(errorMessage);\r\n    },\r\n    onSettled: () => {\r\n      setIsVerifying(false);\r\n    },\r\n  });\r\n\r\n  // Called on each email input change\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newEmail = e.target.value;\r\n    setEmail(newEmail);\r\n    setUserExists(null);\r\n    setError(\"\");\r\n\r\n    if (!newEmail) return;\r\n\r\n    if (!isValidEmail(newEmail)) {\r\n      setError(t('invalidEmail'));\r\n      return;\r\n    }\r\n\r\n    // Cancel any previously scheduled user check\r\n    if (emailCheckTimeoutRef.current) {\r\n      clearTimeout(emailCheckTimeoutRef.current);\r\n    }\r\n\r\n    // Debounced call to verify email after 800ms\r\n    emailCheckTimeoutRef.current = setTimeout(() => {\r\n      setIsVerifying(true);\r\n      checkUserExistsMutation.mutate(newEmail);\r\n    }, 800);\r\n  };\r\n\r\n  // Form validation logic\r\n  const isFormValid = () => {\r\n    if (!email) {\r\n      setError(t('emailRequired'));\r\n      return false;\r\n    }\r\n    if (!isValidEmail(email)) {\r\n      setError(t('invalidEmail'));\r\n      return false;\r\n    }\r\n    if (!userExists) {\r\n      setError(t('userNotFound'));\r\n      return false;\r\n    }\r\n    if (selectedPermissions.length === 0) {\r\n      setError(t('selectPermission'));\r\n      return false;\r\n    }\r\n    setError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Mutation to add user to project with selected permissions\r\n  const addUserMutation = useMutation({\r\n    mutationFn: () => {\r\n      // Convert permission array to object format required by API\r\n      const permissionsObject = selectedPermissions.reduce(\r\n        (accumulator, permission) => {\r\n          accumulator[permission] = true;\r\n          return accumulator;\r\n        },\r\n        {} as Record<string, boolean>\r\n      );\r\n\r\n      return addProjectUser({\r\n        projectId: projectId!,\r\n        email,\r\n        permissions: permissionsObject,\r\n      });\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate cached project users to refetch fresh list\r\n      queryClient.invalidateQueries({ queryKey: [\"projectUsers\", projectId] });\r\n      dispatch(\r\n        showNotification({\r\n          message: t('userAdded'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      if (onUserAdded) onUserAdded();\r\n      onClose();\r\n    },\r\n    onError: (error: any) => {\r\n      // Handle and display error message from API or fallback\r\n      let errorMessage: string;\r\n      if (typeof error === \"string\") {\r\n        errorMessage = error;\r\n      } else if (error instanceof Error) {\r\n        errorMessage = error.message;\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage =\r\n          typeof error.response.data.message === \"object\"\r\n            ? JSON.stringify(error.response.data.message)\r\n            : error.response.data.message;\r\n      } else {\r\n        errorMessage = t('failedToAddUser');\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: errorMessage,\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setError(errorMessage);\r\n    },\r\n  });\r\n\r\n  // Final submit handler for adding the user\r\n  const handleSubmit = () => {\r\n    if (!projectId) {\r\n      setError(t('projectIdRequired'));\r\n      return;\r\n    }\r\n\r\n    if (isFormValid()) {\r\n      addUserMutation.mutate();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 p-6 rounded-md\">\r\n      {/* Email input with validation */}\r\n      <div className=\"relative\">\r\n        <input\r\n          className={`w-full border ${\r\n            error ? \"border-red-500\" : \"border-neutral-300\"\r\n          } rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`}\r\n          placeholder={t('email')}\r\n          value={email}\r\n          onChange={handleEmailChange}\r\n        />\r\n        {/* Close button for modal */}\r\n        <button\r\n          className=\"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900\"\r\n          onClick={onClose}\r\n          type=\"button\"\r\n        >\r\n          <X size={22} />\r\n        </button>\r\n        {/* Status messages */}\r\n        {isVerifying && (\r\n          <p className=\"text-neutral-500 text-sm mb-2\">{t('verifyingEmail')}</p>\r\n        )}\r\n        {userExists === true && (\r\n          <p className=\"text-green-500 text-sm mb-2\">{t('userFound')}</p>\r\n        )}\r\n        {error && <p className=\"text-red-500 text-sm mb-2\">{error}</p>}\r\n      </div>\r\n\r\n      {/* Permissions checkboxes */}\r\n      <div className=\"flex flex-col gap-2\">\r\n        {permissions.map((permission) => (\r\n          <div key={permission.value} className=\"flex flex-col\">\r\n            <label className=\"flex items-center gap-2\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPermissions.includes(permission.value)}\r\n                onChange={() => handlePermissionChange(permission.value)}\r\n              />\r\n              {permission.label}\r\n            </label>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Submit button */}\r\n      <button\r\n        className={`mt-6 ${\r\n          addUserMutation.isPending || isVerifying\r\n            ? \"bg-neutral-400\"\r\n            : \"bg-blue-400 hover:bg-blue-500\"\r\n        } text-white px-6 py-2 rounded disabled:opacity-50`}\r\n        disabled={\r\n          addUserMutation.isPending ||\r\n          isVerifying ||\r\n          !email ||\r\n          selectedPermissions.length === 0 ||\r\n          !userExists\r\n        }\r\n        onClick={handleSubmit}\r\n      >\r\n        {addUserMutation.isPending ? t('adding') : t('grantPermissions')}\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { AddUser };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAQA,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAgB;;IAChE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,+DAA+D;IAC/D,MAAM,cAAc;QAClB;YAAE,OAAO,EAAE;YAAa,OAAO;QAAW;QAC1C;YAAE,OAAO,EAAE;YAAa,OAAO;QAAW;QAC1C;YAAE,OAAO,EAAE;YAAoB,OAAO;QAAkB;QACxD;YAAE,OAAO,EAAE;YAAoB,OAAO;QAAkB;QACxD;YAAE,OAAO,EAAE;YAAmB,OAAO;QAAiB;QACtD;YAAE,OAAO,EAAE;YAAsB,OAAO;QAAoB;QAC5D;YAAE,OAAO,EAAE;YAAwB,OAAO;QAAsB;QAChE;YAAE,OAAO,EAAE;YAAkB,OAAO;QAAgB;KACrD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE7D,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,KAAK,8CAA8C;IACpF,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,KAAK,2CAA2C;IAC3E,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB,OAAO,qBAAqB;IAEvF,kDAAkD;IAClD,MAAM,eAAe,CAAC;QACpB,OAAO,mDAAmD,IAAI,CAAC;IACjE;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,CAAC;QAC9B,uBACE,CAAC,OACC,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,OAAO,6BAA6B;eAC7D;mBAAI;gBAAM;aAAM,CAAC,sBAAsB;;IAEjD;IAEA,wDAAwD;IACxD,MAAM,0BAA0B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,yHAAA,CAAA,kBAAe;QAC3B,SAAS;4DAAE;gBACT,cAAc;gBACd,SAAS;YACX;;QACA,OAAO;4DAAE,CAAC;gBACR,cAAc;gBACd,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WAAW,EAAE;gBACrC,SAAS;YACX;;QACA,SAAS;4DAAE;gBACT,eAAe;YACjB;;IACF;IAEA,oCAAoC;IACpC,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,cAAc;QACd,SAAS;QAET,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,aAAa,WAAW;YAC3B,SAAS,EAAE;YACX;QACF;QAEA,6CAA6C;QAC7C,IAAI,qBAAqB,OAAO,EAAE;YAChC,aAAa,qBAAqB,OAAO;QAC3C;QAEA,6CAA6C;QAC7C,qBAAqB,OAAO,GAAG,WAAW;YACxC,eAAe;YACf,wBAAwB,MAAM,CAAC;QACjC,GAAG;IACL;IAEA,wBAAwB;IACxB,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO;YACV,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,CAAC,aAAa,QAAQ;YACxB,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,CAAC,YAAY;YACf,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,SAAS,EAAE;YACX,OAAO;QACT;QACA,SAAS;QACT,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAClC,UAAU;oDAAE;gBACV,4DAA4D;gBAC5D,MAAM,oBAAoB,oBAAoB,MAAM;8EAClD,CAAC,aAAa;wBACZ,WAAW,CAAC,WAAW,GAAG;wBAC1B,OAAO;oBACT;6EACA,CAAC;gBAGH,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;oBACpB,WAAW;oBACX;oBACA,aAAa;gBACf;YACF;;QACA,SAAS;oDAAE;gBACT,wDAAwD;gBACxD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB;qBAAU;gBAAC;gBACtE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,IAAI,aAAa;gBACjB;YACF;;QACA,OAAO;oDAAE,CAAC;gBACR,wDAAwD;gBACxD,IAAI;gBACJ,IAAI,OAAO,UAAU,UAAU;oBAC7B,eAAe;gBACjB,OAAO,IAAI,iBAAiB,OAAO;oBACjC,eAAe,MAAM,OAAO;gBAC9B,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACxC,eACE,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,WACnC,KAAK,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAC1C,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACnC,OAAO;oBACL,eAAe,EAAE;gBACnB;gBAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,SAAS;YACX;;IACF;IAEA,2CAA2C;IAC3C,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW;YACd,SAAS,EAAE;YACX;QACF;QAEA,IAAI,eAAe;YACjB,gBAAgB,MAAM;QACxB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,CAAC,cAAc,EACxB,QAAQ,mBAAmB,qBAC5B,2GAA2G,CAAC;wBAC7G,aAAa,EAAE;wBACf,OAAO;wBACP,UAAU;;;;;;kCAGZ,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,MAAK;kCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;oBAGV,6BACC,6LAAC;wBAAE,WAAU;kCAAiC,EAAE;;;;;;oBAEjD,eAAe,sBACd,6LAAC;wBAAE,WAAU;kCAA+B,EAAE;;;;;;oBAE/C,uBAAS,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAItD,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;wBAA2B,WAAU;kCACpC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,oBAAoB,QAAQ,CAAC,WAAW,KAAK;oCACtD,UAAU,IAAM,uBAAuB,WAAW,KAAK;;;;;;gCAExD,WAAW,KAAK;;;;;;;uBAPX,WAAW,KAAK;;;;;;;;;;0BAc9B,6LAAC;gBACC,WAAW,CAAC,KAAK,EACf,gBAAgB,SAAS,IAAI,cACzB,mBACA,gCACL,iDAAiD,CAAC;gBACnD,UACE,gBAAgB,SAAS,IACzB,eACA,CAAC,SACD,oBAAoB,MAAM,KAAK,KAC/B,CAAC;gBAEH,SAAS;0BAER,gBAAgB,SAAS,GAAG,EAAE,YAAY,EAAE;;;;;;;;;;;;AAIrD;GAhPM;;QACM,yMAAA,CAAA,kBAAe;QAoBL,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAmBI,iLAAA,CAAA,cAAW;QAkEnB,iLAAA,CAAA,cAAW;;;KA3G/B", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ShareProjectModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { LuPlus } from \"react-icons/lu\";\r\nimport { Project } from \"@/types\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { AddUser } from \"../addUser/AddUser\";\r\nimport axios from \"@/lib/axios\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst ShareProjectModal = ({\r\n  showModal,\r\n  onClose,\r\n  onShare,\r\n  selectedProject,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onShare: () => void;\r\n  selectedProject?: Project;\r\n  selectedProjects?: Project[];\r\n}) => {\r\n  const { hashedId } = useParams();\r\n  const { user } = useAuth();\r\n  const [showAddUser, setShowAddUser] = useState(false);\r\n\r\n  // Get project ID from either selected project or URL\r\n  const urlProjectId = hashedId ? decode(hashedId as string) : null;\r\n  const projectId = selectedProject?.id || urlProjectId;\r\n  const t = useTranslations();\r\n\r\n  // Fetch project details using the project ID\r\n  const { data: projectData, isLoading: projectLoading } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: async () => {\r\n      const data = await fetchProjectById({ projectId: projectId! });\r\n      return data;\r\n    },\r\n    enabled: !!projectId && !!user?.id,\r\n  });\r\n\r\n  // Fetch project users\r\n  const [projectUsers, setProjectUsers] = useState([]);\r\n  const [usersLoading, setUsersLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      if (!projectId) return;\r\n\r\n      setUsersLoading(true);\r\n      try {\r\n        const response = await axios.get(`/project-users/${projectId}`);\r\n\r\n        if (response.data && response.data.data && response.data.data.AllUser) {\r\n          const users = response.data.data.AllUser || [];\r\n          setProjectUsers(users);\r\n        } else {\r\n          console.warn(\"No users data in response:\", response.data);\r\n          setProjectUsers([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching project users:\", error);\r\n        setProjectUsers([]);\r\n      } finally {\r\n        setUsersLoading(false);\r\n      }\r\n    };\r\n\r\n    if (showModal && projectId) {\r\n      fetchUsers();\r\n    }\r\n  }, [projectId, showModal]);\r\n\r\n  // If loading, show spinner\r\n  if (projectLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // Use the fetched project data or fallback to selected project\r\n  const displayData = projectData || selectedProject;\r\n\r\n  // If we have no data at all, show error\r\n  if (!displayData) {\r\n    return (\r\n      <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n        <div className=\"text-center py-4\">\r\n          <p className=\"text-red-500\">{t('projectNotFound')}</p>\r\n          <Button onClick={onClose} className=\"mt-4\">\r\n            {t('close')}\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    );\r\n  }\r\n\r\n  // Generate avatar color based on user name\r\n  const getAvatarColor = (name?: string) => {\r\n    if (!name) return \"bg-gray-500\"; // Default color if no name provided\r\n    const colors = [\r\n      \"bg-green-500\",\r\n      \"bg-blue-500\",\r\n      \"bg-red-500\",\r\n      \"bg-purple-500\",\r\n      \"bg-yellow-500\",\r\n      \"bg-pink-500\",\r\n      \"bg-indigo-500\",\r\n      \"bg-orange-500\",\r\n    ];\r\n    const charCode = name.charCodeAt(0);\r\n    return colors[charCode % colors.length];\r\n  };\r\n\r\n  // Get the first letter of the name for the avatar\r\n  const getInitial = (name?: string) => {\r\n    return name ? name.charAt(0).toUpperCase() : \"?\";\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n        {`${t('sharingProject')}: ${displayData.name || \"\"}`}\r\n      </h2>\r\n\r\n      <div className=\"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <div className=\"text-xl font-semibold\">{t('whoHasAccess')}</div>\r\n          <div\r\n            className=\"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50\"\r\n            onClick={() => setShowAddUser(true)}\r\n          >\r\n            <LuPlus size={18} className=\"mr-2\" />\r\n            <div className=\"text-sm\">{t('addUser')}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* User List */}\r\n        <div className=\"space-y-4\">\r\n          {/* Project Owner */}\r\n          {displayData.user && (\r\n            <div className=\"flex items-center\">\r\n              <div\r\n                className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                  displayData.user.name\r\n                )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n              >\r\n                {getInitial(displayData.user.name)}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"font-medium\">\r\n                  {displayData.user.name ||\r\n                    displayData.user.email ||\r\n                    t('unknownUser')}\r\n                </div>\r\n                <div className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\">\r\n                  {t('owner')}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Project Users */}\r\n          {usersLoading ? (\r\n            <div className=\"py-2 text-center\">\r\n              <div className=\"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin\"></div>\r\n            </div>\r\n          ) : projectUsers && projectUsers.length > 0 ? (\r\n            projectUsers.map((projectUser: any, index: number) => {\r\n              const userName =\r\n                (projectUser.user && projectUser.user.name) ||\r\n                (projectUser.user && projectUser.user.email) ||\r\n                `User ${projectUser.userId}`;\r\n\r\n              return (\r\n                <div key={index} className=\"flex items-center mt-4\">\r\n                  <div\r\n                    className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                      userName\r\n                    )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n                  >\r\n                    {getInitial(userName)}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium\">{userName}</div>\r\n                    <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                      {projectUser.permission &&\r\n                        Object.entries(projectUser.permission)\r\n                          .filter(([key, value]) => value === true)\r\n                          .map(([key]) => (\r\n                            <div\r\n                              key={key}\r\n                              className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\"\r\n                            >\r\n                              {key === \"viewForm\"\r\n                                ? t('viewForm')\r\n                                : key === \"editForm\"\r\n                                ? t('editForm')\r\n                                : key === \"viewSubmissions\"\r\n                                ? t('viewSubmissions')\r\n                                : key === \"editSubmissions\"\r\n                                ? t('editSubmissions')\r\n                                : key === \"addSubmissions\"\r\n                                ? t('addSubmissions')\r\n                                : key === \"deleteSubmissions\"\r\n                                ? t('deleteSubmissions')\r\n                                : key === \"validateSubmissions\"\r\n                                ? t('validateSubmissions')\r\n                                : key === \"manageProject\"\r\n                                ? t('manageProject')\r\n                                : key}\r\n                            </div>\r\n                          ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })\r\n          ) : null}\r\n        </div>\r\n\r\n        {/* AddUser form */}\r\n        {showAddUser && projectId && (\r\n          <div className=\"mt-6\">\r\n            <AddUser\r\n              onClose={() => setShowAddUser(false)}\r\n              projectId={projectId}\r\n              onUserAdded={() => {\r\n                // Refetch users when a new user is added\r\n                const fetchUsers = async () => {\r\n                  setUsersLoading(true);\r\n                  try {\r\n                    const response = await axios.get(\r\n                      `/project-users/${projectId}`\r\n                    );\r\n                    if (\r\n                      response.data &&\r\n                      response.data.data &&\r\n                      response.data.data.AllUser\r\n                    ) {\r\n                      const users = response.data.data.AllUser || [];\r\n                      setProjectUsers(users);\r\n                    } else {\r\n                      setProjectUsers([]);\r\n                    }\r\n                  } catch (error) {\r\n                    console.error(\"Error fetching project users:\", error);\r\n                    setProjectUsers([]);\r\n                  } finally {\r\n                    setUsersLoading(false);\r\n                  }\r\n                };\r\n                fetchUsers();\r\n              }}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Anonymous Submissions */}\r\n        <div className=\"mt-8 border-t pt-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <div>\r\n              <div className=\"font-medium\">{t('anonymousSubmissions')}</div>\r\n              <div className=\"text-sm text-gray-500 mt-1\">\r\n                {t('allowAnonymousSubmissions')}\r\n              </div>\r\n            </div>\r\n            <div className=\"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer\">\r\n              <div className=\"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copy Team Button */}\r\n        <div className=\"mt-8\">\r\n          <div className=\"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50\">\r\n            {t('copyTeamFromProject')}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ShareProjectModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,OAAO,EACP,eAAe,EAOhB;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qDAAqD;IACrD,MAAM,eAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE,YAAsB;IAC7D,MAAM,YAAY,iBAAiB,MAAM;IACzC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,6CAA6C;IAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QACzE,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;0CAAE;gBACP,MAAM,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;gBAC5D,OAAO;YACT;;QACA,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM;IAClC;IAEA,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;0DAAa;oBACjB,IAAI,CAAC,WAAW;oBAEhB,gBAAgB;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;wBAE9D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BACrE,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;4BAC9C,gBAAgB;wBAClB,OAAO;4BACL,QAAQ,IAAI,CAAC,8BAA8B,SAAS,IAAI;4BACxD,gBAAgB,EAAE;wBACpB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,gBAAgB,EAAE;oBACpB,SAAU;wBACR,gBAAgB;oBAClB;gBACF;;YAEA,IAAI,aAAa,WAAW;gBAC1B;YACF;QACF;sCAAG;QAAC;QAAW;KAAU;IAEzB,2BAA2B;IAC3B,IAAI,gBAAgB;QAClB,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,+DAA+D;IAC/D,MAAM,cAAc,eAAe;IAEnC,wCAAwC;IACxC,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC,iIAAA,CAAA,UAAK;YAAC,QAAQ;YAAW,SAAS;YAAS,WAAU;sBACpD,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB,EAAE;;;;;;kCAC/B,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,WAAU;kCACjC,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO,eAAe,oCAAoC;QACrE,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,WAAW,KAAK,UAAU,CAAC;QACjC,OAAO,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC;IACzC;IAEA,kDAAkD;IAClD,MAAM,aAAa,CAAC;QAClB,OAAO,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK;IAC/C;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QAAC,QAAQ;QAAW,SAAS;QAAS,WAAU;;0BACpD,6LAAC;gBAAG,WAAU;0BACX,GAAG,EAAE,kBAAkB,EAAE,EAAE,YAAY,IAAI,IAAI,IAAI;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyB,EAAE;;;;;;0CAC1C,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAW,EAAE;;;;;;;;;;;;;;;;;;kCAKhC,6LAAC;wBAAI,WAAU;;4BAEZ,YAAY,IAAI,kBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,YAAY,IAAI,CAAC,IAAI,EACrB,mEAAmE,CAAC;kDAErE,WAAW,YAAY,IAAI,CAAC,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,YAAY,IAAI,CAAC,IAAI,IACpB,YAAY,IAAI,CAAC,KAAK,IACtB,EAAE;;;;;;0DAEN,6LAAC;gDAAI,WAAU;0DACZ,EAAE;;;;;;;;;;;;;;;;;;4BAOV,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;uCAEf,gBAAgB,aAAa,MAAM,GAAG,IACxC,aAAa,GAAG,CAAC,CAAC,aAAkB;gCAClC,MAAM,WACJ,AAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,IACzC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,KAAK,IAC3C,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE;gCAE9B,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,UACA,mEAAmE,CAAC;sDAErE,WAAW;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe;;;;;;8DAC9B,6LAAC;oDAAI,WAAU;8DACZ,YAAY,UAAU,IACrB,OAAO,OAAO,CAAC,YAAY,UAAU,EAClC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,UAAU,MACnC,GAAG,CAAC,CAAC,CAAC,IAAI,iBACT,6LAAC;4DAEC,WAAU;sEAET,QAAQ,aACL,EAAE,cACF,QAAQ,aACR,EAAE,cACF,QAAQ,oBACR,EAAE,qBACF,QAAQ,oBACR,EAAE,qBACF,QAAQ,mBACR,EAAE,oBACF,QAAQ,sBACR,EAAE,uBACF,QAAQ,wBACR,EAAE,yBACF,QAAQ,kBACR,EAAE,mBACF;2DAnBC;;;;;;;;;;;;;;;;;mCAhBT;;;;;4BA0Cd,KACE;;;;;;;oBAIL,eAAe,2BACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,UAAO;4BACN,SAAS,IAAM,eAAe;4BAC9B,WAAW;4BACX,aAAa;gCACX,yCAAyC;gCACzC,MAAM,aAAa;oCACjB,gBAAgB;oCAChB,IAAI;wCACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,eAAe,EAAE,WAAW;wCAE/B,IACE,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,IAAI,IAClB,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAC1B;4CACA,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;4CAC9C,gBAAgB;wCAClB,OAAO;4CACL,gBAAgB,EAAE;wCACpB;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,iCAAiC;wCAC/C,gBAAgB,EAAE;oCACpB,SAAU;wCACR,gBAAgB;oCAClB;gCACF;gCACA;4BACF;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAe,EAAE;;;;;;sDAChC,6LAAC;4CAAI,WAAU;sDACZ,EAAE;;;;;;;;;;;;8CAGP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA/QM;;QAYiB,qIAAA,CAAA,YAAS;QACb,oHAAA,CAAA,UAAO;QAMd,yMAAA,CAAA,kBAAe;QAGgC,8KAAA,CAAA,WAAQ;;;KAtB7D", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/tables/columns/ProjectListColumns.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { LuArrowUpDown } from \"react-icons/lu\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Project } from \"@/types\";\r\nimport Link from \"next/link\";\r\nimport { encode } from \"@/lib/encodeDecode\";\r\nimport { formatDate } from \"@/lib/utils\";\r\nimport { Eye } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\n// Function to format cell value based on data type\r\nconst formatCellValue = (value: any, type?: string, t?: any): string => {\r\n  if (value === null || value === undefined) return \"-\";\r\n\r\n  if (typeof value === \"boolean\") {\r\n    return value ? (t ? t(\"yes\") : \"Yes\") : (t ? t(\"no\") : \"No\");\r\n  }\r\n\r\n  if (value instanceof Date) {\r\n    return formatDate(value);\r\n  }\r\n\r\n  if (type === \"date\" && typeof value === \"string\") {\r\n    try {\r\n      return formatDate(new Date(value));\r\n    } catch {\r\n      return value;\r\n    }\r\n  }\r\n\r\n  return String(value);\r\n};\r\n\r\n// Hook to create columns with translations\r\nexport const useProjectListColumns = (): ColumnDef<Project>[] => {\r\n  const t = useTranslations();\r\n\r\n  return [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          className=\"w-6 h-6 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer\"\r\n          checked={\r\n            table.getIsAllPageRowsSelected() ||\r\n            (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n          }\r\n          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n          aria-label={t(\"select\")}\r\n        />\r\n      ),\r\n      cell: ({ row }) => {\r\n        const projectId = row.original.id;\r\n        const encryptedProjectId = encode(projectId);\r\n\r\n        return (\r\n          <div className=\"flex items-center gap-2\">\r\n            <Checkbox\r\n              className=\"w-6 h-6 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 cursor-pointer\"\r\n              checked={row.getIsSelected()}\r\n              onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n              aria-label={t(\"select\")}\r\n            />\r\n            <Link\r\n              title={t(\"overview\")}\r\n              href={`project/${encryptedProjectId}/overview`}\r\n              className=\" hover:text-primary-500 transition-colors\"\r\n            >\r\n              <Eye className=\"w-5 h-5\" />\r\n            </Link>\r\n          </div>\r\n        );\r\n      },\r\n      enableHiding: false,\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <button\r\n            className=\"flex items-center text-left\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n          >\r\n            {t(\"projectName\")}\r\n            <LuArrowUpDown className=\"h-4 w-4\" />\r\n          </button>\r\n        );\r\n      },\r\n      cell: ({ row }) => {\r\n        const projectId = row.original.id;\r\n        const encryptedProjectId = encode(projectId);\r\n        return (\r\n          <Link\r\n            href={`project/${encryptedProjectId}/overview`}\r\n            className=\"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300\"\r\n          >\r\n            {row.getValue(\"name\")}\r\n          </Link>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n      sortingFn: (rowA, rowB, columnId) => {\r\n        const a = rowA.getValue(columnId)?.toString().toLowerCase() || \"\";\r\n        const b = rowB.getValue(columnId)?.toString().toLowerCase() || \"\";\r\n        return a.localeCompare(b);\r\n      },\r\n    },\r\n\r\n    {\r\n      accessorKey: \"description\",\r\n      header: t(\"description\"),\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: t(\"status\"),\r\n    },\r\n    {\r\n      accessorKey: \"updatedAt\",\r\n      header: t(\"dateModified\"),\r\n      cell: ({ getValue }) => {\r\n        const value = getValue();\r\n        return (\r\n          <div className=\"font-medium text-neutral-700\">\r\n            {formatCellValue(value, \"date\", t) || t(\"notRecorded\") || \"Not recorded\"}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"lastDeployedAt\",\r\n      header: t(\"dateDeployed\"),\r\n    },\r\n    {\r\n      accessorKey: \"sector\",\r\n      header: t(\"sector\"),\r\n    },\r\n    {\r\n      accessorKey: \"country\",\r\n      header: t(\"countries\"),\r\n    },\r\n    {\r\n      accessorKey: \"lastSubmittionAt\",\r\n      header: t(\"lastSubmissionAt\"),\r\n      sortingFn: \"basic\",\r\n    },\r\n  ];\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;AAYA,mDAAmD;AACnD,MAAM,kBAAkB,CAAC,OAAY,MAAe;IAClD,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAElD,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO,QAAS,IAAI,EAAE,SAAS,QAAU,IAAI,EAAE,QAAQ;IACzD;IAEA,IAAI,iBAAiB,MAAM;QACzB,OAAO,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE;IACpB;IAEA,IAAI,SAAS,UAAU,OAAO,UAAU,UAAU;QAChD,IAAI;YACF,OAAO,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK;QAC7B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,OAAO;AAChB;AAGO,MAAM,wBAAwB;;IACnC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,OAAO;QACL;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,gIAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;oBAExC,iBAAiB,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;oBAC9D,cAAY,EAAE;;;;;;YAGlB,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC,EAAE;gBACjC,MAAM,qBAAqB,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;gBAElC,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,WAAQ;4BACP,WAAU;4BACV,SAAS,IAAI,aAAa;4BAC1B,iBAAiB,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;4BACjD,cAAY,EAAE;;;;;;sCAEhB,6LAAC,+JAAA,CAAA,UAAI;4BACH,OAAO,EAAE;4BACT,MAAM,CAAC,QAAQ,EAAE,mBAAmB,SAAS,CAAC;4BAC9C,WAAU;sCAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;YAIvB;YACA,cAAc;QAChB;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;wBAE5D,EAAE;sCACH,6LAAC,iJAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;YAG/B;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC,EAAE;gBACjC,MAAM,qBAAqB,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;gBAClC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAC,QAAQ,EAAE,mBAAmB,SAAS,CAAC;oBAC9C,WAAU;8BAET,IAAI,QAAQ,CAAC;;;;;;YAGpB;YACA,eAAe;YACf,WAAW,CAAC,MAAM,MAAM;gBACtB,MAAM,IAAI,KAAK,QAAQ,CAAC,WAAW,WAAW,iBAAiB;gBAC/D,MAAM,IAAI,KAAK,QAAQ,CAAC,WAAW,WAAW,iBAAiB;gBAC/D,OAAO,EAAE,aAAa,CAAC;YACzB;QACF;QAEA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,MAAM,CAAC,EAAE,QAAQ,EAAE;gBACjB,MAAM,QAAQ;gBACd,qBACE,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,OAAO,QAAQ,MAAM,EAAE,kBAAkB;;;;;;YAGhE;QACF;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;QACZ;QACA;YACE,aAAa;YACb,QAAQ,EAAE;YACV,WAAW;QACb;KACD;AACH;GAhHa;;QACD,yMAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ProjectListClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { JS<PERSON>, useEffect, useState } from \"react\";\r\nimport {\r\n  FaChevronDown,\r\n  FaChevronUp,\r\n  FaArchive,\r\n  FaUserPlus,\r\n} from \"react-icons/fa\";\r\nimport { RiDeleteBin6Fill } from \"react-icons/ri\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nimport { Table as ReactTable, VisibilityState } from \"@tanstack/react-table\";\r\nimport { ConfirmationModal } from \"@/components/modals/ConfirmationModal\";\r\nimport { ShareProjectModal } from \"@/components/modals/ShareProjectModal\";\r\nimport { Project } from \"@/types\";\r\nimport { GeneralTable } from \"./tables/GeneralTable\";\r\nimport { useProjectListColumns } from \"./tables/columns/ProjectListColumns\";\r\nimport {\r\n  archiveMultipleProjects,\r\n  deleteMultipleProjects,\r\n} from \"@/lib/api/projects\";\r\nimport { useQueryClient, useMutation } from \"@tanstack/react-query\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface ProjectListClientProps {\r\n  data: Project[];\r\n}\r\n\r\n\r\nconst STORAGE_KEY = \"data-table-column-visibility\";\r\n\r\nconst ProjectListClient = ({ data }: ProjectListClientProps) => {\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n  const t = useTranslations();\r\n  const columns = useProjectListColumns();\r\n\r\n  const { user } = useAuth();\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [tableInstance, setTableInstance] =\r\n    useState<ReactTable<Project> | null>(null);\r\n\r\n  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);\r\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\r\n  const [confirmationModalContent, setConfirmationModalContent] = useState<{\r\n    title: string;\r\n    description: string | JSX.Element;\r\n    confirmButtonText: string;\r\n    confirmButtonClass: string;\r\n    onConfirm: () => void;\r\n  } | null>(null);\r\n\r\n  const [showShareModal, setShowShareModal] = useState(false);\r\n  const [selectedProject, setSelectedProject] = useState<Project | undefined>(\r\n    undefined\r\n  );\r\n\r\n  // State to track if any rows are selected\r\n  const [isAnyRowSelected, setIsAnyRowSelected] = useState(false);\r\n  const [isSingleRowSelected, setIsSingleRowSelected] = useState(false);\r\n  const [isAnyDeployedSelected, setIsAnyDeployedSelected] = useState(false);\r\n\r\n  // Archive mutation\r\n  const archiveMutation = useMutation({\r\n    mutationFn: (projectIds: number[]) => archiveMultipleProjects(projectIds),\r\n    onSuccess: (data, variables) => {\r\n      // Show success notification\r\n      const projectCount = variables.length;\r\n      const message =\r\n        projectCount === 1\r\n          ? t('projectArchived')\r\n          : `${projectCount} ${t('projectArchivedSuccess')}`;\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message,\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Invalidate projects query to trigger a refetch\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n\r\n      // Clear row selection after successful operation\r\n      tableInstance?.resetRowSelection();\r\n\r\n      // Close modal\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error archiving projects:\", error);\r\n\r\n      // Show error notification\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectArchiveFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n\r\n      // Close modal\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // Delete mutation\r\n  const deleteMutation = useMutation({\r\n    mutationFn: (projectIds: number[]) => deleteMultipleProjects(projectIds),\r\n    onSuccess: (data, variables) => {\r\n      // Show success notification\r\n      const projectCount = variables.length;\r\n      const message =\r\n        projectCount === 1\r\n          ? t('projectDeleted')\r\n          : `${projectCount} ${t('projectDeleted')}`;\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message,\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Invalidate projects query to trigger a refetch\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n\r\n      // Clear row selection after successful operation\r\n      tableInstance?.resetRowSelection();\r\n\r\n      // Close modal\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error deleting projects:\", error);\r\n\r\n      // Show error notification\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectDeleteFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n\r\n      // Close modal\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // Callback to monitor row selection changes\r\n  const handleRowSelectionChange = (rowSelection: Record<string, boolean>) => {\r\n    const selectedRowIds = Object.keys(rowSelection);\r\n\r\n    const selectedRows = data.filter((_, index) => {\r\n      const isSelected = selectedRowIds.includes(index.toString());\r\n      return isSelected;\r\n    });\r\n\r\n    // Check if ALL selected rows have deployed status\r\n    const allAreDeployed =\r\n      selectedRows.length > 0 &&\r\n      selectedRows.every((row) => row.status.toLowerCase() === \"deployed\");\r\n\r\n    setIsAnyRowSelected(selectedRowIds.length > 0);\r\n    setIsSingleRowSelected(selectedRowIds.length === 1);\r\n    setIsAnyDeployedSelected(allAreDeployed);\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    setConfirmationModalContent({\r\n      title: t('confirmDelete'),\r\n      description: (\r\n        <>\r\n          <p>\r\n            {t('confirmDeleteMessage')}\r\n          </p>\r\n          <ul className=\"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700\">\r\n            <li>{t('deleteProjectWarning1')}</li>\r\n            <li>{t('deleteProjectWarning2')}</li>\r\n            <li>\r\n              {t('deleteProjectWarning3')}\r\n            </li>\r\n          </ul>\r\n        </>\r\n      ),\r\n      confirmButtonText: t('delete'),\r\n      confirmButtonClass: \"bg-red-500 hover:bg-red-600 cursor-pointer\",\r\n      onConfirm: () => {\r\n        const selectedRowIds =\r\n          tableInstance\r\n            ?.getSelectedRowModel()\r\n            .rows.map((row) => row.original.id) || [];\r\n        // Execute the mutation\r\n        deleteMutation.mutate(selectedRowIds);\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const handleArchiveClick = () => {\r\n    setConfirmationModalContent({\r\n      title: t('confirmArchive'),\r\n      description:\r\n        t('confirmArchiveMessage'),\r\n      confirmButtonText: t('archive'),\r\n      confirmButtonClass: \"btn-primary\",\r\n      onConfirm: () => {\r\n        const selectedRowIds =\r\n          tableInstance\r\n            ?.getSelectedRowModel()\r\n            .rows.map((row) => row.original.id) || [];\r\n        // Execute the mutation\r\n        archiveMutation.mutate(selectedRowIds);\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const handleShareClick = () => {\r\n    const selectedRow = tableInstance?.getSelectedRowModel().rows[0]?.original;\r\n\r\n    if (selectedRow) {\r\n      setSelectedProject(selectedRow);\r\n      setShowShareModal(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    try {\r\n      const saved = localStorage.getItem(STORAGE_KEY);\r\n      if (saved) {\r\n        const parsed = JSON.parse(saved);\r\n        if (parsed && typeof parsed === \"object\" && !Array.isArray(parsed)) {\r\n          setColumnVisibility(parsed);\r\n        } else {\r\n          console.warn(\"Invalid format in localstorage for column visibility\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading column visibility:\", error);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (Object.keys(columnVisibility).length > 0) {\r\n      try {\r\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));\r\n      } catch (error) {\r\n        console.error(\"Error saving column visibility:\", error);\r\n      }\r\n    }\r\n  }, [columnVisibility]);\r\n\r\n  const handleColumnVisibilityChange = (newState: VisibilityState) => {\r\n    setColumnVisibility(newState);\r\n  };\r\n\r\n  const handleTableInit = (table: ReactTable<Project>) => {\r\n    setTableInstance(table);\r\n\r\n    if (Object.keys(columnVisibility).length > 0) {\r\n      table.setColumnVisibility(columnVisibility);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 rounded-md\">\r\n      <div className=\"px-6 py-2\">\r\n        <div className=\"flex flex-col laptop:flex-row justify-between items-center laptop:gap-5 mobile:p-4 laptop:p-2\">\r\n          <div className=\"flex flex-col items-center gap-2 laptop:gap-14 laptop:flex-row  text-neutral-700 laptop:p-4\">\r\n            <h2 className=\" font-semibold\">{t(\"myProjects\")}</h2>\r\n            <div className=\"flex flex-col tablet:flex-row gap-4 items-center py-4\">\r\n              <Input\r\n                placeholder={t(\"searchAllColumns\")}\r\n                value={globalFilter}\r\n                onChange={(e) => setGlobalFilter(e.target.value)}\r\n              />\r\n\r\n              {tableInstance && (\r\n                <DropdownMenu\r\n                  open={isDropdownOpen}\r\n                  onOpenChange={(open) => setIsDropdownOpen(open)}\r\n                >\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      className=\"flex items-center gap-2 cursor-pointer\"\r\n                    >\r\n                      {t(\"showHideColumns\")}\r\n                      {isDropdownOpen ? (\r\n                        <FaChevronUp className=\"w-3 h-3\" />\r\n                      ) : (\r\n                        <FaChevronDown className=\"w-3 h-3\" />\r\n                      )}\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent\r\n                    align=\"start\"\r\n                    className=\" border bg-neutral-100 border-neutral-200 shadow-md\"\r\n                  >\r\n                    {tableInstance\r\n                      .getAllColumns()\r\n                      .filter((column) => column.getCanHide())\r\n                      .map((column) => (\r\n                        <DropdownMenuCheckboxItem\r\n                          key={column.id}\r\n                          className=\"capitalize cursor-pointer hover:bg-neutral-200\"\r\n                          checked={columnVisibility[column.id] ?? true}\r\n                          onCheckedChange={(value) =>\r\n                            setColumnVisibility((prev) => ({\r\n                              ...prev,\r\n                              [column.id]: value,\r\n                            }))\r\n                          }\r\n                        >\r\n                          {column.id}\r\n                        </DropdownMenuCheckboxItem>\r\n                      ))}\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex gap-4 text-neutral-700\">\r\n            <div\r\n              title={t('archive')}\r\n              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${\r\n                isAnyDeployedSelected\r\n                  ? \"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer\"\r\n                  : \"opacity-50\"\r\n              }`}\r\n              onClick={isAnyDeployedSelected ? handleArchiveClick : undefined}\r\n            >\r\n              <FaArchive className=\"h-4 w-4\" />\r\n            </div>\r\n            <div\r\n              title={t('share')}\r\n              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${\r\n                isSingleRowSelected\r\n                  ? \"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer\"\r\n                  : \"opacity-50\"\r\n              }`}\r\n              onClick={isSingleRowSelected ? handleShareClick : undefined}\r\n            >\r\n              <FaUserPlus className=\"h-4 w-4\" />\r\n            </div>\r\n            <div\r\n              title={t('delete')}\r\n              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${\r\n                isAnyRowSelected\r\n                  ? \"hover:bg-primary-500 hover:text-neutral-100 cursor-pointer\"\r\n                  : \"opacity-50\"\r\n              }`}\r\n              onClick={isAnyRowSelected ? handleDeleteClick : undefined}\r\n            >\r\n              <RiDeleteBin6Fill className=\"h-4 w-4\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\" mx-auto\">\r\n          <GeneralTable\r\n            columns={columns}\r\n            data={data}\r\n            globalFilter={globalFilter}\r\n            setGlobalFilter={setGlobalFilter}\r\n            onTableInit={handleTableInit}\r\n            columnVisibility={columnVisibility}\r\n            setColumnVisibility={handleColumnVisibilityChange}\r\n            onRowSelectionChange={handleRowSelectionChange} // Monitor row selection changes\r\n          />\r\n        </div>\r\n      </div>\r\n      {confirmationModalContent && (\r\n        <ConfirmationModal\r\n          showModal={showConfirmationModal}\r\n          onClose={() => setShowConfirmationModal(false)}\r\n          onConfirm={confirmationModalContent.onConfirm}\r\n          title={confirmationModalContent.title}\r\n          description={confirmationModalContent.description}\r\n          confirmButtonText={confirmationModalContent.confirmButtonText}\r\n          confirmButtonClass={confirmationModalContent.confirmButtonClass}\r\n        />\r\n      )}\r\n      <ShareProjectModal\r\n        showModal={showShareModal}\r\n        selectedProject={selectedProject}\r\n        onClose={() => setShowShareModal(false)}\r\n        onShare={() => {\r\n          setShowShareModal(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { ProjectListClient };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AAOA;AAGA;AACA;AAEA;AACA;AACA;AAIA;AAAA;AACA;AACA;AACA;AACA;;;AAlCA;;;;;;;;;;;;;;;;;AAyCA,MAAM,cAAc;AAEpB,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAA0B;;IACzD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,UAAU,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD;IAEpC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IAEvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAM7D;IAEV,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD;IAGF,0CAA0C;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAClC,UAAU;8DAAE,CAAC,aAAyB,CAAA,GAAA,yHAAA,CAAA,0BAAuB,AAAD,EAAE;;QAC9D,SAAS;8DAAE,CAAC,MAAM;gBAChB,4BAA4B;gBAC5B,MAAM,eAAe,UAAU,MAAM;gBACrC,MAAM,UACJ,iBAAiB,IACb,EAAE,qBACF,GAAG,aAAa,CAAC,EAAE,EAAE,2BAA2B;gBAEtD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf;oBACA,MAAM;gBACR;gBAGF,iDAAiD;gBACjD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY,MAAM;qBAAG;gBAAC;gBAEjE,iDAAiD;gBACjD,eAAe;gBAEf,cAAc;gBACd,yBAAyB;YAC3B;;QACA,OAAO;8DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,6BAA6B;gBAE3C,0BAA0B;gBAC1B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,cAAc;gBACd,yBAAyB;YAC3B;;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;6DAAE,CAAC,aAAyB,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;;QAC7D,SAAS;6DAAE,CAAC,MAAM;gBAChB,4BAA4B;gBAC5B,MAAM,eAAe,UAAU,MAAM;gBACrC,MAAM,UACJ,iBAAiB,IACb,EAAE,oBACF,GAAG,aAAa,CAAC,EAAE,EAAE,mBAAmB;gBAE9C,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf;oBACA,MAAM;gBACR;gBAGF,iDAAiD;gBACjD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY,MAAM;qBAAG;gBAAC;gBAEjE,iDAAiD;gBACjD,eAAe;gBAEf,cAAc;gBACd,yBAAyB;YAC3B;;QACA,OAAO;6DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,4BAA4B;gBAE1C,0BAA0B;gBAC1B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,cAAc;gBACd,yBAAyB;YAC3B;;IACF;IAEA,4CAA4C;IAC5C,MAAM,2BAA2B,CAAC;QAChC,MAAM,iBAAiB,OAAO,IAAI,CAAC;QAEnC,MAAM,eAAe,KAAK,MAAM,CAAC,CAAC,GAAG;YACnC,MAAM,aAAa,eAAe,QAAQ,CAAC,MAAM,QAAQ;YACzD,OAAO;QACT;QAEA,kDAAkD;QAClD,MAAM,iBACJ,aAAa,MAAM,GAAG,KACtB,aAAa,KAAK,CAAC,CAAC,MAAQ,IAAI,MAAM,CAAC,WAAW,OAAO;QAE3D,oBAAoB,eAAe,MAAM,GAAG;QAC5C,uBAAuB,eAAe,MAAM,KAAK;QACjD,yBAAyB;IAC3B;IAEA,MAAM,oBAAoB;QACxB,4BAA4B;YAC1B,OAAO,EAAE;YACT,2BACE;;kCACE,6LAAC;kCACE,EAAE;;;;;;kCAEL,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAI,EAAE;;;;;;0CACP,6LAAC;0CAAI,EAAE;;;;;;0CACP,6LAAC;0CACE,EAAE;;;;;;;;;;;;;;YAKX,mBAAmB,EAAE;YACrB,oBAAoB;YACpB,WAAW;gBACT,MAAM,iBACJ,eACI,sBACD,KAAK,IAAI,CAAC,MAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,EAAE;gBAC7C,uBAAuB;gBACvB,eAAe,MAAM,CAAC;YACxB;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,qBAAqB;QACzB,4BAA4B;YAC1B,OAAO,EAAE;YACT,aACE,EAAE;YACJ,mBAAmB,EAAE;YACrB,oBAAoB;YACpB,WAAW;gBACT,MAAM,iBACJ,eACI,sBACD,KAAK,IAAI,CAAC,MAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,EAAE;gBAC7C,uBAAuB;gBACvB,gBAAgB,MAAM,CAAC;YACzB;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc,eAAe,sBAAsB,IAAI,CAAC,EAAE,EAAE;QAElE,IAAI,aAAa;YACf,mBAAmB;YACnB,kBAAkB;QACpB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,UAAU,OAAO,WAAW,YAAY,CAAC,MAAM,OAAO,CAAC,SAAS;wBAClE,oBAAoB;oBACtB,OAAO;wBACL,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;sCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;gBAC5C,IAAI;oBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACnD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACnD;YACF;QACF;sCAAG;QAAC;KAAiB;IAErB,MAAM,+BAA+B,CAAC;QACpC,oBAAoB;IACtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QAEjB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;YAC5C,MAAM,mBAAmB,CAAC;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkB,EAAE;;;;;;kDAClC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDACJ,aAAa,EAAE;gDACf,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;4CAGhD,+BACC,6LAAC,wIAAA,CAAA,eAAY;gDACX,MAAM;gDACN,cAAc,CAAC,OAAS,kBAAkB;;kEAE1C,6LAAC,wIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;;gEAET,EAAE;gEACF,+BACC,6LAAC,iJAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,iJAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAI/B,6LAAC,wIAAA,CAAA,sBAAmB;wDAClB,OAAM;wDACN,WAAU;kEAET,cACE,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC,uBACJ,6LAAC,wIAAA,CAAA,2BAAwB;gEAEvB,WAAU;gEACV,SAAS,gBAAgB,CAAC,OAAO,EAAE,CAAC,IAAI;gEACxC,iBAAiB,CAAC,QAChB,oBAAoB,CAAC,OAAS,CAAC;4EAC7B,GAAG,IAAI;4EACP,CAAC,OAAO,EAAE,CAAC,EAAE;wEACf,CAAC;0EAGF,OAAO,EAAE;+DAVL,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAmB9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO,EAAE;wCACT,WAAW,CAAC,yDAAyD,EACnE,wBACI,+DACA,cACJ;wCACF,SAAS,wBAAwB,qBAAqB;kDAEtD,cAAA,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCACC,OAAO,EAAE;wCACT,WAAW,CAAC,yDAAyD,EACnE,sBACI,+DACA,cACJ;wCACF,SAAS,sBAAsB,mBAAmB;kDAElD,cAAA,6LAAC,iJAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;wCACC,OAAO,EAAE;wCACT,WAAW,CAAC,yDAAyD,EACnE,mBACI,+DACA,cACJ;wCACF,SAAS,mBAAmB,oBAAoB;kDAEhD,cAAA,6LAAC,iJAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAIlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wIAAA,CAAA,eAAY;4BACX,SAAS;4BACT,MAAM;4BACN,cAAc;4BACd,iBAAiB;4BACjB,aAAa;4BACb,kBAAkB;4BAClB,qBAAqB;4BACrB,sBAAsB;;;;;;;;;;;;;;;;;YAI3B,0CACC,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,yBAAyB;gBACxC,WAAW,yBAAyB,SAAS;gBAC7C,OAAO,yBAAyB,KAAK;gBACrC,aAAa,yBAAyB,WAAW;gBACjD,mBAAmB,yBAAyB,iBAAiB;gBAC7D,oBAAoB,yBAAyB,kBAAkB;;;;;;0BAGnE,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,iBAAiB;gBACjB,SAAS,IAAM,kBAAkB;gBACjC,SAAS;oBACP,kBAAkB;gBACpB;;;;;;;;;;;;AAIR;GA3WM;;QACgB,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QACT,yJAAA,CAAA,wBAAqB;QAEpB,oHAAA,CAAA,UAAO;QA2BA,iLAAA,CAAA,cAAW;QA2CZ,iLAAA,CAAA,cAAW;;;KA5E9B", "debugId": null}}, {"offset": {"line": 1624, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { ProjectListClient } from \"@/components/ProjectListClient\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { Project } from \"@/types\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst fetchProjects = async () => {\r\n  const { data } = await axios.get(`/projects`);\r\n  return data.projects;\r\n};\r\n\r\nexport default function ProjectListPage() {\r\n  // replace with actual user id\r\n  const { user } = useAuth();\r\n  const t = useTranslations();\r\n\r\n  const {\r\n    data: projectsData,\r\n    isLoading: projectsLoading,\r\n    isError: projectsError,\r\n  } = useQuery<Project[]>({\r\n    queryKey: [\"projects\", user?.id],\r\n    queryFn: fetchProjects,\r\n    enabled: !!user?.id,\r\n  });\r\n\r\n  if (projectsLoading || !projectsData) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  if (projectsError) {\r\n    return <p className=\"text-red-500\">{t('error_loading_data')}</p>;\r\n  }\r\n\r\n  return <ProjectListClient data={projectsData} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AACA;;;AATA;;;;;;;AAWA,MAAM,gBAAgB;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;IAC5C,OAAO,KAAK,QAAQ;AACtB;AAEe,SAAS;;IACtB,8BAA8B;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,EACJ,MAAM,YAAY,EAClB,WAAW,eAAe,EAC1B,SAAS,aAAa,EACvB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAa;QACtB,UAAU;YAAC;YAAY,MAAM;SAAG;QAChC,SAAS;QACT,SAAS,CAAC,CAAC,MAAM;IACnB;IAEA,IAAI,mBAAmB,CAAC,cAAc;QACpC,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,IAAI,eAAe;QACjB,qBAAO,6LAAC;YAAE,WAAU;sBAAgB,EAAE;;;;;;IACxC;IAEA,qBAAO,6LAAC,mIAAA,CAAA,oBAAiB;QAAC,MAAM;;;;;;AAClC;GAxBwB;;QAEL,oHAAA,CAAA,UAAO;QACd,yMAAA,CAAA,kBAAe;QAMrB,8KAAA,CAAA,WAAQ;;;KATU", "debugId": null}}]}
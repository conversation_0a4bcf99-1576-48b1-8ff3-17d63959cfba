-- CreateTable
CREATE TABLE "ProjectQuestionOrder" (
    "id" SERIAL NOT NULL,
    "questionId" INTEGER,
    "projectId" INTEGER,
    "groupId" INTEGER,
    "type" TEXT NOT NULL,
    "parentGroupId" INTEGER,
    "position" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProjectQuestionOrder_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProjectQuestionOrder_parentGroupId_position_projectId_key" ON "ProjectQuestionOrder"("parentGroupId", "position", "projectId");

-- AddForeignKey
ALTER TABLE "ProjectQuestionOrder" ADD CONSTRAINT "ProjectQuestionOrder_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectQuestionOrder" ADD CONSTRAINT "ProjectQuestionOrder_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectQuestionOrder" ADD CONSTRAINT "ProjectQuestionOrder_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "QuestionGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProjectQuestionOrder" ADD CONSTRAINT "ProjectQuestionOrder_parentGroupId_fkey" FOREIGN KEY ("parentGroupId") REFERENCES "QuestionGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

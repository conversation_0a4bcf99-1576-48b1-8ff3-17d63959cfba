import { Question } from "@/types/formBuilder";
import React, { useEffect } from "react";
import Modal from "./Modal";
import { ContextType } from "@/types";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useDispatch } from "react-redux";
import { LoadingOverlay } from "../general/LoadingOverlay";
import { TableQuestionBuilder } from "../form-builder/TableQuestionBuilder";
import { fetchTableStructure, TableQuestion } from "@/lib/api/table";
import { useTranslations } from "next-intl";

const EditTableQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  question,
  contextId,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  question: Question;
  contextId: number;
}) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const t = useTranslations();

  // Fetch the table structure (columns and rows)
  const {
    data: rawTableData,
    isLoading,
    error,
  } = useQuery<TableQuestion>({
    queryKey: ["tableQuestion", question.id],
    queryFn: async () => {
      try {
        const data = await fetchTableStructure(question.id);
        return data;
      } catch (err) {
        console.error("Error fetching table data:", err);
        throw err;
      }
    },
    enabled: showModal && question.id > 0 && question.inputType === "table",
  });

  // Process the table data to match the expected format for TableQuestionBuilder
  const tableData = React.useMemo(() => {
    if (!rawTableData) return null;

    // Create a copy of the raw table data and ensure it matches the expected interface
    const processedData = {
      id: rawTableData.id,
      label: rawTableData.label,
      tableColumns: rawTableData.tableColumns.map((col) => ({
        id: col.id,
        columnName: col.columnName,
        parentColumnId: col.parentColumnId,
        childColumns:
          col.childColumns?.map((child) => ({
            id: child.id,
            columnName: child.columnName,
            parentColumnId: child.parentColumnId || col.id, // Ensure parentColumnId is set for child columns
          })) || [],
      })),
      tableRows: rawTableData.tableRows.map((row) => ({
        id: row.id,
        rowsName: row.rowsName,
      })),
    };

    return processedData;
  }, [rawTableData]);

  // Log when the modal is shown or hidden
  useEffect(() => {
    if (showModal) {
    }
  }, [showModal, question]);

  const queryKey =
    contextType === "project"
      ? ["questions"]
      : contextType === "template"
        ? ["templateQuestions"]
        : ["questionBlockQuestions"];

  const handleTableUpdated = (tableId: number) => {

    // Invalidate queries to refresh the data
    queryClient.invalidateQueries({
      queryKey,
      exact: false,
    });

    queryClient.invalidateQueries({
      queryKey: ["tableQuestion", question.id],
      exact: true,
    });

    // Also invalidate form builder data for project contexts
    if (contextType === "project") {
      queryClient.invalidateQueries({ queryKey: ["formBuilderData", contextId] });
    }

    dispatch(
      showNotification({
        message: t('tableQuestionUpdated'),
        type: "success",
      })
    );

    // Add a small delay before closing the modal to ensure state is properly updated
    setTimeout(() => {
      setShowModal(false);
    }, 100);
  };

  if (error) {
    console.error("Error fetching table data:", error);
    dispatch(
      showNotification({
        message: t('tableDataLoadFailed'),
        type: "error",
      })
    );
  }

  // Reference to the TableQuestionBuilder component
  const tableBuilderRef = React.useRef<HTMLDivElement>(null);

  // Function to trigger the submit event on the TableQuestionBuilder
  const handleSaveClick = () => {

    // Try multiple approaches to find the table builder element
    let tableBuilder = null;

    // Approach 1: Direct reference through ref
    if (tableBuilderRef.current) {
      tableBuilder = tableBuilderRef.current;
    }

    // Approach 2: Find by class name in the document
    if (!tableBuilder) {
      const allTableBuilders = document.querySelectorAll(
        ".table-question-builder"
      );

      if (allTableBuilders.length > 0) {
        tableBuilder = allTableBuilders[0];
      }
    }

    // Approach 3: Find by class name in the container
    if (!tableBuilder && tableBuilderRef.current) {
      const containerTableBuilder = tableBuilderRef.current.querySelector(
        ".table-question-builder"
      );
      if (containerTableBuilder) {
        tableBuilder = containerTableBuilder;
      }
    }

    if (tableBuilder) {

      // Create and dispatch the event
      const submitEvent = new CustomEvent("submitTable", {
        bubbles: true, // Allow event to bubble up the DOM tree
        cancelable: true, // Allow event to be canceled
        detail: { timestamp: Date.now() }, // Add some detail to help with debugging
      });

      tableBuilder.dispatchEvent(submitEvent);
    } else {
      console.error(
        "Could not find any table builder element to dispatch event to"
      );

      // As a last resort, try to find any element with a similar class
      const anyElement = document.querySelector("[class*='table']");
      if (anyElement) {

        const submitEvent = new CustomEvent("submitTable", {
          bubbles: true,
          cancelable: true,
          detail: { timestamp: Date.now(), isLastResort: true },
        });

        anyElement.dispatchEvent(submitEvent);
      }
    }
  };

  // Function to handle confirmation before closing
  const handleClose = () => {
    // Show a confirmation dialog if there are unsaved changes
    if (
      window.confirm(
        t('unsavedChangesCloseConfirm')
      )
    ) {
      setShowModal(false);
    }
  };

  return (
    <Modal
      isOpen={showModal}
      onClose={handleClose}
      className="w-11/12 tablet:w-4/5 desktop:w-3/5"
      preventOutsideClick={true}
    >
      {isLoading && <LoadingOverlay />}

      <div className="space-y-4">
        <h1 className="heading-text capitalize mb-4">{t('editTableQuestion')}</h1>

        {tableData ? (
          <div
            ref={tableBuilderRef}
            className="table-question-builder-container"
          >
            <TableQuestionBuilder
              projectId={contextId}
              isInModal={true}
              isEditMode={true}
              existingTableData={tableData as any}
              onTableCreated={handleTableUpdated}
            />

            {/* Add Save button outside the TableQuestionBuilder */}
            <div className="flex items-center justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="btn-outline"
              >
                {t('cancel')}
              </button>
              <button
                type="button"
                onClick={handleSaveClick}
                className="btn-primary"
              >
                {t('saveChanges')}
              </button>
            </div>
          </div>
        ) : !isLoading ? (
          <div className="p-4 text-center">
            <p className="text-red-500">
              {t('tableDataLoadErrorRetry')}
            </p>
          </div>
        ) : null}
      </div>
    </Modal>
  );
};

export { EditTableQuestionModal };
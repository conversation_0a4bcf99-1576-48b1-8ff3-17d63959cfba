{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_c7623d1d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_c7623d1d-module__whmfGG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_c7623d1d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_devanagari_b7a6032d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"noto_sans_devanagari_b7a6032d-module__iJAsDW__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/noto_sans_devanagari_b7a6032d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Noto_Sans_Devanagari%22,%22arguments%22:[{%22weight%22:[%22400%22,%22700%22],%22subsets%22:[%22devanagari%22],%22display%22:%22swap%22}],%22variableName%22:%22devanagari%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Noto Sans Devanagari', 'Noto Sans Devanagari Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,oKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,oKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,oKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReduxProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReduxProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/providers/ReduxProvider.tsx <module evaluation>\",\n    \"ReduxProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReduxProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReduxProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/providers/ReduxProvider.tsx\",\n    \"ReduxProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Notification.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/general/Notification.tsx <module evaluation>\",\n    \"Notification\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qEACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Notification.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Notification = registerClientReference(\n    function() { throw new Error(\"Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/general/Notification.tsx\",\n    \"Notification\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,iDACA", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReactQueryProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReactQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/providers/ReactQueryProvider.tsx <module evaluation>\",\n    \"ReactQueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,kEACA", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/providers/ReactQueryProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ReactQueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/providers/ReactQueryProvider.tsx\",\n    \"ReactQueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8CACA", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport \"./globals.css\";\r\nimport { <PERSON><PERSON><PERSON>, Noto_Sans_Devanagari } from \"next/font/google\";\r\nimport { ReduxProvider } from \"@/providers/ReduxProvider\";\r\nimport { Notification } from \"@/components/general/Notification\";\r\nimport { ReactQueryProvider } from \"@/providers/ReactQueryProvider\";\r\n\r\nconst poppins = Poppins({\r\n  weight: [\"300\", \"400\", \"500\", \"600\"],\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n});\r\n\r\nconst devanagari = Noto_Sans_Devanagari({\r\n  weight: [\"400\", \"700\"],\r\n  subsets: [\"devanagari\"],\r\n  display: \"swap\",\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Data analysis tool\",\r\n  description: \"A tool for data collection and analysis.\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body\r\n        className={`${poppins.className} ${devanagari.className} antialiased`}\r\n      >\r\n        <ReduxProvider>\r\n          <ReactQueryProvider>\r\n            <Notification />\r\n            <main className=\"bg-neutral-200\">{children}</main>\r\n          </ReactQueryProvider>\r\n        </ReduxProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,2IAAA,CAAA,UAAO,CAAC,SAAS,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC;sBAErE,cAAA,8OAAC,2HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,gIAAA,CAAA,qBAAkB;;sCACjB,8OAAC,sIAAA,CAAA,eAAY;;;;;sCACb,8OAAC;4BAAK,WAAU;sCAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
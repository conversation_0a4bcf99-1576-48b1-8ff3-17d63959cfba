{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["// middleware.ts\r\nimport { NextRequest, NextResponse } from \"next/server\";\r\nimport createIntlMiddleware from \"next-intl/middleware\";\r\n\r\n// Create the internationalization middleware with improved configuration\r\nconst intlMiddleware = createIntlMiddleware({\r\n  locales: [\"en\", \"ne\"],\r\n  defaultLocale: \"en\",\r\n  localePrefix: \"as-needed\",\r\n  // Enable locale detection from headers and cookies\r\n  localeDetection: true,\r\n  // Add alternate links for SEO\r\n  alternateLinks: true,\r\n});\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const pathname = request.nextUrl.pathname;\r\n\r\n  // Skip middleware for static assets and API routes\r\n  const shouldSkipMiddleware =\r\n    [\r\n      \"/favicon.ico\",\r\n      \"/images\",\r\n      \"/fonts\",\r\n      \"/api\",\r\n      \"/_next\",\r\n      \"/edit-submission\", // Skip auth for edit-submission routes\r\n      \"/form-submission\", // Skip auth for form-test routes\r\n    ].some((path) => pathname.startsWith(path)) ||\r\n    pathname.match(/\\.(jpg|jpeg|png|gif|svg|css|js|ico|woff|woff2|ttf|eot)$/);\r\n\r\n  if (shouldSkipMiddleware) {\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // Handle internationalization first\r\n  const intlResponse = intlMiddleware(request);\r\n\r\n  // If intl middleware returns a redirect (e.g., adding locale prefix), return it immediately\r\n  if (intlResponse.status !== 200) {\r\n    return intlResponse;\r\n  }\r\n\r\n  // Extract locale from the pathname more reliably\r\n  const localeMatch = pathname.match(/^\\/(en|ne)(?:\\/|$)/);\r\n  const locale = localeMatch ? localeMatch[1] : \"en\"; // Default to 'en' if no locale found\r\n\r\n  // Extract the path without locale prefix for auth checks\r\n  const pathWithoutLocale = pathname.replace(/^\\/(en|ne)/, \"\") || \"/\";\r\n\r\n  // Get token from cookies\r\n  const token = request.cookies.get(\"token\")?.value;\r\n\r\n  // Define auth pages (pages that don't require authentication)\r\n  const authPages = [\r\n    \"/\",\r\n    \"/signup\",\r\n    \"/reset-password\",\r\n    \"/reset-password/change-password\",\r\n  ];\r\n\r\n  const isAuthPage = authPages.includes(pathWithoutLocale);\r\n\r\n  // Special case for form-test sign-in - allow access regardless of auth status\r\n  if (pathname.startsWith(\"/form-submission\") && pathname.includes(\"/sign-in\")) {\r\n    return intlResponse; // Return the intl response to maintain locale handling\r\n  }\r\n\r\n  // Validate token\r\n  let isValidToken = false;\r\n\r\n  if (token) {\r\n    try {\r\n      // Parse JWT token to check expiry\r\n      const tokenData = JSON.parse(atob(token.split(\".\")[1]));\r\n      const expiry = tokenData.exp * 1000; // Convert to milliseconds\r\n      isValidToken = Date.now() < expiry;\r\n    } catch (error) {\r\n      // If token parsing fails, consider it invalid\r\n      console.warn(\"Invalid token format:\", error);\r\n      isValidToken = false;\r\n    }\r\n  }\r\n\r\n  // Skip auth checks for special routes but maintain locale handling\r\n  if (\r\n    pathWithoutLocale.startsWith(\"/form-submission\") ||\r\n    pathWithoutLocale.startsWith(\"/edit-submission\") ||\r\n    pathWithoutLocale.startsWith(\"/test-page\")\r\n  ) {\r\n    return intlResponse;\r\n  }\r\n\r\n  // Redirect authenticated users away from auth pages\r\n  if (isValidToken && isAuthPage) {\r\n    const url = request.nextUrl.clone();\r\n    url.pathname = `/${locale}/dashboard`;\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // Redirect unauthenticated users to login page\r\n  if (!isValidToken && !isAuthPage) {\r\n    const url = request.nextUrl.clone();\r\n    url.pathname = `/${locale}/`;\r\n    return NextResponse.redirect(url);\r\n  }\r\n\r\n  // All checks passed, continue with the intl response\r\n  return intlResponse;\r\n}\r\n\r\nexport const config = {\r\n  // Match all paths except static assets and API routes\r\n  matcher: [\r\n    // Include all paths except static files and API routes\r\n    \"/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)\",\r\n  ],\r\n};\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AAAA;AACA;;;AAEA,yEAAyE;AACzE,MAAM,iBAAiB,CAAA,GAAA,8LAAA,CAAA,UAAoB,AAAD,EAAE;IAC1C,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf,cAAc;IACd,mDAAmD;IACnD,iBAAiB;IACjB,8BAA8B;IAC9B,gBAAgB;AAClB;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,mDAAmD;IACnD,MAAM,uBACJ;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC,CAAC,OAAS,SAAS,UAAU,CAAC,UACrC,SAAS,KAAK,CAAC;IAEjB,IAAI,sBAAsB;QACxB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,oCAAoC;IACpC,MAAM,eAAe,eAAe;IAEpC,4FAA4F;IAC5F,IAAI,aAAa,MAAM,KAAK,KAAK;QAC/B,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,cAAc,SAAS,KAAK,CAAC;IACnC,MAAM,SAAS,cAAc,WAAW,CAAC,EAAE,GAAG,MAAM,qCAAqC;IAEzF,yDAAyD;IACzD,MAAM,oBAAoB,SAAS,OAAO,CAAC,cAAc,OAAO;IAEhE,yBAAyB;IACzB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU;IAE5C,8DAA8D;IAC9D,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,UAAU,QAAQ,CAAC;IAEtC,8EAA8E;IAC9E,IAAI,SAAS,UAAU,CAAC,uBAAuB,SAAS,QAAQ,CAAC,aAAa;QAC5E,OAAO,cAAc,uDAAuD;IAC9E;IAEA,iBAAiB;IACjB,IAAI,eAAe;IAEnB,IAAI,OAAO;QACT,IAAI;YACF,kCAAkC;YAClC,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACrD,MAAM,SAAS,UAAU,GAAG,GAAG,MAAM,0BAA0B;YAC/D,eAAe,KAAK,GAAG,KAAK;QAC9B,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,QAAQ,IAAI,CAAC,yBAAyB;YACtC,eAAe;QACjB;IACF;IAEA,mEAAmE;IACnE,IACE,kBAAkB,UAAU,CAAC,uBAC7B,kBAAkB,UAAU,CAAC,uBAC7B,kBAAkB,UAAU,CAAC,eAC7B;QACA,OAAO;IACT;IAEA,oDAAoD;IACpD,IAAI,gBAAgB,YAAY;QAC9B,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC;QACrC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+CAA+C;IAC/C,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAChC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,sDAAsD;IACtD,SAAS;QACP,uDAAuD;QACvD;KACD;AACH"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/get-messages.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\r\n\r\n// Define supported locales\r\nconst locales = ['en', 'ne'] as const;\r\ntype Locale = typeof locales[number];\r\n\r\n// This function loads messages for a specific locale\r\nexport async function getMessages(locale: string): Promise<Record<string, any>> {\r\n  // Validate locale\r\n  if (!locales.includes(locale as Locale)) {\r\n    console.warn(`Unsupported locale: ${locale}, falling back to 'en'`);\r\n    locale = 'en';\r\n  }\r\n\r\n  try {\r\n    // Dynamic import of the messages file based on locale\r\n    const messages = (await import(`../messages/${locale}.json`)).default;\r\n\r\n    if (!messages || typeof messages !== 'object') {\r\n      throw new Error(`Invalid messages format for locale: ${locale}`);\r\n    }\r\n\r\n    return messages;\r\n  } catch (error) {\r\n    console.error(`Failed to load messages for locale: ${locale}`, error);\r\n\r\n    // Fallback to English if the requested locale fails\r\n    if (locale !== 'en') {\r\n      try {\r\n        console.log('Falling back to English messages');\r\n        return (await import(`../messages/en.json`)).default;\r\n      } catch (fallbackError) {\r\n        console.error('Failed to load fallback English messages', fallbackError);\r\n      }\r\n    }\r\n\r\n    // Return empty object as last resort\r\n    return {};\r\n  }\r\n}\r\n\r\n// Configuration for next-intl\r\nexport default getRequestConfig(async ({ locale }) => {\r\n  // Ensure locale is a valid string\r\n  const localeString = locale?.toString() || 'en';\r\n\r\n  return {\r\n    // The locale must be included in the return object\r\n    locale: localeString,\r\n    // Load messages for the locale\r\n    messages: await getMessages(localeString),\r\n    // Set timezone for date formatting\r\n    timeZone: 'Asia/Kathmandu',\r\n    // Default formats for dates, numbers, etc.\r\n    formats: {\r\n      dateTime: {\r\n        short: {\r\n          day: 'numeric',\r\n          month: 'short',\r\n          year: 'numeric'\r\n        },\r\n        medium: {\r\n          day: 'numeric',\r\n          month: 'long',\r\n          year: 'numeric'\r\n        },\r\n        long: {\r\n          weekday: 'long',\r\n          day: 'numeric',\r\n          month: 'long',\r\n          year: 'numeric'\r\n        }\r\n      },\r\n      number: {\r\n        currency: {\r\n          style: 'currency',\r\n          currency: 'NPR'\r\n        }\r\n      }\r\n    }\r\n  };\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,2BAA2B;AAC3B,MAAM,UAAU;IAAC;IAAM;CAAK;AAIrB,eAAe,YAAY,MAAc;IAC9C,kBAAkB;IAClB,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAmB;QACvC,QAAQ,IAAI,CAAC,CAAC,oBAAoB,EAAE,OAAO,sBAAsB,CAAC;QAClE,SAAS;IACX;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,WAAW,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;QAErE,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;YAC7C,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,QAAQ;QACjE;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,QAAQ,EAAE;QAE/D,oDAAoD;QACpD,IAAI,WAAW,MAAM;YACnB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,OAAO,CAAC,yGAAmC,EAAE,OAAO;YACtD,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;QAEA,qCAAqC;QACrC,OAAO,CAAC;IACV;AACF;uCAGe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,kCAAkC;IAClC,MAAM,eAAe,QAAQ,cAAc;IAE3C,OAAO;QACL,mDAAmD;QACnD,QAAQ;QACR,+BAA+B;QAC/B,UAAU,MAAM,YAAY;QAC5B,mCAAmC;QACnC,UAAU;QACV,2CAA2C;QAC3C,SAAS;YACP,UAAU;gBACR,OAAO;oBACL,KAAK;oBACL,OAAO;oBACP,MAAM;gBACR;gBACA,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;gBACR;gBACA,MAAM;oBACJ,SAAS;oBACT,KAAK;oBACL,OAAO;oBACP,MAAM;gBACR;YACF;YACA,QAAQ;gBACN,UAAU;oBACR,OAAO;oBACP,UAAU;gBACZ;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["\r\nimport { NextIntlClientProvider } from 'next-intl';\r\nimport { notFound } from 'next/navigation';\r\nimport { getMessages } from '@/lib/get-messages';\r\n\r\n// Define supported locales\r\nconst locales = ['en', 'ne'] as const;\r\ntype Locale = typeof locales[number];\r\n\r\nexport function generateStaticParams() {\r\n  return locales.map((locale) => ({ locale }));\r\n}\r\n\r\nexport default async function LocaleLayout({\r\n  children,\r\n  params,\r\n}: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  // Await the params before accessing its properties\r\n  const { locale } = await params;\r\n\r\n  // Validate that the incoming `locale` parameter is valid\r\n  const isValidLocale = locales.includes(locale as Locale);\r\n  if (!isValidLocale) notFound();\r\n\r\n  // Load messages for the locale\r\n  const messages = await getMessages(locale);\r\n\r\n  return (\r\n    <NextIntlClientProvider locale={locale} messages={messages}>\r\n      {children}\r\n    </NextIntlClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;;;;;AAEA,2BAA2B;AAC3B,MAAM,UAAU;IAAC;IAAM;CAAK;AAGrB,SAAS;IACd,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE;QAAO,CAAC;AAC5C;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,mDAAmD;IACnD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,MAAM,gBAAgB,QAAQ,QAAQ,CAAC;IACvC,IAAI,CAAC,eAAe,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAE3B,+BAA+B;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAEnC,qBACE,8OAAC,kQAAA,CAAA,yBAAsB;QAAC,QAAQ;QAAQ,UAAU;kBAC/C;;;;;;AAGP", "debugId": null}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/modals/TableDataViewModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/Modal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/table.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const TableDataViewModal = ({ isOpen, onClose, title, tableData, uniqueColumns, uniqueRows, rowsMap, useParentChildColumns = false, loading = false, tableStructure })=>{
    _s();
    // Parse the submitted table data to get the actual cell values
    const parsedTableData = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "TableDataViewModal.useMemo[parsedTableData]": ()=>{
            if (!tableData || tableData.length === 0) return [];
            // Convert tableData to CellValue format
            const cellValues = [];
            tableData.forEach({
                "TableDataViewModal.useMemo[parsedTableData]": (item)=>{
                    // Try to parse column and row as numbers (IDs)
                    const columnId = parseInt(item.column);
                    const rowId = parseInt(item.row);
                    if (!isNaN(columnId) && !isNaN(rowId)) {
                        cellValues.push({
                            columnId,
                            rowsId: rowId,
                            value: item.value
                        });
                    }
                }
            }["TableDataViewModal.useMemo[parsedTableData]"]);
            return cellValues;
        }
    }["TableDataViewModal.useMemo[parsedTableData]"], [
        tableData
    ]);
    // Group columns by parent-child relationships (same logic as TableInput.tsx)
    const groupedColumns = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "TableDataViewModal.useMemo[groupedColumns]": ()=>{
            if (!tableStructure?.tableColumns || tableStructure.tableColumns.length === 0) {
                return {
                    parentColumns: [],
                    columnMap: new Map(),
                    hasChildColumns: false
                };
            }
            // Get all parent columns (those without a parentColumnId)
            const parentColumns = tableStructure.tableColumns.filter({
                "TableDataViewModal.useMemo[groupedColumns].parentColumns": (col)=>col.parentColumnId === undefined || col.parentColumnId === null
            }["TableDataViewModal.useMemo[groupedColumns].parentColumns"]);
            // Create a map of parent columns to their child columns
            const columnMap = new Map();
            parentColumns.forEach({
                "TableDataViewModal.useMemo[groupedColumns]": (parentCol)=>{
                    // Find all child columns for this parent
                    const childColumns = tableStructure.tableColumns.filter({
                        "TableDataViewModal.useMemo[groupedColumns].childColumns": (col)=>col.parentColumnId === parentCol.id
                    }["TableDataViewModal.useMemo[groupedColumns].childColumns"]);
                    columnMap.set(parentCol.id, childColumns);
                }
            }["TableDataViewModal.useMemo[groupedColumns]"]);
            // Check if any parent has child columns
            const hasChildColumns = parentColumns.some({
                "TableDataViewModal.useMemo[groupedColumns].hasChildColumns": (p)=>(columnMap.get(p.id) || []).length > 0
            }["TableDataViewModal.useMemo[groupedColumns].hasChildColumns"]);
            return {
                parentColumns,
                columnMap,
                hasChildColumns
            };
        }
    }["TableDataViewModal.useMemo[groupedColumns]"], [
        tableStructure
    ]);
    // Create a map for quick cell value lookup
    const cellValueMap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "TableDataViewModal.useMemo[cellValueMap]": ()=>{
            const map = new Map();
            parsedTableData.forEach({
                "TableDataViewModal.useMemo[cellValueMap]": (cell)=>{
                    map.set(`${cell.columnId}_${cell.rowsId}`, cell.value);
                }
            }["TableDataViewModal.useMemo[cellValueMap]"]);
            return map;
        }
    }["TableDataViewModal.useMemo[cellValueMap]"], [
        parsedTableData
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: isOpen,
        onClose: onClose,
        className: "p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "text-xl font-semibold text-neutral-700",
                    children: title
                }, void 0, false, {
                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                    lineNumber: 140,
                    columnNumber: 9
                }, this),
                loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-center p-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"
                        }, void 0, false, {
                            fileName: "[project]/components/modals/TableDataViewModal.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2 text-neutral-600",
                            children: "Loading table data..."
                        }, void 0, false, {
                            fileName: "[project]/components/modals/TableDataViewModal.tsx",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                    lineNumber: 143,
                    columnNumber: 11
                }, this) : !tableStructure?.tableColumns || tableStructure.tableColumns.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "py-4 text-center text-amber-600",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "No table structure available."
                        }, void 0, false, {
                            fileName: "[project]/components/modals/TableDataViewModal.tsx",
                            lineNumber: 150,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm mt-2",
                            children: "Debug info:"
                        }, void 0, false, {
                            fileName: "[project]/components/modals/TableDataViewModal.tsx",
                            lineNumber: 151,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                            className: "text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40",
                            children: JSON.stringify({
                                hasTableStructure: !!tableStructure,
                                tableColumnsLength: tableStructure?.tableColumns?.length || 0,
                                tableRowsLength: tableStructure?.tableRows?.length || 0,
                                tableDataLength: tableData?.length || 0,
                                useParentChildColumns
                            }, null, 2)
                        }, void 0, false, {
                            fileName: "[project]/components/modals/TableDataViewModal.tsx",
                            lineNumber: 152,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                    lineNumber: 149,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "overflow-auto max-h-[70vh]",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
                        className: "border-collapse border border-amber-700",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                                className: "bg-amber-100",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                className: "px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100",
                                                rowSpan: groupedColumns.hasChildColumns ? 2 : 1,
                                                children: title
                                            }, void 0, false, {
                                                fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                lineNumber: 172,
                                                columnNumber: 19
                                            }, this),
                                            groupedColumns.parentColumns.map((parentCol)=>{
                                                const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                                // If this parent has children, it spans multiple columns
                                                const colSpan = childColumns.length || 1;
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    colSpan: colSpan,
                                                    className: "px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100",
                                                    children: parentCol.columnName
                                                }, parentCol.id, false, {
                                                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 23
                                                }, this);
                                            })
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                        lineNumber: 171,
                                        columnNumber: 17
                                    }, this),
                                    groupedColumns.hasChildColumns && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: groupedColumns.parentColumns.map((parentCol)=>{
                                            const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                            // If this parent has no children, render a placeholder
                                            if (childColumns.length === 0) {
                                                return null; // Don't render anything for parents without children
                                            }
                                            // Otherwise, render each child column
                                            return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50",
                                                    children: childCol.columnName
                                                }, childCol.id, false, {
                                                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                    lineNumber: 210,
                                                    columnNumber: 25
                                                }, this));
                                        })
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                        lineNumber: 198,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                lineNumber: 169,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                                children: tableStructure.tableRows?.map((row, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        className: "bg-white",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                className: "px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50",
                                                children: row.rowsName
                                            }, void 0, false, {
                                                fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                lineNumber: 225,
                                                columnNumber: 21
                                            }, this),
                                            groupedColumns.parentColumns.map((parentCol)=>{
                                                const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                                // If this parent has no children, render a single cell
                                                if (childColumns.length === 0) {
                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        className: "px-3 py-2 text-xs border border-amber-700",
                                                        children: cellValueMap.get(`${parentCol.id}_${row.id}`) || ""
                                                    }, `cell-${parentCol.id}-${row.id}`, false, {
                                                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                        lineNumber: 237,
                                                        columnNumber: 27
                                                    }, this);
                                                }
                                                // Otherwise, render cells for each child column
                                                return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        className: "px-3 py-2 text-xs border border-amber-700",
                                                        children: cellValueMap.get(`${childCol.id}_${row.id}`) || ""
                                                    }, `cell-${childCol.id}-${row.id}`, false, {
                                                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                                        lineNumber: 249,
                                                        columnNumber: 25
                                                    }, this));
                                            })
                                        ]
                                    }, row.id, true, {
                                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                        lineNumber: 224,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/components/modals/TableDataViewModal.tsx",
                                lineNumber: 222,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                        lineNumber: 168,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                    lineNumber: 167,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-end mt-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: onClose,
                        className: "px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors",
                        children: "Close"
                    }, void 0, false, {
                        fileName: "[project]/components/modals/TableDataViewModal.tsx",
                        lineNumber: 265,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/modals/TableDataViewModal.tsx",
                    lineNumber: 264,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/modals/TableDataViewModal.tsx",
            lineNumber: 139,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/modals/TableDataViewModal.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
};
_s(TableDataViewModal, "xwbv2ikfZL8N8EArhdA4b+LXb7c=");
_c = TableDataViewModal;
const __TURBOPACK__default__export__ = TableDataViewModal;
var _c;
__turbopack_context__.k.register(_c, "TableDataViewModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateColumns": (()=>generateColumns)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/checkbox.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-up-down.js [app-client] (ecmascript) <export default as ArrowUpDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$TableDataViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/TableDataViewModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bi/index.mjs [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
// Function to format cell value based on data type
const formatCellValue = (value, type)=>{
    if (value === null || value === undefined) return "-";
    if (typeof value === "boolean") {
        return value ? "Yes" : "No";
    }
    if (value instanceof Date) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDate"])(value);
    }
    if (type === "date" && typeof value === "string") {
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDate"])(new Date(value));
        } catch  {
            return value;
        }
    }
    return String(value);
};
// Function to fetch table structure (columns and rows) from the backend
const fetchTableStructure = async (questionId)=>{
    try {
        let tableData = null;
        // Use the table-questions endpoint (the working one)
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/table-questions/${questionId}`);
            if (data && data.success && data.data) {
                // The response structure is { success: true, data: { question: {...}, cellValues: {...} } }
                tableData = data.data.question;
            } else {}
        } catch (err) {
            console.error("Error fetching from /table-questions/ endpoint:", err);
        }
        // If we still don't have tableRows, try to fetch them separately
        if (tableData && tableData.tableColumns && !tableData.tableRows) {
            try {
                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/table-rows/${questionId}`);
                if (data && data.data && data.data.tableRows) {
                    tableData.tableRows = data.data.tableRows;
                }
            } catch (err) {
                console.error("Error fetching table rows separately:", err);
            }
        }
        if (!tableData) {
            // Return a default structure instead of null to prevent errors
            return {
                id: questionId,
                label: "Table Data",
                tableColumns: [],
                tableRows: [
                    {
                        id: 1,
                        rowsName: "Row 1"
                    },
                    {
                        id: 2,
                        rowsName: "Row 2"
                    },
                    {
                        id: 3,
                        rowsName: "Row 3"
                    }
                ]
            };
        }
        // Ensure tableColumns exists and flatten nested structure
        if (!tableData.tableColumns || !Array.isArray(tableData.tableColumns)) {
            console.error("tableColumns is missing or not an array, creating default tableColumns");
            tableData.tableColumns = [];
        } else {
            // Flatten the nested structure to include child columns
            const flattenedColumns = [];
            tableData.tableColumns.forEach((column)=>{
                // Add the parent column
                flattenedColumns.push({
                    id: column.id,
                    columnName: column.columnName,
                    parentColumnId: column.parentColumnId || null
                });
                // Add child columns if they exist
                if (column.childColumns && Array.isArray(column.childColumns)) {
                    column.childColumns.forEach((childColumn)=>{
                        flattenedColumns.push({
                            id: childColumn.id,
                            columnName: childColumn.columnName,
                            parentColumnId: childColumn.parentColumnId || column.id
                        });
                    });
                }
            });
            tableData.tableColumns = flattenedColumns;
        }
        // Ensure tableRows exists
        if (!tableData.tableRows || !Array.isArray(tableData.tableRows)) {
            console.error("tableRows is missing or not an array, creating default tableRows");
            // Create dummy tableRows if none exist and tableColumns exists
            if (tableData.tableColumns && tableData.tableColumns.length > 0) {
                tableData.tableRows = tableData.tableColumns.map((col)=>({
                        id: col.id,
                        rowsName: `Row ${col.id}`
                    }));
            } else {
                // If tableColumns doesn't exist or is empty, create a default tableRows array
                tableData.tableRows = [
                    {
                        id: 1,
                        rowsName: "Row 1"
                    },
                    {
                        id: 2,
                        rowsName: "Row 2"
                    },
                    {
                        id: 3,
                        rowsName: "Row 3"
                    }
                ];
            }
        }
        return tableData;
    } catch (error) {
        console.error("Error fetching table structure:", error);
        // Return a default structure instead of null to prevent errors
        const defaultStructure = {
            id: questionId,
            label: "Table Data",
            tableColumns: [],
            tableRows: [
                {
                    id: 1,
                    rowsName: "Row 1"
                },
                {
                    id: 2,
                    rowsName: "Row 2"
                },
                {
                    id: 3,
                    rowsName: "Row 3"
                }
            ]
        };
        return defaultStructure;
    }
};
const generateColumns = (onViewSubmission, Submission, hashedId, allQuestions // All questions from the project including conditional ones
)=>{
    // Base column for ID or index
    const baseColumns = [
        {
            id: "select",
            header: ({ table })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Checkbox"], {
                    className: "w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer",
                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && "indeterminate",
                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),
                    "aria-label": "Select all"
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this),
            cell: ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Checkbox"], {
                    className: "w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer",
                    checked: row.getIsSelected(),
                    onCheckedChange: (value)=>row.toggleSelected(!!value),
                    "aria-label": "Select row"
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this),
            enableHiding: false
        },
        {
            id: "id",
            header: ({ column })=>{
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center hover:text-neutral-300",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: "ID"
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 213,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2 flex-shrink-0 w-4 h-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpDown$3e$__["ArrowUpDown"], {
                                className: "w-full h-full",
                                onClick: ()=>column.toggleSorting(column.getIsSorted() === "asc")
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                lineNumber: 215,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 214,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 212,
                    columnNumber: 11
                }, this);
            },
            accessorFn: (_, rowIndex)=>rowIndex + 1,
            enableSorting: true,
            cell: ({ row })=>{
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-8 font-medium text-neutral-700",
                    children: [
                        row.index + 1,
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                    onClick: ()=>onViewSubmission(row.original),
                                    className: "w-4 h-4 cursor-pointer hover:text-primary-500"
                                }, void 0, false, {
                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                    lineNumber: 232,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BiSolidEdit"], {
                                    className: "w-4 h-4 cursor-pointer hover:text-primary-500",
                                    title: "Edit",
                                    onClick: ()=>{
                                        const submissionId = row.original.id;
                                        if (!submissionId || !hashedId) return;
                                        window.open(`/edit-submission/${hashedId}/${submissionId}`, "_blank");
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                    lineNumber: 237,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 231,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 229,
                    columnNumber: 11
                }, this);
            }
        },
        {
            id: "validation",
            header: "Validation",
            accessorKey: "validation"
        }
    ];
    // Use allQuestions if provided, otherwise fall back to questions from submissions
    let questionsToUse = [];
    if (allQuestions && allQuestions.length > 0) {
        // Use ALL questions from the project (including conditional ones)
        questionsToUse = allQuestions;
    } else if (Submission && Submission.answers?.length) {
        // Fallback: use unique questions from submissions (old behavior)
        questionsToUse = Array.from(new Map(Submission.answers.map((a)=>[
                a.question.id,
                a.question
            ])).values());
    } else {
        // No questions available
        return baseColumns;
    }
    // Generate dynamic question columns
    const questionColumns = questionsToUse.map((question)=>({
            id: `${question.label}`,
            header: ({ column })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: question.label
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 285,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpDown$3e$__["ArrowUpDown"], {
                            className: "ml-1 h-4 w-4 cursor-pointer opacity-60",
                            onClick: ()=>column.toggleSorting(column.getIsSorted() === "asc")
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 286,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 284,
                    columnNumber: 9
                }, this),
            accessorFn: (row)=>{
                // Filter answers that match the question.id
                const matches = row.answers.filter((a)=>a.question?.id === question.id);
                // If no matches found, this question wasn't answered (likely due to conditional logic)
                if (matches.length === 0) {
                    return null; // Will be displayed as "-" or "N/A"
                }
                // Handle selectmany vs normal input
                if (question.inputType === "selectmany") {
                    // For selectmany, each selected option creates a separate answer record
                    // We need to collect all the values and join them properly
                    const values = matches.map((m)=>m.value).filter((v)=>v && String(v).trim() !== "") // Filter out empty values
                    .sort(); // Sort for consistent display
                    return values.length > 0 ? values.join(", ") : null;
                }
                // For selectone and other input types, return the first (and should be only) value
                return matches[0]?.value ?? null;
            },
            cell: ({ getValue })=>{
                const value = getValue();
                // Handle table input type differently
                if (question.inputType === "table") {
                    try {
                        // First, ensure we're working with a string
                        let valueStr = typeof value === "string" ? value : String(value);
                        // Check if the value is already a JSON string
                        let tableData;
                        // Try to parse if it looks like JSON
                        if (valueStr.startsWith("[") && valueStr.includes("{")) {
                            try {
                                tableData = JSON.parse(valueStr);
                            } catch (parseError) {
                                console.error("Failed to parse JSON string:", parseError);
                                // If it's a malformed JSON string, try to clean it up
                                // This handles cases where the JSON might have extra quotes or escaping
                                valueStr = valueStr.replace(/\\"/g, '"').replace(/^"/, "").replace(/"$/, "");
                                try {
                                    tableData = JSON.parse(valueStr);
                                } catch (secondParseError) {
                                    console.error("Failed second parse attempt:", secondParseError);
                                    // If it still fails, try to extract the array from the string
                                    const match = valueStr.match(/\[([\s\S]*)\]/);
                                    if (match && match[0]) {
                                        try {
                                            tableData = JSON.parse(match[0]);
                                        } catch (thirdParseError) {
                                            console.error("Failed third parse attempt:", thirdParseError);
                                            // Special handling for the format in the screenshot
                                            // Format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...
                                            try {
                                                // Convert single quotes to double quotes for valid JSON
                                                const fixedStr = match[0].replace(/'/g, '"');
                                                tableData = JSON.parse(fixedStr);
                                            } catch (fourthParseError) {
                                                console.error("Failed fourth parse attempt:", fourthParseError);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // If we still don't have valid data, try to parse it as a custom format
                        if (!tableData && valueStr.includes("columnId") && valueStr.includes("rowsId")) {
                            try {
                                // Handle the format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...]
                                // Convert to valid JSON by replacing single quotes with double quotes
                                const fixedStr = valueStr.replace(/'/g, '"');
                                tableData = JSON.parse(fixedStr);
                            } catch (customFormatError) {
                                console.error("Failed custom format parsing:", customFormatError);
                            }
                        }
                        // If we have valid array data, display it as a table
                        if (Array.isArray(tableData)) {
                            var _s = __turbopack_context__.k.signature();
                            // Process the table data to extract column and row information
                            const processedData = tableData.map((item)=>{
                                // Get column name - use the actual column name if available
                                let columnName = "";
                                if (item.columnName) {
                                    // If columnName is directly available in the data, use it
                                    columnName = item.columnName;
                                } else if (item.column && item.column.columnName) {
                                    // If the column object is available with columnName property
                                    columnName = item.column.columnName;
                                } else if (item.columnId) {
                                    // If we only have columnId, check if we have a name for it
                                    // First, try to get the name from the item itself if it has a name property
                                    if (item.name) {
                                        columnName = item.name;
                                    } else if (item.label) {
                                        columnName = item.label;
                                    } else {
                                        // For now, store the columnId but we'll replace it with the actual name later
                                        // We'll use the columnId as a key to look up the actual name when we fetch the table structure
                                        columnName = String(item.columnId);
                                    }
                                }
                                // Get row name - use the actual row name if available
                                let rowName = "";
                                if (item.rowsName) {
                                    // If rowsName is directly available in the data, use it
                                    rowName = item.rowsName;
                                } else if (item.row && item.row.rowsName) {
                                    // If the row object is available with rowsName property
                                    rowName = item.row.rowsName;
                                } else if (item.rowsId) {
                                    // If we only have rowsId, check if we have a name for it
                                    // First, try to get the name from the item itself if it has a name property
                                    if (item.name) {
                                        rowName = item.name;
                                    } else if (item.label) {
                                        rowName = item.label;
                                    } else {
                                        // Store the rowsId as a key to look up the actual name later
                                        // We'll replace it with the actual name when we fetch the table structure
                                        rowName = String(item.rowsId);
                                    }
                                }
                                // Extract the value
                                const cellValue = item.value !== undefined ? item.value : "";
                                return {
                                    column: columnName,
                                    row: rowName,
                                    value: cellValue
                                };
                            });
                            // Create a structured table with proper headers in the format of the second image
                            // Group data by rows and columns to create a matrix
                            const rowsMap = new Map();
                            const columnsSet = new Set();
                            // First pass: collect all unique rows and columns
                            processedData.forEach((item)=>{
                                columnsSet.add(String(item.column));
                                if (!rowsMap.has(String(item.row))) {
                                    rowsMap.set(String(item.row), new Map());
                                }
                                rowsMap.get(String(item.row))?.set(String(item.column), String(item.value));
                            });
                            // Convert to arrays for rendering
                            const uniqueColumns = Array.from(columnsSet);
                            const uniqueRows = Array.from(rowsMap.keys());
                            // Check if we should use the parent-child column structure
                            // This should be determined by the table structure from the backend
                            // Set to true to use the parent-child column structure
                            const useParentChildColumns = true;
                            // Create a TableCellWithModal component to handle state
                            const TableCellWithModal = ()=>{
                                _s();
                                const [showModal, setShowModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
                                const [tableStructure, setTableStructure] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
                                const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
                                // Debug effect to track tableStructure changes
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
                                    "generateColumns.questionColumns.TableCellWithModal.useEffect": ()=>{}
                                }["generateColumns.questionColumns.TableCellWithModal.useEffect"], [
                                    tableStructure
                                ]);
                                // Function to fetch table structure when the modal is opened
                                const fetchTableData = async ()=>{
                                    if (!question.id) {
                                        console.error("No question ID available");
                                        return;
                                    }
                                    setLoading(true);
                                    try {
                                        const structure = await fetchTableStructure(question.id);
                                        if (structure) {
                                            // Make sure tableRows is included in the structure
                                            if (!structure.tableRows) {
                                                console.error("tableRows is missing from the structure!");
                                            }
                                            setTableStructure(structure);
                                            // Update the processed data with actual column and row names
                                            if (structure.tableColumns && structure.tableRows) {
                                                // Create maps for quick lookup
                                                const columnMap = new Map();
                                                const rowMap = new Map();
                                                structure.tableColumns.forEach((col)=>{
                                                    columnMap.set(col.id, col.columnName);
                                                    // Also map the string version of the ID
                                                    columnMap.set(String(col.id), col.columnName);
                                                });
                                                structure.tableRows.forEach((row)=>{
                                                    rowMap.set(row.id, row.rowsName);
                                                    // Also map the string version of the ID
                                                    rowMap.set(String(row.id), row.rowsName);
                                                });
                                                // Update the processed data
                                                processedData.forEach((item)=>{
                                                    if (item.column) {
                                                        // Check if the column is a number (ID) or already a name
                                                        if (!isNaN(Number(item.column))) {
                                                            const columnId = item.column;
                                                            if (columnMap.has(columnId)) {
                                                                const oldColumn = item.column;
                                                                item.column = columnMap.get(columnId);
                                                            } else {
                                                                // If we don't have a mapping for this column ID, try to find a column with this ID
                                                                const matchingColumn = structure.tableColumns.find((col)=>String(col.id) === String(columnId));
                                                                if (matchingColumn) {
                                                                    const oldColumn = item.column;
                                                                    item.column = matchingColumn.columnName;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    if (item.row) {
                                                        // Check if the row is a number (ID) or already a name
                                                        if (!isNaN(Number(item.row))) {
                                                            const rowId = item.row;
                                                            if (rowMap.has(rowId)) {
                                                                const oldRow = item.row;
                                                                item.row = rowMap.get(rowId);
                                                            } else {
                                                                // If we don't have a mapping for this row ID, try to find a row with this ID
                                                                const matchingRow = structure.tableRows.find((row)=>String(row.id) === String(rowId));
                                                                if (matchingRow) {
                                                                    const oldRow = item.row;
                                                                    item.row = matchingRow.rowsName;
                                                                }
                                                            }
                                                        }
                                                    }
                                                });
                                                // Rebuild the rowsMap with the updated column and row names
                                                const newRowsMap = new Map();
                                                const newColumnsSet = new Set();
                                                // First pass: collect all unique rows and columns with their updated names
                                                processedData.forEach((item)=>{
                                                    newColumnsSet.add(String(item.column));
                                                    if (!newRowsMap.has(String(item.row))) {
                                                        newRowsMap.set(String(item.row), new Map());
                                                    }
                                                    newRowsMap.get(String(item.row))?.set(String(item.column), String(item.value));
                                                });
                                                // Update the original arrays with the transformed data
                                                uniqueColumns.length = 0;
                                                uniqueRows.length = 0;
                                                // Add the updated column and row names
                                                newColumnsSet.forEach((col)=>uniqueColumns.push(col));
                                                newRowsMap.forEach((_, row)=>uniqueRows.push(row));
                                                // Clear the existing rowsMap and copy entries from newRowsMap
                                                rowsMap.clear();
                                                newRowsMap.forEach((valueMap, key)=>{
                                                    rowsMap.set(key, valueMap);
                                                });
                                            // Log the final rowsMap for debugging
                                            }
                                        }
                                    } catch (error) {
                                        console.error("Error fetching table structure:", error);
                                    } finally{
                                        setLoading(false);
                                    }
                                };
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-medium text-neutral-700",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            onClick: async (e)=>{
                                                e.preventDefault();
                                                setLoading(true);
                                                setShowModal(true);
                                                await fetchTableData(); // Wait for table structure to load
                                            },
                                            className: "inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                    size: 12,
                                                    className: "inline"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                                    lineNumber: 633,
                                                    columnNumber: 23
                                                }, this),
                                                " Click to view table"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                            lineNumber: 623,
                                            columnNumber: 21
                                        }, this),
                                        (()=>{
                                            return null;
                                        })(),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$TableDataViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            isOpen: showModal,
                                            onClose: ()=>setShowModal(false),
                                            title: question.label || "Table Data",
                                            tableData: processedData,
                                            uniqueColumns: uniqueColumns,
                                            uniqueRows: uniqueRows,
                                            rowsMap: rowsMap,
                                            useParentChildColumns: useParentChildColumns,
                                            loading: loading,
                                            tableStructure: tableStructure
                                        }, void 0, false, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                            lineNumber: 640,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                    lineNumber: 622,
                                    columnNumber: 19
                                }, this);
                            };
                            _s(TableCellWithModal, "9Xw5cgyLk0Cg7YDYkAP6u9Z02jE=");
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TableCellWithModal, {}, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                lineNumber: 656,
                                columnNumber: 22
                            }, this);
                        }
                    } catch (e) {
                        // If parsing fails, log the error and the value that caused it
                        console.error("Error parsing table data:", e, "Value:", value);
                    }
                }
                // Default rendering for non-table data or if table parsing failed
                // Handle null/undefined values for conditional questions
                if (value === null || value === undefined || value === "") {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "font-medium text-neutral-400 italic",
                        children: "-"
                    }, void 0, false, {
                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                        lineNumber: 667,
                        columnNumber: 18
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "font-medium text-neutral-700",
                    children: String(value)
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 671,
                    columnNumber: 11
                }, this);
            },
            enableSorting: true
        }));
    // Optional metadata columns
    const rearColumns = [
        {
            id: "submissionTime",
            header: ({ column })=>{
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4 hover:text-neutral-300",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: "Submission Time"
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 685,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2 flex-shrink-0 w-4 h-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpDown$3e$__["ArrowUpDown"], {
                                className: "w-full h-full",
                                onClick: ()=>column.toggleSorting(column.getIsSorted() === "asc")
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                lineNumber: 687,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 686,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 684,
                    columnNumber: 11
                }, this);
            },
            accessorKey: "submissionTime",
            cell: ({ getValue })=>{
                const value = getValue();
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "font-medium text-neutral-700",
                    children: formatCellValue(value, "date") || "Not recorded"
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 701,
                    columnNumber: 11
                }, this);
            },
            enableSorting: true
        },
        {
            id: "submittedBy",
            header: ({ column })=>{
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4 hover:text-neutral-300",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: "Submitted By"
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 713,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2 flex-shrink-0 w-4 h-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$up$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowUpDown$3e$__["ArrowUpDown"], {
                                className: "w-full h-full",
                                onClick: ()=>column.toggleSorting(column.getIsSorted() === "asc")
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                                lineNumber: 715,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                            lineNumber: 714,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 712,
                    columnNumber: 11
                }, this);
            },
            accessorKey: "submittedBy",
            accessorFn: (row)=>row.user?.name || "Anonymous",
            cell: ({ getValue })=>{
                const value = getValue();
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "font-medium text-neutral-700",
                    children: value
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx",
                    lineNumber: 729,
                    columnNumber: 16
                }, this);
            },
            enableSorting: true
        }
    ];
    return [
        ...baseColumns,
        ...questionColumns,
        ...rearColumns
    ];
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/submission.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteFormSubmission": (()=>deleteFormSubmission),
    "deleteMultipleFormSubmissions": (()=>deleteMultipleFormSubmissions),
    "updateAnswer": (()=>updateAnswer),
    "updateMultipleAnswers": (()=>updateMultipleAnswers),
    "updateMultipleAnswersWithEndpoint": (()=>updateMultipleAnswersWithEndpoint)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
// Delete form submission
const deleteFormSubmission = async (submissionId, projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/form-submissions/${submissionId}?projectId=${projectId}`);
        return data;
    } catch (error) {
        console.error("Error deleting form submission:", error);
        throw error;
    }
};
// Delete multiple form submissions
const deleteMultipleFormSubmissions = async (submissionIds, projectId)=>{
    try {
        const deletePromises = submissionIds.map((id)=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/form-submissions/${id}?projectId=${projectId}`));
        const results = await Promise.all(deletePromises);
        return results.map((result)=>result.data);
    } catch (error) {
        console.error("Error deleting multiple form submissions:", error);
        throw error;
    }
};
// Update a single answer
const updateAnswer = async (answerData, projectId)=>{
    try {
        if (!answerData.submissionId || !answerData.questionId) {
            throw new Error("submissionId and questionId are required");
        }
        const formattedData = {
            ...answerData
        };
        if (formattedData.questionOptionId === null) {
            delete formattedData.questionOptionId;
        } else if (Array.isArray(formattedData.questionOptionId)) {
            formattedData.questionOptionId = formattedData.questionOptionId.filter((id)=>id != null);
            if (formattedData.questionOptionId.length === 0) {
                delete formattedData.questionOptionId;
            }
        }
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/${answerData.questionId}?projectId=${projectId}`, formattedData);
        return data;
    } catch (error) {
        console.error("Error updating answer:", error);
        throw error;
    }
};
// Update multiple answers for a single question (for select_many type)
const updateMultipleAnswers = async (answerData)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/${answerData.questionId}`, {
            ...answerData,
            answerType: "selectmany"
        });
        return data;
    } catch (error) {
        console.error("Error updating multiple answers:", error);
        throw error;
    }
};
// Update multiple answers using the /answers/multiple endpoint
const updateMultipleAnswersWithEndpoint = async (answers, projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/multiple?projectId=${projectId}`, answers);
        return data;
    } catch (error) {
        console.error("Error updating multiple answers with endpoint:", error);
        throw error;
    }
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/modals/EditSubmissionModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EditSubmissionModal": (()=>EditSubmissionModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/Modal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/table.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-table/build/lib/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/table-core/build/lib/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/redux/slices/notificationSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/submission.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
// Transform submission data to a simpler format for the table
const transformSubmissionData = (submission)=>{
    if (!submission || !submission.answers) return [];
    // Create a map to group answers by question
    const questionMap = new Map();
    submission.answers.forEach((answer)=>{
        const questionId = answer.question?.id;
        const questionLabel = answer.question?.label || 'unknown';
        if (!questionId) return;
        if (!questionMap.has(questionId)) {
            questionMap.set(questionId, {
                type: answer.question?.inputType || "text",
                question: questionLabel,
                questionObject: answer.question,
                answers: [
                    answer.value
                ],
                originalData: [
                    answer
                ]
            });
        } else {
            const existingEntry = questionMap.get(questionId);
            existingEntry.answers.push(answer.value);
            existingEntry.originalData.push(answer);
        }
    });
    // Convert map to array
    return Array.from(questionMap.values());
};
const EditSubmissionModal = ({ showModal, onClose, onConfirm, submission, isMultipleSelection = false, selectedSubmissions = [], projectId })=>{
    _s();
    const [isEditing, setIsEditing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editingData, setEditingData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [newResponse, setNewResponse] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [selectedSubmissionId, setSelectedSubmissionId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Add dispatch for notifications
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    // Create refs for the input elements
    const questionInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const answerInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Separate state for filtering
    const [questionFilter, setQuestionFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [answerFilter, setAnswerFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // Transform the submission data for display
    const data = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "EditSubmissionModal.useMemo[data]": ()=>{
            if (isMultipleSelection && selectedSubmissionId) {
                // Find the selected submission from the array of selected submissions
                const selectedSubmission = selectedSubmissions.find({
                    "EditSubmissionModal.useMemo[data].selectedSubmission": (submission)=>submission.id === selectedSubmissionId
                }["EditSubmissionModal.useMemo[data].selectedSubmission"]);
                return selectedSubmission ? transformSubmissionData(selectedSubmission) : [];
            }
            return transformSubmissionData(submission);
        }
    }["EditSubmissionModal.useMemo[data]"], [
        submission,
        isMultipleSelection,
        selectedSubmissionId,
        selectedSubmissions
    ]);
    // Set default selected submission when modal opens
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditSubmissionModal.useEffect": ()=>{
            if (isMultipleSelection && selectedSubmissions.length > 0 && !selectedSubmissionId) {
                const firstId = selectedSubmissions[0]?.id;
                if (firstId !== undefined) {
                    setSelectedSubmissionId(firstId);
                }
            }
        }
    }["EditSubmissionModal.useEffect"], [
        isMultipleSelection,
        selectedSubmissions,
        selectedSubmissionId
    ]);
    // Filter the data directly
    const filteredData = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "EditSubmissionModal.useMemo[filteredData]": ()=>{
            return data.filter({
                "EditSubmissionModal.useMemo[filteredData]": (item)=>{
                    const questionMatches = item.question.toLowerCase().includes(questionFilter.toLowerCase()) || !questionFilter;
                    const answerMatches = String(item.answers).toLowerCase().includes(answerFilter.toLowerCase()) || !answerFilter;
                    return questionMatches && answerMatches;
                }
            }["EditSubmissionModal.useMemo[filteredData]"]);
        }
    }["EditSubmissionModal.useMemo[filteredData]"], [
        data,
        questionFilter,
        answerFilter
    ]);
    // Handle submission selection change
    const handleSubmissionChange = (event)=>{
        setSelectedSubmissionId(Number(event.target.value));
    };
    const table = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"])({
        data: filteredData,
        columns: [
            {
                accessorKey: "type",
                header: t('type')
            },
            {
                accessorKey: "question",
                header: {
                    "EditSubmissionModal.useReactTable[table]": ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: "Question"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 166,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                    ref: questionInputRef,
                                    placeholder: t('searchQuestions'),
                                    value: questionFilter,
                                    onChange: {
                                        "EditSubmissionModal.useReactTable[table]": (e)=>setQuestionFilter(e.target.value)
                                    }["EditSubmissionModal.useReactTable[table]"],
                                    className: "bg-neutral-100 text-neutral-700 mt-2 h-8"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 167,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this)
                }["EditSubmissionModal.useReactTable[table]"]
            },
            {
                accessorKey: "answers",
                header: {
                    "EditSubmissionModal.useReactTable[table]": ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: "Answer"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 181,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                    ref: answerInputRef,
                                    placeholder: t('searchAnswers'),
                                    value: answerFilter,
                                    onChange: {
                                        "EditSubmissionModal.useReactTable[table]": (e)=>setAnswerFilter(e.target.value)
                                    }["EditSubmissionModal.useReactTable[table]"],
                                    className: "bg-neutral-100 text-neutral-700 mt-2 h-8"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 182,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                            lineNumber: 180,
                            columnNumber: 11
                        }, this)
                }["EditSubmissionModal.useReactTable[table]"],
                cell: {
                    "EditSubmissionModal.useReactTable[table]": ({ row })=>{
                        const answers = row.original.answers;
                        // If multiple submissions are selected, show special text
                        if (isMultipleSelection) {
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-neutral-800 italic",
                                    children: t('multipleResponses')
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 198,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 197,
                                columnNumber: 15
                            }, this);
                        }
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col",
                            children: answers.map({
                                "EditSubmissionModal.useReactTable[table]": (answer, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: index > 0 ? "" : "",
                                        children: String(answer)
                                    }, index, false, {
                                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                        lineNumber: 208,
                                        columnNumber: 17
                                    }, this)
                            }["EditSubmissionModal.useReactTable[table]"])
                        }, void 0, false, {
                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                            lineNumber: 206,
                            columnNumber: 13
                        }, this);
                    }
                }["EditSubmissionModal.useReactTable[table]"]
            },
            {
                accessorKey: "action",
                header: t('action'),
                cell: {
                    "EditSubmissionModal.useReactTable[table]": ({ row })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "btn-primary",
                            onClick: {
                                "EditSubmissionModal.useReactTable[table]": ()=>handleEdit(row.original)
                            }["EditSubmissionModal.useReactTable[table]"],
                            children: t('edit')
                        }, void 0, false, {
                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this)
                }["EditSubmissionModal.useReactTable[table]"]
            }
        ],
        getCoreRowModel: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$table$2d$core$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCoreRowModel"])()
    });
    const handleEdit = (rowData)=>{
        setIsEditing(true);
        setEditingData(rowData);
        // Join multiple answers with line breaks for editing
        setNewResponse(rowData.answers.join("\n"));
    };
    // Define mutation for updating answers
    const updateAnswerMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "EditSubmissionModal.useMutation[updateAnswerMutation]": (data)=>{
                // Make sure the questionId is valid
                if (!data.questionId) {
                    throw new Error(t('questionIdRequired'));
                }
                // Make sure the submissionId is valid
                if (!data.submissionId) {
                    throw new Error(t('submissionIdRequired'));
                }
                // Set up the payload according to backend expectations
                const payload = {
                    submissionId: data.submissionId,
                    questionId: data.questionId,
                    answerType: data.answerType,
                    value: data.value
                };
                // Handle questionOptionId correctly based on answerType
                if (data.answerType === "selectmany") {
                    // For selectmany, questionOptionId must be an array
                    payload.questionOptionId = Array.isArray(data.questionOptionId) ? data.questionOptionId : data.questionOptionId ? [
                        data.questionOptionId
                    ] : []; // Convert to array if not already
                } else if (data.questionOptionId !== undefined) {
                    // For other types, questionOptionId must NOT be an array
                    payload.questionOptionId = Array.isArray(data.questionOptionId) ? data.questionOptionId[0] // Take first if it's incorrectly an array
                     : data.questionOptionId;
                }
                // Ensure value type matches answerType requirements
                if (data.answerType === "number" || data.answerType === "decimal") {
                    // Ensure it's a number
                    payload.value = typeof data.value === "string" ? parseFloat(data.value) : typeof data.value === "number" ? data.value : 0;
                } else if (data.answerType === "selectmany") {
                    // For selectmany, value must be an array of strings
                    payload.value = Array.isArray(data.value) ? data.value.map({
                        "EditSubmissionModal.useMutation[updateAnswerMutation]": (v)=>String(v)
                    }["EditSubmissionModal.useMutation[updateAnswerMutation]"]) : [
                        String(data.value)
                    ];
                } else {
                    // For all other types, ensure value is a string
                    payload.value = String(data.value);
                }
                // Send the request
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateAnswer"])(payload, projectId);
            }
        }["EditSubmissionModal.useMutation[updateAnswerMutation]"],
        onSuccess: {
            "EditSubmissionModal.useMutation[updateAnswerMutation]": (response)=>{
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: t('answerUpdated'),
                    type: "success"
                }));
                // Invalidate queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: [
                        "formSubmissions"
                    ]
                });
                // Reset edit state
                setIsEditing(false);
                setEditingData(null);
                setNewResponse("");
                // Call onConfirm to close modal and clear selections
                onConfirm();
            }
        }["EditSubmissionModal.useMutation[updateAnswerMutation]"],
        onError: {
            "EditSubmissionModal.useMutation[updateAnswerMutation]": (error)=>{
                console.error("Error updating answer:", error);
                // Log full error details
                console.error("Error details:", {
                    response: error?.response?.data,
                    status: error?.response?.status,
                    headers: error?.response?.headers
                });
                // Extract more specific error message if available
                const errorMessage = error?.response?.data?.message || error?.response?.data?.errors || "Failed to update answer";
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: typeof errorMessage === "string" ? errorMessage : t('validationError'),
                    type: "error"
                }));
            }
        }["EditSubmissionModal.useMutation[updateAnswerMutation]"]
    });
    const handleSave = async ()=>{
        if (!editingData) return;
        // Get the current working submission
        const currentSubmission = isMultipleSelection && selectedSubmissionId ? selectedSubmissions.find((submission)=>submission.id === selectedSubmissionId) || submission : submission;
        // Check if submission has ID
        if (!currentSubmission?.id) {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                message: t('submissionIdMissing'),
                type: "error"
            }));
            return;
        }
        // Split new response by line breaks to get individual answers
        const newAnswers = newResponse.split("\n").map((answer)=>answer.trim()).filter((answer)=>answer);
        // Skip if no answers
        if (newAnswers.length === 0) {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                message: t('enterValidResponse'),
                type: "error"
            }));
            return;
        }
        const questionId = editingData.questionObject?.id;
        if (!questionId) {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                message: t('questionIdMissing'),
                type: "error"
            }));
            return;
        }
        // Get question type
        const answerType = editingData.type || "text";
        try {
            // Get question option IDs from original data if available
            let questionOptionId;
            if (answerType === "selectmany") {
                // For selectmany, we need an array of option IDs
                const optionIds = editingData.originalData?.map((item)=>item.questionOptionId).filter(Boolean);
                // If we don't have enough options, or any at all, create placeholders or use just the first one
                if (!optionIds || optionIds.length === 0) {
                    questionOptionId = newAnswers.map(()=>null);
                } else if (optionIds.length !== newAnswers.length) {
                    // If counts don't match, use the available ones and null for the rest
                    questionOptionId = [
                        ...optionIds
                    ];
                    while(questionOptionId.length < newAnswers.length){
                        questionOptionId.push(questionOptionId[0] || null);
                    }
                } else {
                    questionOptionId = optionIds;
                }
            } else {
                // For non-selectmany, use the first option ID if available
                questionOptionId = editingData.originalData?.[0]?.questionOptionId || null;
            }
            // Prepare the mutation data
            const mutationData = {
                submissionId: currentSubmission.id,
                questionId: questionId,
                answerType: answerType,
                value: answerType === "selectmany" ? newAnswers : newAnswers[0],
                questionOptionId: questionOptionId
            };
            // Execute the mutation
            updateAnswerMutation.mutate(mutationData);
        } catch (error) {
            console.error("Form validation error:", error);
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                message: t('checkInputTryAgain'),
                type: "error"
            }));
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            isOpen: showModal,
            onClose: onClose,
            className: "flex flex-col gap-5 p-6 rounded-md",
            children: !isEditing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col gap-4 max-h-[500px] overflow-y-auto",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-semibold text-neutral-700",
                                children: isMultipleSelection ? t('editSelectedSubmission') : t('editSubmission')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 470,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: isMultipleSelection && selectedSubmissions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "submission-selector",
                                            className: "text-sm font-medium text-neutral-700",
                                            children: t('selectSubmissionToEdit')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                            lineNumber: 480,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            id: "submission-selector",
                                            className: "border border-neutral-300 rounded-md p-1 text-sm bg-white",
                                            value: selectedSubmissionId || "",
                                            onChange: handleSubmissionChange,
                                            children: selectedSubmissions.map((sub)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: sub.id,
                                                    children: [
                                                        t('id'),
                                                        ": ",
                                                        sub.id
                                                    ]
                                                }, sub.id, true, {
                                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                                    lineNumber: 493,
                                                    columnNumber: 25
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                            lineNumber: 486,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 479,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 476,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 469,
                        columnNumber: 13
                    }, this),
                    isMultipleSelection && selectedSubmissionId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "font-medium",
                                children: [
                                    t('editingSubmissionId'),
                                    ": ",
                                    selectedSubmissionId
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 505,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs mt-1",
                                children: t('editingOneFromMultiple')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 508,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 504,
                        columnNumber: 15
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-neutral-700",
                        children: isMultipleSelection ? t('multipleSelectedChoose') : t('editingSingle')
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 514,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "rounded-md border border-neutral-400 max-h-[450px] overflow-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                                    className: "h-20",
                                    children: table.getHeaderGroups().map((headerGroup)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                            className: "text-sm border-neutral-400",
                                            children: headerGroup.headers.map((header)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold",
                                                    children: header.isPlaceholder ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(header.column.columnDef.header, header.getContext())
                                                }, header.id, false, {
                                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                                    lineNumber: 530,
                                                    columnNumber: 25
                                                }, this))
                                        }, headerGroup.id, false, {
                                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                            lineNumber: 525,
                                            columnNumber: 21
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 523,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                                    children: table.getRowModel().rows?.length ? table.getRowModel().rows.map((row)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                            className: " text-sm border-neutral-400",
                                            "data-state": row.getIsSelected() && "selected",
                                            children: row.getVisibleCells().map((cell)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                    className: "py-4 px-6",
                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flexRender"])(cell.column.columnDef.cell, cell.getContext())
                                                }, cell.id, false, {
                                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                                    lineNumber: 554,
                                                    columnNumber: 27
                                                }, this))
                                        }, row.id, false, {
                                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                            lineNumber: 548,
                                            columnNumber: 23
                                        }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                            colSpan: table.getAllColumns().length,
                                            className: "h-24 text-center",
                                            children: t('noResults')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                            lineNumber: 565,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                        lineNumber: 564,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                    lineNumber: 545,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                            lineNumber: 522,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 521,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end gap-4 mt-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "btn-outline",
                                onClick: onClose,
                                children: t('cancel')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 577,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "btn-primary",
                                onClick: handleSave,
                                disabled: updateAnswerMutation.isPending,
                                children: updateAnswerMutation.isPending ? t('saving') : t('confirmAndSave')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 580,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 576,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-lg font-semibold text-neutral-700",
                        children: [
                            t('editingQuestion'),
                            ": ",
                            editingData?.question
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 593,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-neutral-700",
                        children: t('editingMultipleInfo')
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 596,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: newResponse,
                        onChange: (e)=>setNewResponse(e.target.value),
                        className: "mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500",
                        placeholder: t('enterNewResponse')
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 599,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end gap-4 mt-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "btn-outline",
                                onClick: ()=>{
                                    setIsEditing(false);
                                    setEditingData(null);
                                    setNewResponse("");
                                },
                                disabled: updateAnswerMutation.isPending,
                                children: t('cancel')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 606,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "btn-primary",
                                onClick: handleSave,
                                disabled: updateAnswerMutation.isPending,
                                children: updateAnswerMutation.isPending ? t('saving') : t('confirmAndSave')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                                lineNumber: 617,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditSubmissionModal.tsx",
                        lineNumber: 605,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true)
        }, void 0, false, {
            fileName: "[project]/components/modals/EditSubmissionModal.tsx",
            lineNumber: 462,
            columnNumber: 7
        }, this)
    }, void 0, false);
};
_s(EditSubmissionModal, "NVP1rKWATTGgnkFkqnNxFDJT9Lo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$table$2f$build$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useReactTable"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
_c = EditSubmissionModal;
;
var _c;
__turbopack_context__.k.register(_c, "EditSubmissionModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/modals/ViewSubmissionDetail.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/Modal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
const ViewSubmissionDetail = ({ isOpen, onClose, submission })=>{
    _s();
    const [isFullscreen, setIsFullscreen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    // Ref to the container that will go fullscreen
    const contentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleFullscreenToggle = ()=>{
        setIsFullscreen((prev)=>!prev);
    };
    // Group answers by question label
    const groupedAnswers = new Map();
    submission.answers.forEach((answer)=>{
        const label = answer.question.label;
        if (!groupedAnswers.has(label)) {
            groupedAnswers.set(label, []);
        }
        groupedAnswers.get(label).push(answer.value);
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: isOpen,
        onClose: onClose,
        className: "p-6 rounded-md max-w-4xl w-full ",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            ref: contentRef,
            className: `flex flex-col gap-4 transition-all duration-300 ${isFullscreen ? "fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto" : ""}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-semibold text-neutral-700",
                            children: t('submissionDetails')
                        }, void 0, false, {
                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-neutral-600",
                                    children: "Validation status:"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 56,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    className: "px-3 py-1 border border-neutral-500 rounded-md text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "",
                                            children: t('select')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 58,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "valid",
                                            children: t('valid')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 59,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "invalid",
                                            children: t('notValid')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 60,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "pending",
                                            children: t('pending')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 61,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 57,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleFullscreenToggle,
                                    className: "btn-primary",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuFullscreen"], {
                                            className: "w-5 h-5"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 64,
                                            columnNumber: 15
                                        }, this),
                                        isFullscreen ? t('exitFullscreen') : t('fullscreen')
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 63,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                            lineNumber: 55,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                    lineNumber: 51,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                        className: "min-w-full divide-y divide-neutral-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                className: "bg-primary-500 text-neutral-100",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                            className: "px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider",
                                            children: t('question')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 74,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                            className: "px-4 py-2 text-left text-xs font-medium uppercase tracking-wider",
                                            children: t('response')
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                            lineNumber: 77,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 73,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                lineNumber: 72,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                className: "bg-neutral-100 divide-y divide-neutral-200",
                                children: [
                                    ...groupedAnswers.entries()
                                ].map(([label, values])=>{
                                    // Find the corresponding answer to get question details
                                    const answer = submission.answers.find((a)=>a.question.label === label);
                                    const isTableInput = answer?.question.inputType === "table";
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                className: "px-4 py-2 align-top",
                                                children: label
                                            }, void 0, false, {
                                                fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                                lineNumber: 92,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                className: "px-4 py-2",
                                                children: isTableInput ? // Show simple text for table input types
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-neutral-600 italic",
                                                    children: t('tableDataNote')
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                                    lineNumber: 96,
                                                    columnNumber: 25
                                                }, this) : // Show regular values for non-table input types
                                                values.join(", ")
                                            }, void 0, false, {
                                                fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                                lineNumber: 93,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, label, true, {
                                        fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                        lineNumber: 91,
                                        columnNumber: 19
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                        lineNumber: 71,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center mt-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-neutral-600 font-semibold",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        t('submittedBy'),
                                        ": ",
                                        submission.user?.name
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        t('submissionTime'),
                                        ": ",
                                        submission.submissionTime
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "btn-primary",
                                onClick: onClose,
                                children: t('close')
                            }, void 0, false, {
                                fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                            lineNumber: 116,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/modals/ViewSubmissionDetail.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
};
_s(ViewSubmissionDetail, "2IvsW4ZFXQrY5qgQ7kf0/bXV/8E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = ViewSubmissionDetail;
const __TURBOPACK__default__export__ = ViewSubmissionDetail;
var _c;
__turbopack_context__.k.register(_c, "ViewSubmissionDetail");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/form-builder.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addQuestion": (()=>addQuestion),
    "deleteQuestion": (()=>deleteQuestion),
    "duplicateQuestion": (()=>duplicateQuestion),
    "fetchFormBuilderData": (()=>fetchFormBuilderData),
    "fetchQuestionBlockQuestions": (()=>fetchQuestionBlockQuestions),
    "fetchQuestions": (()=>fetchQuestions),
    "fetchTemplateQuestions": (()=>fetchTemplateQuestions),
    "updateQuestion": (()=>updateQuestion),
    "updateQuestionPositions": (()=>updateQuestionPositions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
const getQuestionsEndPoint = (contextType)=>{
    if (contextType === "project") return "/questions";
    else if (contextType === "template") return "/template-questions";
    else if (contextType === "questionBlock") return "/question-blocks";
    throw new Error("Unsupported context type");
};
const fetchQuestions = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/questions/${projectId}`);
    return data.questions;
};
const fetchTemplateQuestions = async ({ templateId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/template-questions/${templateId}`);
    return data.questions;
};
const addQuestion = async ({ contextType, contextId, dataToSend, position })=>{
    const url = contextType === "questionBlock" ? `${getQuestionsEndPoint(contextType)}` : `${getQuestionsEndPoint(contextType)}/${contextId}`;
    // Validate required fields
    if (!dataToSend.label || !dataToSend.inputType) {
        throw new Error("Label and inputType are required");
    }
    // Check if this input type requires options
    const needsOptions = [
        "selectone",
        "selectmany"
    ].includes(dataToSend.inputType);
    const hasFile = dataToSend.file instanceof File;
    const hasOptions = Array.isArray(dataToSend.questionOptions) && dataToSend.questionOptions.length > 0;
    // Validate options based on input type and upload method
    if (needsOptions && !hasFile && !hasOptions) {
        throw new Error("Options are required for select input types");
    }
    if (hasFile) {
        const formData = new FormData();
        // Add basic question data
        formData.append("label", dataToSend.label);
        // Convert boolean to string in a way backend can parse
        formData.append("isRequired", dataToSend.isRequired ? "true" : "false");
        formData.append("inputType", dataToSend.inputType);
        if (dataToSend.hint) formData.append("hint", dataToSend.hint);
        if (dataToSend.placeholder) formData.append("placeholder", dataToSend.placeholder);
        // Convert number to string
        formData.append("position", String(position || 1));
        // Add file with the correct field name
        formData.append("file", dataToSend.file);
        // Important: Do NOT include questionOptions when uploading a file
        // They will be parsed from the file on the server
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, formData, {
                headers: {
                    "Content-Type": "multipart/form-data"
                }
            });
            return data;
        } catch (error) {
            console.error("Upload error details:", error.response?.data || error.message);
            throw new Error(`Failed to upload question with file: ${error.response?.data?.message || error.message}`);
        }
    } else {
        // Regular JSON request (no file)
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, {
                label: dataToSend.label,
                isRequired: dataToSend.isRequired,
                hint: dataToSend.hint,
                placeholder: dataToSend.placeholder,
                inputType: dataToSend.inputType,
                questionOptions: dataToSend.questionOptions,
                position: position || 1
            });
            return data;
        } catch (error) {
            console.error("API error details:", error.response?.data || error.message);
            throw new Error(`Failed to add question: ${error.response?.data?.message || error.message}`);
        }
    }
};
const deleteQuestion = async ({ contextType, id, projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`);
    return data;
};
const duplicateQuestion = async ({ id, contextType, contextId })=>{
    // For question blocks, we don't need to send the contextId in the body
    // The userId is taken from the authenticated user in the backend
    const requestBody = contextType === "questionBlock" ? {} : contextType === "project" ? {
        projectId: contextId
    } : {
        templateId: contextId
    };
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${getQuestionsEndPoint(contextType)}/duplicate/${id}?projectId=${contextId}`, requestBody);
    return data;
};
const updateQuestion = async ({ id, contextType, dataToSend, contextId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`, dataToSend);
    return data;
};
const fetchQuestionBlockQuestions = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/question-blocks`);
        return response.data.questions || [];
    } catch (error) {
        console.error("Error fetching question block questions:", error);
        throw error;
    }
};
const updateQuestionPositions = async ({ contextType, contextId, questionPositions })=>{
    // Only support position updates for projects currently
    if (contextType !== "project") {
        throw new Error("Question position updates are only supported for projects");
    }
    const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;
    const payload = {
        questionPositions
    };
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(url, payload);
        return data;
    } catch (error) {
        console.error("Update failed - Full error:", error);
        console.error("Update failed - Error details:", {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            config: {
                url: error.config?.url,
                method: error.config?.method,
                data: error.config?.data
            }
        });
        throw error;
    }
};
// Fetch form builder data with ordered structure (groups and questions)
const fetchFormBuilderData = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/projects/getalldata/${projectId}`);
    return data.data;
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/modals/ConfirmationModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConfirmationModal": (()=>ConfirmationModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/Modal.tsx [app-client] (ecmascript)");
"use client";
;
;
const ConfirmationModal = ({ showModal, onClose, onConfirm, title, description, confirmButtonText, cancelButtonText, confirmButtonClass, children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$Modal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: showModal,
        onClose: onClose,
        className: "p-6 rounded-md max-w-xl",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-lg font-semibold text-neutral-700",
                children: title
            }, void 0, false, {
                fileName: "[project]/components/modals/ConfirmationModal.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-neutral-700 mt-2",
                children: description
            }, void 0, false, {
                fileName: "[project]/components/modals/ConfirmationModal.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-6 space-y-4",
                children: children
            }, void 0, false, {
                fileName: "[project]/components/modals/ConfirmationModal.tsx",
                lineNumber: 35,
                columnNumber: 20
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end gap-4 mt-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "btn-outline",
                        onClick: onClose,
                        type: "button",
                        children: cancelButtonText || "Cancel"
                    }, void 0, false, {
                        fileName: "[project]/components/modals/ConfirmationModal.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: `font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`,
                        onClick: onConfirm,
                        type: "button",
                        children: confirmButtonText
                    }, void 0, false, {
                        fileName: "[project]/components/modals/ConfirmationModal.tsx",
                        lineNumber: 40,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/modals/ConfirmationModal.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/modals/ConfirmationModal.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
};
_c = ConfirmationModal;
;
var _c;
__turbopack_context__.k.register(_c, "ConfirmationModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$locale$5d2f28$main$292f$project$2f5b$hashedId$5d2f$data$2f$table$2f$columns$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[locale]/(main)/project/[hashedId]/data/table/columns.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ri/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$EditSubmissionModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/EditSubmissionModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$tables$2f$GeneralTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/tables/GeneralTable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$ViewSubmissionDetail$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/ViewSubmissionDetail.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/form-builder.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$ConfirmationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/modals/ConfirmationModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/encodeDecode.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/submission.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/redux/slices/notificationSlice.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const fetchSubmissions = async (projectId)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/form-submissions/${projectId}`);
    return data.data.formSubmissions;
};
const STORAGE_KEY = "data-table-column-visibility";
const Page = ()=>{
    _s();
    const { hashedId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const hashedIdString = hashedId;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const projectId = Number((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decode"])(hashedIdString));
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { data: submissions = [], isLoading, refetch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "formSubmissions",
            projectId
        ],
        queryFn: {
            "Page.useQuery": ()=>fetchSubmissions(projectId)
        }["Page.useQuery"],
        enabled: projectId !== null,
        refetchInterval: 1000,
        staleTime: 0,
        gcTime: 0
    });
    // Fetch ALL questions for the project (including conditional questions)
    const { data: allQuestions = [], isLoading: isLoadingQuestions } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "allQuestions",
            projectId
        ],
        queryFn: {
            "Page.useQuery": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchQuestions"])({
                    projectId: projectId
                })
        }["Page.useQuery"],
        enabled: projectId !== null,
        staleTime: 5 * 60 * 1000
    });
    const [isFullscreen, setIsFullscreen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showConfirmationModal, setShowConfirmationModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [globalFilter, setGlobalFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isAnyRowSelected, setIsAnyRowSelected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [confirmationModalContent, setConfirmationModalContent] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const [showEditModal, setShowEditModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [columnVisibility, setColumnVisibility] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState({});
    const [tableInstance, setTableInstance] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(null);
    const [selectedRows, setSelectedRows] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState({});
    const [showViewModal, setShowViewModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedSubmission, setSelectedSubmission] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const handleViewSubmission = (submission)=>{
        setSelectedSubmission(submission);
        setShowViewModal(true);
    };
    const columns = submissions.length > 0 && allQuestions.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$locale$5d2f28$main$292f$project$2f5b$hashedId$5d2f$data$2f$table$2f$columns$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateColumns"])(handleViewSubmission, submissions[0], hashedIdString, allQuestions) : [];
    const [isDropdownOpen, setIsDropdownOpen] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [statusDropdownOpen, setStatusDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const statusDropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Ref to the container that will go fullscreen
    const contentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Callback to monitor row selection changes
    const handleRowSelectionChange = (rowSelection)=>{
        setIsAnyRowSelected(Object.keys(rowSelection).length > 0);
        setSelectedRows(rowSelection);
        // Get the selected submission when row selection changes
        if (Object.keys(rowSelection).length === 1) {
            // If exactly one row is selected
            const selectedIndex = Number(Object.keys(rowSelection)[0]);
            setSelectedSubmission(submissions[selectedIndex]);
        } else if (Object.keys(rowSelection).length === 0) {
            // If no rows are selected
            setSelectedSubmission(null);
        }
    };
    const handleFullscreenToggle = ()=>{
        setIsFullscreen((prev)=>!prev);
    };
    // Handle status dropdown toggling
    const handleStatusDropdownToggle = ()=>{
        setStatusDropdownOpen((prev)=>!prev);
    };
    const handleEditConfirm = ()=>{
        // Refresh the data using invalidateQueries
        queryClient.invalidateQueries({
            queryKey: [
                "formSubmissions",
                projectId
            ]
        });
        // Clear row selections
        setSelectedRows({});
        setIsAnyRowSelected(false);
        // Close the modal
        setShowEditModal(false);
        setSelectedSubmission(null);
    };
    // Mutation for deleting a single submission
    const deleteMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "Page.useMutation[deleteMutation]": (submissionId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteFormSubmission"])(submissionId, projectId)
        }["Page.useMutation[deleteMutation]"],
        onSuccess: {
            "Page.useMutation[deleteMutation]": ()=>{
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: t('submissionDeleted'),
                    type: "success"
                }));
                queryClient.invalidateQueries({
                    queryKey: [
                        "formSubmissions",
                        projectId
                    ]
                });
                // Clear selections
                setSelectedRows({});
                setIsAnyRowSelected(false);
                setSelectedSubmission(null);
                setShowConfirmationModal(false);
            }
        }["Page.useMutation[deleteMutation]"],
        onError: {
            "Page.useMutation[deleteMutation]": (error)=>{
                console.error("Error deleting submission:", error);
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: t('submissionDeleteFailed'),
                    type: "error"
                }));
                setShowConfirmationModal(false);
            }
        }["Page.useMutation[deleteMutation]"]
    });
    // Mutation for deleting multiple submissions
    const deleteMultipleMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "Page.useMutation[deleteMultipleMutation]": (submissionIds)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteMultipleFormSubmissions"])(submissionIds, projectId)
        }["Page.useMutation[deleteMultipleMutation]"],
        onSuccess: {
            "Page.useMutation[deleteMultipleMutation]": (_, variables)=>{
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: `${variables.length} ${t('submissionsDeleted')}`,
                    type: "success"
                }));
                // Refresh the data
                queryClient.invalidateQueries({
                    queryKey: [
                        "formSubmissions",
                        projectId
                    ]
                });
                // Clear selections
                setSelectedRows({});
                setIsAnyRowSelected(false);
                setSelectedSubmission(null);
                setShowConfirmationModal(false);
            }
        }["Page.useMutation[deleteMultipleMutation]"],
        onError: {
            "Page.useMutation[deleteMultipleMutation]": (error)=>{
                console.error("Error deleting multiple submissions:", error);
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: t('submissionsDeleteFailed'),
                    type: "error"
                }));
                setShowConfirmationModal(false);
            }
        }["Page.useMutation[deleteMultipleMutation]"]
    });
    const handleDeleteClick = ()=>{
        // Get the IDs of the selected submissions
        const selectedSubmissionIds = Object.keys(selectedRows).map((key)=>{
            const index = parseInt(key);
            return submissions[index]?.id || 0;
        }).filter((id)=>id > 0);
        setConfirmationModalContent({
            title: t('confirmDeletion'),
            description: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        t('areYouSureToDelete'),
                        selectedSubmissionIds.length > 1 ? `${t('these')} ${selectedSubmissionIds.length} ${t('submissions')}` : t('thisSubmission'),
                        "? ",
                        t('cannotRecoverSubmissions')
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                    lineNumber: 254,
                    columnNumber: 11
                }, this)
            }, void 0, false),
            confirmButtonText: t('delete'),
            confirmButtonClass: "bg-red-500 hover:bg-red-600 cursor-pointer",
            onConfirm: ()=>{
                if (selectedSubmissionIds.length === 1) {
                    // Delete a single submission using the mutation
                    deleteMutation.mutate(selectedSubmissionIds[0]);
                } else if (selectedSubmissionIds.length > 1) {
                    // Delete multiple submissions using the mutation
                    deleteMultipleMutation.mutate(selectedSubmissionIds);
                }
            }
        });
        setShowConfirmationModal(true);
    };
    const handleEditClick = ()=>{
        const selectedIds = Object.keys(selectedRows);
        if (selectedIds.length === 0) {
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                message: t('noSubmissionSelected'),
                type: "error"
            }));
            return;
        }
        // Collect all selected submissions
        const selectedSubmissionsArray = selectedIds.map((id)=>{
            const index = Number(id);
            return submissions[index];
        }).filter(Boolean);
        // If exactly one row is selected, use that submission
        if (selectedIds.length === 1) {
            setSelectedSubmission(selectedSubmissionsArray[0]);
            setShowEditModal(true);
            return;
        }
        // For multiple selections, we'll set the first one as the default
        if (selectedIds.length > 1) {
            setSelectedSubmission(selectedSubmissionsArray[0]);
            setShowEditModal(true);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Page.useEffect": ()=>{
            const handleClickOutside = {
                "Page.useEffect.handleClickOutside": (event)=>{
                    if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
                        setStatusDropdownOpen(false);
                    }
                }
            }["Page.useEffect.handleClickOutside"];
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "Page.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["Page.useEffect"];
        }
    }["Page.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Page.useEffect": ()=>{
            try {
                const saved = localStorage.getItem(STORAGE_KEY);
                if (saved) {
                    const parsed = JSON.parse(saved);
                    if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
                        setColumnVisibility(parsed);
                    } else {
                        console.warn("Invalid format in localstorage for column visibility");
                    }
                }
            } catch (error) {
                console.error("Error loading column visibility:", error);
            }
        }
    }["Page.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Page.useEffect": ()=>{
            if (Object.keys(columnVisibility).length > 0) {
                try {
                    localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));
                } catch (error) {
                    console.error("Error saving column visibility:", error);
                }
            }
        }
    }["Page.useEffect"], [
        columnVisibility
    ]);
    // Effect to properly update the table when selectedRows changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Page.useEffect": ()=>{
            if (tableInstance && Object.keys(selectedRows).length === 0) {
                // If selectedRows is empty and we have a table instance,
                // explicitly reset the table's row selection
                tableInstance.resetRowSelection();
            }
        }
    }["Page.useEffect"], [
        selectedRows,
        tableInstance
    ]);
    const handleColumnVisibilityChange = (newState)=>{
        setColumnVisibility(newState);
    };
    const handleTableInit = (table)=>{
        setTableInstance(table);
        if (Object.keys(columnVisibility).length > 0) {
            table.setColumnVisibility(columnVisibility);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: contentRef,
        className: `flex flex-col gap-4 transition-all duration-300 ${isFullscreen ? "fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto" : ""}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col desktop:flex-row justify-between gap-8 items-center py-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                placeholder: t('searchAllColumns'),
                                value: globalFilter,
                                onChange: (e)=>setGlobalFilter(e.target.value)
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 387,
                                columnNumber: 11
                            }, this),
                            tableInstance && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                                open: isDropdownOpen,
                                onOpenChange: (open)=>setIsDropdownOpen(open),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            className: "flex items-center gap-2 cursor-pointer",
                                            children: [
                                                t('showHideColumns'),
                                                isDropdownOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronUp"], {
                                                    className: "w-3 h-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                                    lineNumber: 405,
                                                    columnNumber: 21
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronDown"], {
                                                    className: "w-3 h-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                                    lineNumber: 407,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                            lineNumber: 399,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 398,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                        align: "start",
                                        className: "bg-neutral-100 border border-neutral-200 shadow-md",
                                        children: tableInstance.getAllColumns().filter((column)=>column.getCanHide()).map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuCheckboxItem"], {
                                                className: "capitalize cursor-pointer hover:bg-neutral-200",
                                                checked: columnVisibility[column.id] ?? true,
                                                onCheckedChange: (value)=>setColumnVisibility((prev)=>({
                                                            ...prev,
                                                            [column.id]: value
                                                        })),
                                                children: column.id
                                            }, column.id, false, {
                                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                                lineNumber: 419,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 411,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 394,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                        lineNumber: 386,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: statusDropdownRef,
                        className: "flex relative items-center gap-4 text-neutral-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleFullscreenToggle,
                                className: "btn-primary",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuFullscreen"], {
                                        className: "w-5 h-5"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 442,
                                        columnNumber: 13
                                    }, this),
                                    isFullscreen ? t('exitFullscreen') : t('fullscreen')
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 441,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: ` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${isAnyRowSelected ? "hover:bg-primary-600  cursor-pointer" : "opacity-50"}`,
                                onClick: isAnyRowSelected ? handleStatusDropdownToggle : undefined,
                                children: [
                                    t('status'),
                                    statusDropdownOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronUp"], {
                                        className: "w-3 h-3"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 456,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronDown"], {
                                        className: "w-3 h-3"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 458,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 446,
                                columnNumber: 11
                            }, this),
                            statusDropdownOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col  gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",
                                            children: t('setOnApproved')
                                        }, void 0, false, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                            lineNumber: 464,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",
                                            children: t('setOnNotApproved')
                                        }, void 0, false, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                            lineNumber: 467,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm",
                                            children: t('setOnHold')
                                        }, void 0, false, {
                                            fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                            lineNumber: 470,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                    lineNumber: 463,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 462,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: ` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${isAnyRowSelected ? "hover:bg-primary-600  cursor-pointer" : "opacity-50"}`,
                                onClick: isAnyRowSelected ? handleEditClick : undefined,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BiSolidEdit"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 484,
                                        columnNumber: 13
                                    }, this),
                                    t('edit')
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 476,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: ` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${isAnyRowSelected ? "hover:bg-primary-600  cursor-pointer" : "opacity-50"}`,
                                onClick: isAnyRowSelected ? handleDeleteClick : undefined,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RiDeleteBin6Fill"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                        lineNumber: 495,
                                        columnNumber: 13
                                    }, this),
                                    t('delete')
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                                lineNumber: 487,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                        lineNumber: 437,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 385,
                columnNumber: 7
            }, this),
            isLoading || isLoadingQuestions ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center items-center py-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-muted-foreground",
                    children: t('loadingData')
                }, void 0, false, {
                    fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                    lineNumber: 503,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 502,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$tables$2f$GeneralTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GeneralTable"], {
                columns: columns,
                data: submissions,
                globalFilter: globalFilter,
                setGlobalFilter: setGlobalFilter,
                onTableInit: handleTableInit,
                columnVisibility: columnVisibility,
                setColumnVisibility: handleColumnVisibilityChange,
                onRowSelectionChange: handleRowSelectionChange,
                rowSelection: selectedRows
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 506,
                columnNumber: 9
            }, this),
            showEditModal && selectedSubmission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$EditSubmissionModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditSubmissionModal"], {
                showModal: showEditModal,
                projectId: projectId,
                onClose: ()=>{
                    setShowEditModal(false);
                    setSelectedSubmission(null);
                    // Clear row selections when closing the modal
                    setSelectedRows({});
                    setIsAnyRowSelected(false);
                },
                onConfirm: handleEditConfirm,
                submission: selectedSubmission,
                isMultipleSelection: Object.keys(selectedRows).length > 1,
                selectedSubmissions: Object.keys(selectedRows).length > 1 ? Object.keys(selectedRows).map((id)=>submissions[Number(id)]).filter(Boolean) : []
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 521,
                columnNumber: 9
            }, this),
            confirmationModalContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$ConfirmationModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfirmationModal"], {
                showModal: showConfirmationModal,
                onClose: ()=>setShowConfirmationModal(false),
                onConfirm: confirmationModalContent.onConfirm,
                title: confirmationModalContent.title,
                description: confirmationModalContent.description,
                confirmButtonText: confirmationModalContent.confirmButtonText,
                confirmButtonClass: confirmationModalContent.confirmButtonClass
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 545,
                columnNumber: 9
            }, this),
            selectedSubmission && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$ViewSubmissionDetail$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showViewModal,
                onClose: ()=>setShowViewModal(false),
                submission: selectedSubmission
            }, void 0, false, {
                fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
                lineNumber: 557,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/[locale]/(main)/project/[hashedId]/data/table/page.tsx",
        lineNumber: 377,
        columnNumber: 5
    }, this);
};
_s(Page, "h3NlpYrGALLVbPeAjtK2cfEa38U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
_c = Page;
const __TURBOPACK__default__export__ = Page;
var _c;
__turbopack_context__.k.register(_c, "Page");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_62118664._.js.map
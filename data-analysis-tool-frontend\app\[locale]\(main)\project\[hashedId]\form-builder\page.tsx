"use client";

import { FormPreview } from "@/components/form-preview";
import { useState } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { useQuery } from "@tanstack/react-query";
import { fetchQuestions } from "@/lib/api/form-builder";
import { fetchQuestionGroups } from "@/lib/api/question-groups";
import Spinner from "@/components/general/Spinner";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { FormBuilder } from "@/components/form-builder/FormBuilder";
import { useProjectPermissions } from "@/hooks/useProjectPermissions";
import { fetchProjectById } from "@/lib/api/projects";
import { Project } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";

export default function FormBuilderPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const { hashedId } = useParams();
  const { user } = useAuth();
  const hashedIdString = hashedId as string;

  const projectId = Number(decode(hashedIdString));
  const t = useTranslations();

  // Remove the separate questions query since FormBuilder now handles data loading internally

  // Fetch question groups for the project
  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({
    queryKey: ["questionGroups", projectId],
    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),
    enabled: !!projectId,
  });

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id,
  });

  const permissions = useProjectPermissions({
    projectData,
    user,
  });

  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('invalidProjectId')}</h1>
        <p className="text-neutral-700">
          {t('invalidProjectUrl')}
        </p>
      </div>
    );
  }

  // Loading and error handling is now managed within FormBuilder component

  return (
    <div className="p-6">
      {isPreviewMode ? (
        <FormPreview
          questions={[]} // FormPreview will need to be updated separately to use the new API
          questionGroups={questionGroups}
          contextType="project"
          onClose={() => setIsPreviewMode(false)}
          hashedId={hashedIdString} // Pass hashedId
        />
      ) : (
        <FormBuilder
          setIsPreviewMode={setIsPreviewMode}
          contextType="project"
          contextId={projectId}
          permissions={permissions}
        />
      )}
    </div>
  );
}

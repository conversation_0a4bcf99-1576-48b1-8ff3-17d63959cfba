{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/Modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion, easeInOut } from \"framer-motion\";\r\nimport { X } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\nconst Modal = ({\r\n  children,\r\n  className,\r\n  isOpen,\r\n  onClose,\r\n  preventOutsideClick = false,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  preventOutsideClick?: boolean;\r\n}) => {\r\n  // Handle backdrop click with confirmation if needed\r\n  const handleBackdropClick = (e: React.MouseEvent) => {\r\n    if (preventOutsideClick) {\r\n      // Do nothing, prevent closing\r\n      return;\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto\"\r\n          onClick={handleBackdropClick} // Handle backdrop click\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.6, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            exit={{ scale: 0.6, opacity: 0 }}\r\n            transition={{ duration: 0.3, ease: easeInOut }}\r\n            className={`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${className}`}\r\n            onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it\r\n          >\r\n            <X\r\n              onClick={onClose}\r\n              className=\"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300\"\r\n            />\r\n            {children}\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAMA,MAAM,QAAQ,CAAC,EACb,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,sBAAsB,KAAK,EAO5B;IACC,oDAAoD;IACpD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,qBAAqB;YACvB,8BAA8B;YAC9B;QACF,OAAO;YACL;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM,iKAAA,CAAA,YAAS;gBAAC;gBAC7C,WAAW,CAAC,sEAAsE,EAAE,WAAW;gBAC/F,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAEjC,8OAAC,4LAAA,CAAA,IAAC;wBACA,SAAS;wBACT,WAAU;;;;;;oBAEX;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/VerificationModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { AlertTriangle, Mail } from \"lucide-react\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst VerificationModal = ({\r\n  email,\r\n  showModal,\r\n  setShowModal,\r\n}: {\r\n  email: string;\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n}) => {\r\n  const dispatch = useDispatch();\r\n  const t = useTranslations();\r\n  const sendVerificationEmail = async () => {\r\n    try {\r\n      await axios.post(`/users/sendverificationemail`, {\r\n        email: email,\r\n      });\r\n    } catch (error) {\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n           t('failedToSendVerification'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    }\r\n  };\r\n  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(true);\r\n  const [countDown, setCountDown] = useState<number>(60);\r\n\r\n  useEffect(() => {\r\n    let timer: number;\r\n    if (isResendDisabled && countDown > 0) {\r\n      timer = window.setInterval(() => {\r\n        setCountDown((prev) => prev - 1);\r\n      }, 1000);\r\n    } else if (countDown === 0) {\r\n      setIsResendDisabled(false);\r\n      setCountDown(60);\r\n    }\r\n    return () => clearInterval(timer);\r\n  }, [isResendDisabled, countDown]);\r\n\r\n  useEffect(() => {\r\n    if (showModal && email) {\r\n      sendVerificationEmail();\r\n    }\r\n    return () => {\r\n      setCountDown(60), setIsResendDisabled(true);\r\n    };\r\n  }, [showModal]);\r\n\r\n  const handleResend = async () => {\r\n    setIsResendDisabled(true);\r\n    const apiUrl =\r\n      process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\";\r\n    try {\r\n      await axios.post(`/users/sendverificationemail`, { email });\r\n    } catch (error) {\r\n      console.error(\"error sending email\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={() => setShowModal(false)}\r\n      className=\"flex flex-col items-center gap-4\"\r\n    >\r\n      <div className=\"rounded-full p-2 bg-primary-300\">\r\n        <Mail className=\"text-primary-500\" />\r\n      </div>\r\n      <h1 className=\"heading-text\">{t('checkYourEmail')}</h1>\r\n      <p className=\"flex flex-col items-center\">\r\n        {t('verificationSentTo')}\r\n        <span className=\"font-medium\">{email}</span>\r\n      </p>\r\n      <div className=\"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md\">\r\n        <AlertTriangle size={16} /> {t('didNotReceiveEmail')}\r\n      </div>\r\n      <button\r\n        className=\"btn-primary\"\r\n        onClick={handleResend}\r\n        disabled={isResendDisabled}\r\n      >\r\n        {isResendDisabled ? (\r\n          <div className=\"flex items-center gap-2\">\r\n            <span>{t('resendIn')} {countDown}s</span>\r\n            <div className=\"size-4 animate-spin border-x-2 rounded-full\"></div>\r\n          </div>\r\n        ) : (\r\n          <span>{t('resend')}</span>\r\n        )}\r\n      </button>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { VerificationModal };\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASA,MAAM,oBAAoB,CAAC,EACzB,KAAK,EACL,SAAS,EACT,YAAY,EAKb;IACC,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAC/C,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SACC,EAAE;gBACH,MAAM;YACR;QAEJ;IACF;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,oBAAoB,YAAY,GAAG;YACrC,QAAQ,OAAO,WAAW,CAAC;gBACzB,aAAa,CAAC,OAAS,OAAO;YAChC,GAAG;QACL,OAAO,IAAI,cAAc,GAAG;YAC1B,oBAAoB;YACpB,aAAa;QACf;QACA,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAU;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,OAAO;YACtB;QACF;QACA,OAAO;YACL,aAAa,KAAK,oBAAoB;QACxC;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe;QACnB,oBAAoB;QACpB,MAAM,SACJ,iEAAmC;QACrC,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAAE;YAAM;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,aAAa;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,8OAAC;gBAAG,WAAU;0BAAgB,EAAE;;;;;;0BAChC,8OAAC;gBAAE,WAAU;;oBACV,EAAE;kCACH,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,MAAM;;;;;;oBAAM;oBAAE,EAAE;;;;;;;0BAEjC,8OAAC;gBACC,WAAU;gBACV,SAAS;gBACT,UAAU;0BAET,iCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCAAM,EAAE;gCAAY;gCAAE;gCAAU;;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;;;;;;;;;;yCAGjB,8OAAC;8BAAM,EAAE;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/useAuth.tsx"], "sourcesContent": ["import {\r\n  setAuthenticatedUser,\r\n  setAuthError,\r\n  setAuthLoading,\r\n  setUnauthenticated,\r\n} from \"@/redux/slices/authSlice\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { UserSession } from \"@/types/authTypes\";\r\nimport { AxiosError, isAxiosError } from \"axios\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst useAuth = (options?: { skipFetchUser?: boolean }) => {\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { status, user, error } = useSelector((state: RootState) => state.auth);\r\n\r\n  const fetchUserData = async () => {\r\n    try {\r\n      dispatch(setAuthLoading());\r\n      const response = await axios.get(`/users/me`);\r\n      const userData: UserSession = response.data;\r\n      dispatch(setAuthenticatedUser(userData));\r\n    } catch (error) {\r\n      // Handle errors, especially 401 Unauthorized\r\n      dispatch(setUnauthenticated());\r\n\r\n      if (isAxiosError(error)) {\r\n        console.error(\r\n          \"Auth error:\",\r\n          error.response?.status,\r\n          error.response?.data\r\n        );\r\n\r\n        // If error is 401 Unauthorized (including expired token)\r\n        if (error.response?.status === 401) {\r\n          // Check if we're on a form-test route\r\n          if (pathname.startsWith(\"/form-submission\")) {\r\n            // Don't redirect for form-test routes, let the component handle it\r\n            return;\r\n          }\r\n          router.push(\"/\");\r\n        } else {\r\n          // For other errors\r\n          dispatch(\r\n            setAuthError(error.response?.data?.message || error.message)\r\n          );\r\n        }\r\n      } else {\r\n        dispatch(\r\n          setAuthError(\r\n            error instanceof Error\r\n              ? error.message\r\n              : \"An unknown error occurred.\"\r\n          )\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!options?.skipFetchUser) {\r\n      fetchUserData();\r\n    }\r\n  }, [options?.skipFetchUser]);\r\n\r\n  // Add event listener for storage changes to handle logout across tabs\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === \"logout\" && e.newValue === \"true\") {\r\n        dispatch(setUnauthenticated());\r\n        // Check if we're on a form-test route\r\n        if (pathname.startsWith(\"/form-submission\")) {\r\n          // For form-test routes, redirect to the sign-in page of the same form\r\n          const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n          if (hashedId) {\r\n            router.push(`/form-submission/${hashedId}/sign-in`);\r\n          } else {\r\n            router.push(\"/\");\r\n          }\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch, router, pathname]);\r\n\r\n  const refreshAuthState = () => {\r\n    fetchUserData();\r\n  };\r\n\r\n  const signin = async (\r\n    data: { email: string; password: string },\r\n    onSuccess?: () => void,\r\n    onError?: (errorType?: string) => void\r\n  ) => {\r\n    try {\r\n      await axios.post(`/users/login`, data);\r\n      await fetchUserData();\r\n      onSuccess?.();\r\n    } catch (error) {\r\n      if (error instanceof AxiosError) {\r\n        const errorType = error.response?.data?.errorType;\r\n        onError?.(errorType);\r\n      } else {\r\n        onError?.();\r\n      }\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await axios.post(`/users/logout`);\r\n      // Notify other tabs about logout\r\n      localStorage.setItem(\"logout\", \"true\");\r\n      // Remove the flag immediately to ensure future logout events still trigger\r\n      setTimeout(() => localStorage.removeItem(\"logout\"), 100);\r\n    } finally {\r\n      dispatch(setUnauthenticated());\r\n      // Check if we're on a form-test route\r\n      if (pathname.startsWith(\"/form-submission\")) {\r\n        // For form-test routes, redirect to the sign-in page of the same form\r\n        const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n        if (hashedId) {\r\n          router.push(`/form-submission/${hashedId}/sign-in`);\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      } else {\r\n        router.push(\"/\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return {\r\n    status,\r\n    user,\r\n    error,\r\n    isAuthenticated: status === \"authenticated\",\r\n    isLoading: status === \"loading\",\r\n    refreshAuthState,\r\n    signin,\r\n    logout,\r\n  };\r\n};\r\n\r\nexport { useAuth };\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,UAAU,CAAC;IACf,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAE5E,MAAM,gBAAgB;QACpB,IAAI;YACF,SAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;YACtB,MAAM,WAAW,MAAM,4GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YAC5C,MAAM,WAAwB,SAAS,IAAI;YAC3C,SAAS,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;YAE1B,IAAI,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACvB,QAAQ,KAAK,CACX,eACA,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE;gBAGlB,yDAAyD;gBACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,sCAAsC;oBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;wBAC3C,mEAAmE;wBACnE;oBACF;oBACA,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,mBAAmB;oBACnB,SACE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;gBAE/D;YACF,OAAO;gBACL,SACE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EACT,iBAAiB,QACb,MAAM,OAAO,GACb;YAGV;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,eAAe;YAC3B;QACF;IACF,GAAG;QAAC,SAAS;KAAc;IAE3B,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;gBAC/C,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;gBAC1B,sCAAsC;gBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;oBAC3C,sEAAsE;oBACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;oBACtE,IAAI,UAAU;wBACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;oBACpD,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,SAAS,OACb,MACA,WACA;QAEA,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE;YACjC,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,8IAAA,CAAA,aAAU,EAAE;gBAC/B,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;gBACxC,UAAU;YACZ,OAAO;gBACL;YACF;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,4GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;YAChC,iCAAiC;YACjC,aAAa,OAAO,CAAC,UAAU;YAC/B,2EAA2E;YAC3E,WAAW,IAAM,aAAa,UAAU,CAAC,WAAW;QACtD,SAAU;YACR,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD;YAC1B,sCAAsC;YACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;gBAC3C,sEAAsE;gBACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;gBACtE,IAAI,UAAU;oBACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;gBACpD,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA,iBAAiB,WAAW;QAC5B,WAAW,WAAW;QACtB;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useLocale } from 'next-intl';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\n\r\nexport default function LanguageSwitcher() {\r\n  const locale = useLocale();\r\n  const pathname = usePathname();\r\n  \r\n  // Function to get the path for the alternate locale\r\n  const getLocalizedPath = (newLocale: string) => {\r\n    // Remove the current locale from the pathname if it exists\r\n    const pathnameWithoutLocale = pathname.replace(/^\\/(en|ne)/, '');\r\n    return `/${newLocale}${pathnameWithoutLocale}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Link \r\n        href={getLocalizedPath('en')}\r\n        className={`px-3 py-1 rounded ${locale === 'en' ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}\r\n      >\r\n        English\r\n      </Link>\r\n      <Link \r\n        href={getLocalizedPath('ne')}\r\n        className={`px-3 py-1 rounded ${locale === 'ne' ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}\r\n      >\r\n        नेपाली\r\n      </Link>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,oDAAoD;IACpD,MAAM,mBAAmB,CAAC;QACxB,2DAA2D;QAC3D,MAAM,wBAAwB,SAAS,OAAO,CAAC,cAAc;QAC7D,OAAO,CAAC,CAAC,EAAE,YAAY,uBAAuB;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,iBAAiB;gBACvB,WAAW,CAAC,kBAAkB,EAAE,WAAW,OAAO,8BAA8B,eAAe;0BAChG;;;;;;0BAGD,8OAAC,4JAAA,CAAA,UAAI;gBA<PERSON>,MAAM,iBAAiB;gBACvB,WAAW,CAAC,kBAAkB,EAAE,WAAW,OAAO,8BAA8B,eAAe;0BAChG;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28auth%29/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { VerificationModal } from \"@/components/modals/VerificationModal\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { ShieldCheck, Eye, EyeOff } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { z } from \"zod\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport LanguageSwitcher from \"@/components/LanguageSwitcher\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\n\r\nconst signInSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .min(1, \"Email is required\")\r\n    .email(\"Please enter a valid email address\"),\r\n  password: z.string().min(1, \"Password is required\"),\r\n});\r\n\r\ntype SignInFormValues = z.infer<typeof signInSchema>;\r\n\r\nconst page = () => {\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitting },\r\n    handleSubmit,\r\n    getValues,\r\n    watch,\r\n  } = useForm<SignInFormValues>({ resolver: zodResolver(signInSchema) });\r\n\r\n  // Watch password field to determine when to show the eye button\r\n  const passwordValue = watch(\"password\");\r\n\r\n  const router = useRouter();\r\n  const dispatch = useDispatch();\r\n\r\n  const t = useTranslations();\r\n\r\n  const [showVerificationModal, setShowVerificationModal] =\r\n    useState<boolean>(false);\r\n  const [showPassword, setShowPassword] = useState<boolean>(false);\r\n\r\n  const { signin } = useAuth({ skipFetchUser: true });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    signin(\r\n      { email: data.email, password: data.password },\r\n      () => {\r\n        dispatch(\r\n          showNotification({ message: t('signInSuccessful'), type: \"success\" })\r\n        );\r\n        router.push(\"/dashboard\");\r\n      },\r\n      (errorType) => {\r\n        if (errorType === \"unverified\") {\r\n          setShowVerificationModal(true);\r\n        } else {\r\n          dispatch(\r\n            showNotification({\r\n              message: t('invalidEmailOrPassword'),\r\n              type: \"error\",\r\n            })\r\n          );\r\n        }\r\n      }\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center\">\r\n      <VerificationModal\r\n        email={getValues(\"email\")}\r\n        showModal={showVerificationModal}\r\n        setShowModal={setShowVerificationModal}\r\n      />\r\n      <div className=\"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg\">\r\n        <div className=\"flex flex-col items-center gap-2 mb-8\">\r\n          <ShieldCheck size={36} />\r\n          <h1 className=\"text-2xl tablet:text-3xl font-semibold text-center\">\r\n            {t('signInToYourAccount')}\r\n          </h1>\r\n          <p className=\"text-neutral-700 text-center\">\r\n            {t('getStarted2')}\r\n          </p>\r\n        </div>\r\n        <form\r\n          className=\"flex flex-col gap-4 mb-4\"\r\n          onSubmit={handleSubmit(onSubmit)}\r\n        >\r\n          <div className=\"group label-input-group\">\r\n            <label htmlFor=\"email\" className=\"label-text\">\r\n              {t('email')}\r\n            </label>\r\n            <input\r\n              {...register(\"email\")}\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder={t('enterEmail')}\r\n              className=\"input-field\"\r\n            />\r\n            {errors.email && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.email.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"group label-input-group\">\r\n            <label htmlFor=\"password\" className=\"label-text\">\r\n              {t('password')}\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                {...register(\"password\")}\r\n                id=\"password\"\r\n                type={showPassword ? \"text\" : \"password\"}\r\n                placeholder={t('enterPassword')}\r\n                className=\"input-field w-full pr-10\"\r\n              />\r\n              {passwordValue && passwordValue.length > 0 && (\r\n                <button\r\n                  type=\"button\"\r\n                  tabIndex={-1}\r\n                  className=\"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                >\r\n                  {showPassword ? (\r\n                    <EyeOff className=\"h-4 w-4\" />\r\n                  ) : (\r\n                    <Eye className=\"h-4 w-4\" />\r\n                  )}\r\n                  <span className=\"sr-only\">\r\n                    {showPassword ? \"Hide\" : \"Show\"} password\r\n                  </span>\r\n                </button>\r\n              )}\r\n            </div>\r\n            {errors.password && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.password.message}`}</p>\r\n            )}\r\n          </div>\r\n          <button type=\"submit\" className=\"btn-primary\" disabled={isSubmitting}>\r\n            {isSubmitting ? (\r\n              <span className=\"flex items-center gap-2\">\r\n                {t('signingIn')}\r\n                <div className=\"size-4 rounded-full border-x-2 animate-spin\"></div>\r\n              </span>\r\n            ) : (\r\n              t('signIn')\r\n            )}\r\n          </button>\r\n        </form>\r\n        <Link\r\n          href={\"/reset-password\"}\r\n          className=\"self-end underline text-neutral-700\"\r\n        >\r\n          {t('forgotPassword')}\r\n        </Link>\r\n\r\n        <div className=\"text-neutral-700 flex items-center gap-2\">\r\n          <span>{t('noAccount')}</span>\r\n          <Link\r\n            href=\"/signup\"\r\n            className=\"font-medium hover:text-neutral-900 duration-300\"\r\n          >\r\n            {t('signUp')}\r\n          </Link>\r\n        </div>\r\n        <div className=\"mt-4\">\r\n          <LanguageSwitcher/>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAiBA,MAAM,eAAe,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIA,MAAM,OAAO;IACX,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,YAAY,EACZ,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAAE,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IAAc;IAEpE,gEAAgE;IAChE,MAAM,gBAAgB,MAAM;IAE5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE1D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iHAAA,CAAA,UAAO,AAAD,EAAE;QAAE,eAAe;IAAK;IAEjD,MAAM,WAAW,OAAO;QACtB,OACE;YAAE,OAAO,KAAK,KAAK;YAAE,UAAU,KAAK,QAAQ;QAAC,GAC7C;YACE,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,SAAS,EAAE;gBAAqB,MAAM;YAAU;YAErE,OAAO,IAAI,CAAC;QACd,GACA,CAAC;YACC,IAAI,cAAc,cAAc;gBAC9B,yBAAyB;YAC3B,OAAO;gBACL,SACE,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;QACF;IAEJ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,oBAAiB;gBAChB,OAAO,UAAU;gBACjB,WAAW;gBACX,cAAc;;;;;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;0CACnB,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;wBACC,WAAU;wBACV,UAAU,aAAa;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAC9B,EAAE;;;;;;kDAEL,8OAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,IAAG;wCACH,MAAK;wCACL,aAAa,EAAE;wCACf,WAAU;;;;;;oCAEX,OAAO,KAAK,kBACX,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDACjC,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACE,GAAG,SAAS,WAAW;gDACxB,IAAG;gDACH,MAAM,eAAe,SAAS;gDAC9B,aAAa,EAAE;gDACf,WAAU;;;;;;4CAEX,iBAAiB,cAAc,MAAM,GAAG,mBACvC,8OAAC;gDACC,MAAK;gDACL,UAAU,CAAC;gDACX,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;;oDAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEAEjB,8OAAC;wDAAK,WAAU;;4DACb,eAAe,SAAS;4DAAO;;;;;;;;;;;;;;;;;;;oCAKvC,OAAO,QAAQ,kBACd,8OAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,QAAQ,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGrE,8OAAC;gCAAO,MAAK;gCAAS,WAAU;gCAAc,UAAU;0CACrD,6BACC,8OAAC;oCAAK,WAAU;;wCACb,EAAE;sDACH,8OAAC;4CAAI,WAAU;;;;;;;;;;;2CAGjB,EAAE;;;;;;;;;;;;kCAIR,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCAET,EAAE;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAM,EAAE;;;;;;0CACT,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;;kCAGP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+HAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;AAK3B;uCAEe", "debugId": null}}]}
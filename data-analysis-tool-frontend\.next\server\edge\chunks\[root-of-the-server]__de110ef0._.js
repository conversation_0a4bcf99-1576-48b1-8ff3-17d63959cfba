(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__de110ef0._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// middleware.ts
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
;
;
// Create the internationalization middleware with improved configuration
const intlMiddleware = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])({
    locales: [
        "en",
        "ne"
    ],
    defaultLocale: "en",
    localePrefix: "as-needed",
    // Enable locale detection from headers and cookies
    localeDetection: true,
    // Add alternate links for SEO
    alternateLinks: true
});
function middleware(request) {
    const pathname = request.nextUrl.pathname;
    // Skip middleware for static assets and API routes
    const shouldSkipMiddleware = [
        "/favicon.ico",
        "/images",
        "/fonts",
        "/api",
        "/_next",
        "/edit-submission",
        "/form-submission"
    ].some((path)=>pathname.startsWith(path)) || pathname.match(/\.(jpg|jpeg|png|gif|svg|css|js|ico|woff|woff2|ttf|eot)$/);
    if (shouldSkipMiddleware) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // Handle internationalization first
    const intlResponse = intlMiddleware(request);
    // If intl middleware returns a redirect (e.g., adding locale prefix), return it immediately
    if (intlResponse.status !== 200) {
        return intlResponse;
    }
    // Extract locale from the pathname more reliably
    const localeMatch = pathname.match(/^\/(en|ne)(?:\/|$)/);
    const locale = localeMatch ? localeMatch[1] : "en"; // Default to 'en' if no locale found
    // Extract the path without locale prefix for auth checks
    const pathWithoutLocale = pathname.replace(/^\/(en|ne)/, "") || "/";
    // Get token from cookies
    const token = request.cookies.get("token")?.value;
    // Define auth pages (pages that don't require authentication)
    const authPages = [
        "/",
        "/signup",
        "/reset-password",
        "/reset-password/change-password"
    ];
    const isAuthPage = authPages.includes(pathWithoutLocale);
    // Special case for form-test sign-in - allow access regardless of auth status
    if (pathname.startsWith("/form-submission") && pathname.includes("/sign-in")) {
        return intlResponse; // Return the intl response to maintain locale handling
    }
    // Validate token
    let isValidToken = false;
    if (token) {
        try {
            // Parse JWT token to check expiry
            const tokenData = JSON.parse(atob(token.split(".")[1]));
            const expiry = tokenData.exp * 1000; // Convert to milliseconds
            isValidToken = Date.now() < expiry;
        } catch (error) {
            // If token parsing fails, consider it invalid
            console.warn("Invalid token format:", error);
            isValidToken = false;
        }
    }
    // Skip auth checks for special routes but maintain locale handling
    if (pathWithoutLocale.startsWith("/form-submission") || pathWithoutLocale.startsWith("/edit-submission") || pathWithoutLocale.startsWith("/test-page")) {
        return intlResponse;
    }
    // Redirect authenticated users away from auth pages
    if (isValidToken && isAuthPage) {
        const url = request.nextUrl.clone();
        url.pathname = `/${locale}/dashboard`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
    }
    // Redirect unauthenticated users to login page
    if (!isValidToken && !isAuthPage) {
        const url = request.nextUrl.clone();
        url.pathname = `/${locale}/`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
    }
    // All checks passed, continue with the intl response
    return intlResponse;
}
const config = {
    // Match all paths except static assets and API routes
    matcher: [
        // Include all paths except static files and API routes
        "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__de110ef0._.js.map

import React, { useEffect, useState, useRef } from "react";
import Modal from "./Modal";
import { FieldVal<PERSON>, FormProvider, useForm } from "react-hook-form";
import { Select } from "../general/Select";
import { labelToKey } from "@/lib/labelToKey";
import { InputTypeMap } from "@/constants/inputType";
import { Switch } from "../ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { addQuestion } from "@/lib/api/form-builder";
import { DynamicOptions } from "../form-builder/DynamicOptions";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { needsOptions } from "@/lib/needsOptions";
import { QuestionSchema } from "@/types/formBuilder";
import { ContextType } from "@/types";
import { LoadingOverlay } from "../general/LoadingOverlay";
import { TableQuestionBuilder } from "../form-builder/TableQuestionBuilder";
import {
  BiImport,
  BiUpload,
  BiTrash,
  BiDownload,
  BiCheckCircle,
  BiErrorCircle,
} from "react-icons/bi";
import * as XLSX from "xlsx"; // Import XLSX for client-side validation
import { useTranslations } from "next-intl";


// File validation utility
const validateExcelFile = (
  file: File
): Promise<{ isValid: boolean; error?: string }> => {
  return new Promise((resolve) => {
    const validTypes = [
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel",
    ];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      resolve({
        isValid: false,
        error: "Please select a valid Excel file (.xlsx or .xls)",
      });
      return;
    }

    if (file.size > maxSize) {
      resolve({ isValid: false, error: "File size must be less than 5MB" });
      return;
    }

    // Client-side Excel content validation
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const rows = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
        }) as any[][];

        if (rows.length < 2) {
          resolve({
            isValid: false,
            error: "Excel file is empty or has no valid data",
          });
          return;
        }

        const headers = rows[0].map((h: any) => h?.toString().trim());
        const expectedHeaders = ["label", "code", "Next Question ID"];
        if (!headers[0]?.includes("label") || !headers[1]?.includes("code")) {
          resolve({
            isValid: false,
            error:
              "Invalid Excel format: Missing required headers (Label, Code)",
          });
          return;
        }

        const dataRows = rows.slice(1);
        if (dataRows.length === 0) {
          resolve({
            isValid: false,
            error: "Excel file contains no valid options",
          });
          return;
        }

        for (let i = 0; i < dataRows.length; i++) {
          const row = dataRows[i];
          if (!row[0] || !row[1]) {
            resolve({
              isValid: false,
              error: `Invalid data in row ${
                i + 2
              }: Label and Code are required`,
            });
            return;
          }
        }

        resolve({ isValid: true });
      } catch (error) {
        resolve({ isValid: false, error: "Failed to parse Excel file" });
      }
    };
    reader.onerror = () => {
      resolve({ isValid: false, error: "Error reading Excel file" });
    };
    reader.readAsArrayBuffer(file);
  });
};

// File upload status component
const FileUploadStatus = ({
  file,
  onRemove,
  error,
}: {
  file: File;
  onRemove: () => void;
  error?: string;
}) => (
  <div
    className={`flex items-center justify-between p-3 rounded-lg ${
      error
        ? "bg-red-50 border border-red-200"
        : "bg-green-50 border border-green-200"
    }`}
  >
    <div className="flex items-center space-x-2">
      {error ? (
        <BiErrorCircle className="text-red-500" />
      ) : (
        <BiCheckCircle className="text-green-500" />
      )}
      <div>
        <span className="text-sm font-medium">{file.name}</span>
        <div className="text-xs text-gray-500">
          {(file.size / 1024).toFixed(1)} KB
        </div>
        {error && <div className="text-xs text-red-600">{error}</div>}
      </div>
    </div>
    <button
      type="button"
      onClick={onRemove}
      className="text-red-500 hover:text-red-700 p-1"
      title="Remove file"
    >
      <BiTrash />
    </button>
  </div>
);

// Download template function
const downloadExcelTemplate = () => {
  const csvContent =
    "Label,Code,Next Question ID\nOption 1,opt1,\nOption 2,opt2,\nOption 3,opt3,";
  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "question_options_template.csv";
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};

// Confirmation dialog component
const ConfirmationDialog = ({
  isOpen,
  onConfirm,
  onCancel,
}: {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  if (!isOpen) return null;
  const t = useTranslations();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-lg text-neutral-700 font-semibold mb-4">
          {t('unsavedChanges')}
        </h2>
        <p className="mb-6 text-neutral-700">
          {t('unsavedChangesWarning')}
        </p>
        <div className="flex justify-end space-x-3">
          <button onClick={onCancel} className="btn-outline">
            {t('cancel')}
          </button>
          <button onClick={onConfirm} className="btn-danger">
            {t('discardChanges')}
          </button>
        </div>
      </div>
    </div>
  );
};

const AddQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  contextId,
  position,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  contextId: number;
  position?: number;
}) => {
  const methods = useForm<z.infer<typeof QuestionSchema>>({
    resolver: zodResolver(QuestionSchema),
    defaultValues: {
      label: "",
      inputType: "",
      hint: "",
      placeholder: "",
      questionOptions: [],
    },
  });

  const t = useTranslations();


  const {
    register,
    formState: { errors, isSubmitted, isDirty },
    setValue,
    handleSubmit,
    reset,
    watch,
  } = methods;

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadMethod, setUploadMethod] = useState<"form" | "excel">("form");
  const [fileError, setFileError] = useState<string>("");
  const [isRequired, setIsRequired] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const queryClient = useQueryClient();
  const queryKey =
    contextType === "project"
      ? ["questions", contextId]
      : contextType === "template"
      ? ["templateQuestions", contextId]
      : ["questionBlockQuestions", contextId];

  const questionMutation = useMutation({
    mutationFn: addQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      // Also invalidate form builder data for project contexts
      if (contextType === "project") {
        queryClient.invalidateQueries({ queryKey: ["formBuilderData", contextId] });
      }
      handleClose();
    },
    onError: (error: any) => {
      setFileError(error.message || "Failed to add question");
      setIsLoading(false);
    },
  });

  useEffect(() => {
    register("inputType", { required: t('selectInputType') });
  }, [register]);

  useEffect(() => {
    setValue("inputType", selectedInputType, { shouldValidate: isSubmitted });
  }, [selectedInputType, setValue, isSubmitted]);

  useEffect(() => {
    if (showModal) {
      setIsLoading(false);
    }
  }, [showModal]);

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = await validateExcelFile(file);
    if (!validation.isValid) {
      setFileError(validation.error || "Invalid file");
      setSelectedFile(null);
      return;
    }

    setFileError("");
    setSelectedFile(file);
  };

  const removeFile = () => {
    setSelectedFile(null);
    setFileError("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleUploadMethodChange = (method: "form" | "excel") => {
    setUploadMethod(method);
    if (method === "form") {
      removeFile();
    }
  };

  const hasUnsavedChanges = () => {
    const questionOptions = watch("questionOptions");
    return (
      isDirty ||
      !!watch("label") ||
      !!watch("hint") ||
      !!watch("placeholder") ||
      !!selectedInputType ||
      !!selectedFile ||
      (questionOptions &&
        Array.isArray(questionOptions) &&
        questionOptions.length > 0)
    );
  };

  const handleCloseWithConfirmation = () => {
    if (hasUnsavedChanges()) {
      setShowConfirmation(true);
    } else {
      handleClose();
    }
  };

  const handleClose = () => {
    reset();
    setSelectedInputType("");
    setSelectedFile(null);
    setUploadMethod("form");
    setFileError("");
    setIsLoading(false);
    setShowConfirmation(false);
    setShowModal(false);
  };

  const onSubmit = async (data: FieldValues) => {
    if (isLoading) return;

    // If it's a table question, let the TableQuestionBuilder handle it
    if (selectedInputType === "table") {
      const tableBuilder = document.querySelector(".table-question-builder");
      if (tableBuilder) {
        tableBuilder.dispatchEvent(new CustomEvent("submitTable"));
      } else {
        console.error("TableQuestionBuilder not found");
      }
      return;
    }

    // Check if this input type requires options
    const inputTypeNeedsOptions = needsOptions(selectedInputType);

    // Validate based on upload method
    if (inputTypeNeedsOptions) {
      if (uploadMethod === "excel") {
        // For Excel upload, validate file is selected
        if (!selectedFile) {
          setFileError("Please select an Excel file");
          return;
        }
        // If file is selected, we don't need to validate questionOptions
        // as they will come from the file
      } else {
        // For manual entry, validate options exist
        const options = data.questionOptions || [];
        if (options.length === 0) {
          methods.setError("questionOptions", {
            type: "custom",
            message: t('atLeastOneOptionRequired'),
          });
          return;
        }

        // Validate each option has required fields
      }
    }

    setIsLoading(true);
    const fileToSend = selectedFile ?? undefined;
    const dataToSend = {
      label: data.label,
      isRequired,
      hint: data.hint,
      placeholder: data.placeholder,
      inputType: selectedInputType,
      // Only include questionOptions for manual entry
      questionOptions:
        uploadMethod === "form" ? data.questionOptions : undefined,
      // Only include file for Excel upload
      file: uploadMethod === "excel" ? fileToSend : undefined,
    };

    questionMutation.mutate({
      contextType,
      contextId,
      dataToSend,
      position,
    });
  };

  const inputTypeNeedsOptions = needsOptions(selectedInputType);

  return (
    <>
      <Modal
        isOpen={showModal}
        onClose={handleCloseWithConfirmation}
        className="w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6"
      >
        <h1 className="heading-text capitalize mb-4">{t('addQuestion')}</h1>

        {questionMutation.isPending && <LoadingOverlay />}

        <FormProvider {...methods}>
          <form
            className="space-y-4 max-h-[500px] overflow-y-auto p-4"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="label-input-group group ">
              <input
                {...register("label", {
                  required: t('questionNameRequired'),
                })}
                className="input-field"
                placeholder= {t('enterQuestion')}
               
              />
              {errors.label && (
                <p className="text-sm text-red-500">{`${errors.label.message}`}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="label-input-group group">
                <label htmlFor="question-type" className="label-text">
                  {t('inputType')}
                </label>
                <div className="mt-1">
                  <Select
                    id="question-type"
                    options={Object.values(InputTypeMap)}
                    value={
                      selectedInputType && InputTypeMap[selectedInputType]
                        ? InputTypeMap[selectedInputType]
                        : t('selectOption')
                    }
                    onChange={(label) => {
                      const selectedKey = labelToKey(label, InputTypeMap);
                      setSelectedInputType(selectedKey ?? "");
                      setUploadMethod("form");
                      removeFile();
                    }}
                  />
                </div>
                {errors.inputType && (
                  <p className="text-sm text-red-500">{`${errors.inputType.message}`}</p>
                )}
              </div>

              <div className="flex items-end">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="required"
                    checked={isRequired}
                    onCheckedChange={() => setIsRequired((prev) => !prev)}
                    className="data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"
                  />
                  <label htmlFor="required" className="label-text">
                    {t('required')}
                  </label>
                </div>
              </div>
            </div>

            <div className="label-input-group group">
              <label htmlFor="hint" className="label-text">
                {t('helpText')}
              </label>
              <textarea
                {...register("hint")}
                id="hint"
                placeholder= {t('helpTextHint')}
                className="input-field resize-none"
              />
            </div>

            <div className="label-input-group group">
              <label htmlFor="placeholder" className="label-text">
                {t('placeholderText')}
              </label>
              <input
                {...register("placeholder")}
                id="placeholder"
                placeholder={t('placeholderHint')}
                className="input-field"
              />
            </div>

            {inputTypeNeedsOptions && (
              <div className="space-y-4">
                <div className="label-input-group group">
                  <label className="label-text">{t('questionOptions')}</label>

                  <div className="flex space-x-4 mb-4">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        value="form"
                        checked={uploadMethod === "form"}
                        onChange={(e) =>
                          handleUploadMethodChange(
                            e.target.value as "form" | "excel"
                          )
                        }
                        className="text-primary-500"
                      />
                      <span>{t('manualEntry')}</span>
                    </label>
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        value="excel"
                        checked={uploadMethod === "excel"}
                        onChange={(e) =>
                          handleUploadMethodChange(
                            e.target.value as "form" | "excel"
                          )
                        }
                        className="text-primary-500"
                      />
                      <span>{t('excelUpload')}</span>
                    </label>
                  </div>

                  {uploadMethod === "excel" && (
                    <div className="space-y-3">
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors">
                        <div className="text-center">
                          <BiUpload className="mx-auto h-12 w-12 text-gray-400" />
                          <div className="mt-2">
                            <label
                              htmlFor="excel-file"
                              className="cursor-pointer"
                            >
                              <span className="mt-2 block text-sm font-medium text-gray-900">
                                {t('uploadExcel')}
                              </span>
                              <span className="mt-1 block text-xs text-gray-500">
                                {t('supportedFormats')}: .xlsx, .xls (max 5MB)
                              </span>
                            </label>
                            <input
                              ref={fileInputRef}
                              id="excel-file"
                              type="file"
                              accept=".xlsx,.xls"
                              onChange={handleFileSelect}
                              className="sr-only"
                            />
                          </div>
                          <div className="mt-3 flex justify-center space-x-2">
                            <button
                              type="button"
                              onClick={() => fileInputRef.current?.click()}
                              className="btn-outline inline-flex items-center"
                            >
                              <BiImport className="mr-2" />
                              {t('chooseExcel')}
                            </button>
                            <button
                              type="button"
                              onClick={downloadExcelTemplate}
                              className="btn-outline inline-flex items-center"
                              title={t('downloadTemplate')}
                            >
                              <BiDownload className="mr-2" />
                              {t('downloadTemplate')}
                            </button>
                          </div>
                        </div>
                      </div>

                      {selectedFile && (
                        <FileUploadStatus
                          file={selectedFile}
                          onRemove={removeFile}
                          error={fileError}
                        />
                      )}

                      {fileError && !selectedFile && (
                        <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          {fileError}
                        </div>
                      )}

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800 font-medium mb-2">
                          {t('excelFormatRequirements')}
                        </p>
                        <ul className="text-xs text-blue-700 space-y-1">
                          <li>
                            • {t('columnA')}: <strong>{t('label')}</strong> ({t('required')}) -
                            {t('optionDisplayText')}
                          </li>
                          <li>
                            • {t('columnB')}: <strong>{t('code')}</strong> ({t('required')}) -
                            {t('uniqueIdentifiers')}
                          </li>
                          <li>
                            • {t('ColumnC')}: <strong>{t('nextQuestionId')}</strong>{" "}
                            ({t('optional')}) - {t('forConditionalLogic')}
                          </li>
                          <li>• {t('firstRowHeaders')}</li>
                          <li>• {t('eachRowOption')}</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {uploadMethod === "form" && (
                    <DynamicOptions
                      contextType={contextType}
                      contextId={contextId}
                      inputType={selectedInputType}
                    />
                  )}
                </div>
              </div>
            )}

            {selectedInputType === "table" && (
              <div className="mt-4">
                <TableQuestionBuilder
                  projectId={contextId}
                  isInModal={true}
                  onTableCreated={(tableId) => {
                    if (tableId !== -1) {
                      queryClient.invalidateQueries({ queryKey });
                      // Also invalidate form builder data for project contexts
                      if (contextType === "project") {
                        queryClient.invalidateQueries({ queryKey: ["formBuilderData", contextId] });
                      }
                    }
                    handleClose();
                  }}
                />
              </div>
            )}

            <div className="flex items-center justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={handleCloseWithConfirmation}
                className="btn-outline"
                disabled={isLoading}
              >
                {t('cancel')}
              </button>
              <button
                type="submit"
                className="btn-primary flex items-center justify-center gap-2"
                onClick={(e) => {
                  if (selectedInputType === "table") {
                    e.preventDefault();
                    const tableBuilder = document.querySelector(
                      ".table-question-builder"
                    );
                    if (tableBuilder) {
                      tableBuilder.dispatchEvent(
                        new CustomEvent("submitTable")
                      );
                    }
                  }
                }}
                disabled={
                  isLoading ||
                  (uploadMethod === "excel" && (!selectedFile || !!fileError))
                }
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                    {t('saving')}...
                  </>
                ) : (
                  t('save')
                )}
              </button>
            </div>
          </form>
        </FormProvider>
      </Modal>

      <ConfirmationDialog
        isOpen={showConfirmation}
        onConfirm={handleClose}
        onCancel={() => setShowConfirmation(false)}
      />
    </>
  );
};

export { AddQuestionModal };
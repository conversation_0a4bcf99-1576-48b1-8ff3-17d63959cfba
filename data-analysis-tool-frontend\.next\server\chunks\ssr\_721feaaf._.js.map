{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/project/%5BhashedId%5D/data/page.tsx"], "sourcesContent": ["import { redirect } from \"next/navigation\";\r\n\r\nexport default function Page() {\r\n  redirect(\"data/table\");\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS;IACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACT,OAAO;AACT", "debugId": null}}]}
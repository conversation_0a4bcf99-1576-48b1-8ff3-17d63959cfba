{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/addUser/AddUser.tsx"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { addProjectUser, checkUserExists } from \"@/lib/api/projects\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface AddUserProps {\r\n  onClose: () => void; // Callback when the modal/dialog closes\r\n  projectId?: number; // Project ID to which the user will be added\r\n  onUserAdded?: () => void; // Callback after successful addition\r\n}\r\n\r\nconst AddUser = ({ onClose, projectId, onUserAdded }: AddUserProps) => {\r\n  const t = useTranslations();\r\n  \r\n  // Move permissions array inside component where we can use t()\r\n  const permissions = [\r\n    { label: t('viewForm'), value: \"viewForm\" },\r\n    { label: t('editForm'), value: \"editForm\" },\r\n    { label: t('viewSubmissions'), value: \"viewSubmissions\" },\r\n    { label: t('editSubmissions'), value: \"editSubmissions\" },\r\n    { label: t('addSubmissions'), value: \"addSubmissions\" },\r\n    { label: t('deleteSubmissions'), value: \"deleteSubmissions\" },\r\n    { label: t('validateSubmissions'), value: \"validateSubmissions\" },\r\n    { label: t('manageProject'), value: \"manageProject\" },\r\n  ];\r\n\r\n  const [email, setEmail] = useState(\"\");\r\n  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);\r\n  const [error, setError] = useState(\"\");\r\n  const [isVerifying, setIsVerifying] = useState(false);\r\n  const [userExists, setUserExists] = useState<boolean | null>(null);\r\n\r\n  const queryClient = useQueryClient(); // Used to invalidate cached project user list\r\n  const dispatch = useDispatch(); // Redux dispatch for showing notifications\r\n  const emailCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Debounce timer ref\r\n\r\n  // Email validation using basic but reliable regex\r\n  const isValidEmail = (email: string) => {\r\n    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/.test(email);\r\n  };\r\n\r\n  // Toggle permissions when checkboxes are clicked\r\n  const handlePermissionChange = (value: string) => {\r\n    setSelectedPermissions(\r\n      (prev) =>\r\n        prev.includes(value)\r\n          ? prev.filter((v) => v !== value) // remove if already selected\r\n          : [...prev, value] // add if not selected\r\n    );\r\n  };\r\n\r\n  // Mutation to verify if user with provided email exists\r\n  const checkUserExistsMutation = useMutation({\r\n    mutationFn: checkUserExists,\r\n    onSuccess: () => {\r\n      setUserExists(true);\r\n      setError(\"\");\r\n    },\r\n    onError: (error: any) => {\r\n      setUserExists(false);\r\n      const errorMessage =\r\n        error.response?.data?.message || t('userNotFound');\r\n      setError(errorMessage);\r\n    },\r\n    onSettled: () => {\r\n      setIsVerifying(false);\r\n    },\r\n  });\r\n\r\n  // Called on each email input change\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newEmail = e.target.value;\r\n    setEmail(newEmail);\r\n    setUserExists(null);\r\n    setError(\"\");\r\n\r\n    if (!newEmail) return;\r\n\r\n    if (!isValidEmail(newEmail)) {\r\n      setError(t('invalidEmail'));\r\n      return;\r\n    }\r\n\r\n    // Cancel any previously scheduled user check\r\n    if (emailCheckTimeoutRef.current) {\r\n      clearTimeout(emailCheckTimeoutRef.current);\r\n    }\r\n\r\n    // Debounced call to verify email after 800ms\r\n    emailCheckTimeoutRef.current = setTimeout(() => {\r\n      setIsVerifying(true);\r\n      checkUserExistsMutation.mutate(newEmail);\r\n    }, 800);\r\n  };\r\n\r\n  // Form validation logic\r\n  const isFormValid = () => {\r\n    if (!email) {\r\n      setError(t('emailRequired'));\r\n      return false;\r\n    }\r\n    if (!isValidEmail(email)) {\r\n      setError(t('invalidEmail'));\r\n      return false;\r\n    }\r\n    if (!userExists) {\r\n      setError(t('userNotFound'));\r\n      return false;\r\n    }\r\n    if (selectedPermissions.length === 0) {\r\n      setError(t('selectPermission'));\r\n      return false;\r\n    }\r\n    setError(\"\");\r\n    return true;\r\n  };\r\n\r\n  // Mutation to add user to project with selected permissions\r\n  const addUserMutation = useMutation({\r\n    mutationFn: () => {\r\n      // Convert permission array to object format required by API\r\n      const permissionsObject = selectedPermissions.reduce(\r\n        (accumulator, permission) => {\r\n          accumulator[permission] = true;\r\n          return accumulator;\r\n        },\r\n        {} as Record<string, boolean>\r\n      );\r\n\r\n      return addProjectUser({\r\n        projectId: projectId!,\r\n        email,\r\n        permissions: permissionsObject,\r\n      });\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate cached project users to refetch fresh list\r\n      queryClient.invalidateQueries({ queryKey: [\"projectUsers\", projectId] });\r\n      dispatch(\r\n        showNotification({\r\n          message: t('userAdded'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      if (onUserAdded) onUserAdded();\r\n      onClose();\r\n    },\r\n    onError: (error: any) => {\r\n      // Handle and display error message from API or fallback\r\n      let errorMessage: string;\r\n      if (typeof error === \"string\") {\r\n        errorMessage = error;\r\n      } else if (error instanceof Error) {\r\n        errorMessage = error.message;\r\n      } else if (error.response?.data?.message) {\r\n        errorMessage =\r\n          typeof error.response.data.message === \"object\"\r\n            ? JSON.stringify(error.response.data.message)\r\n            : error.response.data.message;\r\n      } else {\r\n        errorMessage = t('failedToAddUser');\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: errorMessage,\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setError(errorMessage);\r\n    },\r\n  });\r\n\r\n  // Final submit handler for adding the user\r\n  const handleSubmit = () => {\r\n    if (!projectId) {\r\n      setError(t('projectIdRequired'));\r\n      return;\r\n    }\r\n\r\n    if (isFormValid()) {\r\n      addUserMutation.mutate();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 p-6 rounded-md\">\r\n      {/* Email input with validation */}\r\n      <div className=\"relative\">\r\n        <input\r\n          className={`w-full border ${\r\n            error ? \"border-red-500\" : \"border-neutral-300\"\r\n          } rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`}\r\n          placeholder={t('email')}\r\n          value={email}\r\n          onChange={handleEmailChange}\r\n        />\r\n        {/* Close button for modal */}\r\n        <button\r\n          className=\"absolute right-2 top-2 text-neutral-700 hover:text-neutral-900\"\r\n          onClick={onClose}\r\n          type=\"button\"\r\n        >\r\n          <X size={22} />\r\n        </button>\r\n        {/* Status messages */}\r\n        {isVerifying && (\r\n          <p className=\"text-neutral-500 text-sm mb-2\">{t('verifyingEmail')}</p>\r\n        )}\r\n        {userExists === true && (\r\n          <p className=\"text-green-500 text-sm mb-2\">{t('userFound')}</p>\r\n        )}\r\n        {error && <p className=\"text-red-500 text-sm mb-2\">{error}</p>}\r\n      </div>\r\n\r\n      {/* Permissions checkboxes */}\r\n      <div className=\"flex flex-col gap-2\">\r\n        {permissions.map((permission) => (\r\n          <div key={permission.value} className=\"flex flex-col\">\r\n            <label className=\"flex items-center gap-2\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPermissions.includes(permission.value)}\r\n                onChange={() => handlePermissionChange(permission.value)}\r\n              />\r\n              {permission.label}\r\n            </label>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Submit button */}\r\n      <button\r\n        className={`mt-6 ${\r\n          addUserMutation.isPending || isVerifying\r\n            ? \"bg-neutral-400\"\r\n            : \"bg-blue-400 hover:bg-blue-500\"\r\n        } text-white px-6 py-2 rounded disabled:opacity-50`}\r\n        disabled={\r\n          addUserMutation.isPending ||\r\n          isVerifying ||\r\n          !email ||\r\n          selectedPermissions.length === 0 ||\r\n          !userExists\r\n        }\r\n        onClick={handleSubmit}\r\n      >\r\n        {addUserMutation.isPending ? t('adding') : t('grantPermissions')}\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { AddUser };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAQA,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAgB;;IAChE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,+DAA+D;IAC/D,MAAM,cAAc;QAClB;YAAE,OAAO,EAAE;YAAa,OAAO;QAAW;QAC1C;YAAE,OAAO,EAAE;YAAa,OAAO;QAAW;QAC1C;YAAE,OAAO,EAAE;YAAoB,OAAO;QAAkB;QACxD;YAAE,OAAO,EAAE;YAAoB,OAAO;QAAkB;QACxD;YAAE,OAAO,EAAE;YAAmB,OAAO;QAAiB;QACtD;YAAE,OAAO,EAAE;YAAsB,OAAO;QAAoB;QAC5D;YAAE,OAAO,EAAE;YAAwB,OAAO;QAAsB;QAChE;YAAE,OAAO,EAAE;YAAkB,OAAO;QAAgB;KACrD;IAED,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE7D,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,KAAK,8CAA8C;IACpF,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,KAAK,2CAA2C;IAC3E,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB,OAAO,qBAAqB;IAEvF,kDAAkD;IAClD,MAAM,eAAe,CAAC;QACpB,OAAO,mDAAmD,IAAI,CAAC;IACjE;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,CAAC;QAC9B,uBACE,CAAC,OACC,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAC,IAAM,MAAM,OAAO,6BAA6B;eAC7D;mBAAI;gBAAM;aAAM,CAAC,sBAAsB;;IAEjD;IAEA,wDAAwD;IACxD,MAAM,0BAA0B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,yHAAA,CAAA,kBAAe;QAC3B,SAAS;4DAAE;gBACT,cAAc;gBACd,SAAS;YACX;;QACA,OAAO;4DAAE,CAAC;gBACR,cAAc;gBACd,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WAAW,EAAE;gBACrC,SAAS;YACX;;QACA,SAAS;4DAAE;gBACT,eAAe;YACjB;;IACF;IAEA,oCAAoC;IACpC,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,cAAc;QACd,SAAS;QAET,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,aAAa,WAAW;YAC3B,SAAS,EAAE;YACX;QACF;QAEA,6CAA6C;QAC7C,IAAI,qBAAqB,OAAO,EAAE;YAChC,aAAa,qBAAqB,OAAO;QAC3C;QAEA,6CAA6C;QAC7C,qBAAqB,OAAO,GAAG,WAAW;YACxC,eAAe;YACf,wBAAwB,MAAM,CAAC;QACjC,GAAG;IACL;IAEA,wBAAwB;IACxB,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO;YACV,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,CAAC,aAAa,QAAQ;YACxB,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,CAAC,YAAY;YACf,SAAS,EAAE;YACX,OAAO;QACT;QACA,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,SAAS,EAAE;YACX,OAAO;QACT;QACA,SAAS;QACT,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAClC,UAAU;oDAAE;gBACV,4DAA4D;gBAC5D,MAAM,oBAAoB,oBAAoB,MAAM;8EAClD,CAAC,aAAa;wBACZ,WAAW,CAAC,WAAW,GAAG;wBAC1B,OAAO;oBACT;6EACA,CAAC;gBAGH,OAAO,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;oBACpB,WAAW;oBACX;oBACA,aAAa;gBACf;YACF;;QACA,SAAS;oDAAE;gBACT,wDAAwD;gBACxD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAgB;qBAAU;gBAAC;gBACtE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,IAAI,aAAa;gBACjB;YACF;;QACA,OAAO;oDAAE,CAAC;gBACR,wDAAwD;gBACxD,IAAI;gBACJ,IAAI,OAAO,UAAU,UAAU;oBAC7B,eAAe;gBACjB,OAAO,IAAI,iBAAiB,OAAO;oBACjC,eAAe,MAAM,OAAO;gBAC9B,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;oBACxC,eACE,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,WACnC,KAAK,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAC1C,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACnC,OAAO;oBACL,eAAe,EAAE;gBACnB;gBAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,SAAS;YACX;;IACF;IAEA,2CAA2C;IAC3C,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW;YACd,SAAS,EAAE;YACX;QACF;QAEA,IAAI,eAAe;YACjB,gBAAgB,MAAM;QACxB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,CAAC,cAAc,EACxB,QAAQ,mBAAmB,qBAC5B,2GAA2G,CAAC;wBAC7G,aAAa,EAAE;wBACf,OAAO;wBACP,UAAU;;;;;;kCAGZ,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,MAAK;kCAEL,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;oBAGV,6BACC,6LAAC;wBAAE,WAAU;kCAAiC,EAAE;;;;;;oBAEjD,eAAe,sBACd,6LAAC;wBAAE,WAAU;kCAA+B,EAAE;;;;;;oBAE/C,uBAAS,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAItD,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;wBAA2B,WAAU;kCACpC,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,oBAAoB,QAAQ,CAAC,WAAW,KAAK;oCACtD,UAAU,IAAM,uBAAuB,WAAW,KAAK;;;;;;gCAExD,WAAW,KAAK;;;;;;;uBAPX,WAAW,KAAK;;;;;;;;;;0BAc9B,6LAAC;gBACC,WAAW,CAAC,KAAK,EACf,gBAAgB,SAAS,IAAI,cACzB,mBACA,gCACL,iDAAiD,CAAC;gBACnD,UACE,gBAAgB,SAAS,IACzB,eACA,CAAC,SACD,oBAAoB,MAAM,KAAK,KAC/B,CAAC;gBAEH,SAAS;0BAER,gBAAgB,SAAS,GAAG,EAAE,YAAY,EAAE;;;;;;;;;;;;AAIrD;GAhPM;;QACM,yMAAA,CAAA,kBAAe;QAoBL,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAmBI,iLAAA,CAAA,cAAW;QAkEnB,iLAAA,CAAA,cAAW;;;KA3G/B", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ShareProjectModal.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { LuPlus } from \"react-icons/lu\";\r\nimport { Project } from \"@/types\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { AddUser } from \"../addUser/AddUser\";\r\nimport axios from \"@/lib/axios\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst ShareProjectModal = ({\r\n  showModal,\r\n  onClose,\r\n  onShare,\r\n  selectedProject,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onShare: () => void;\r\n  selectedProject?: Project;\r\n  selectedProjects?: Project[];\r\n}) => {\r\n  const { hashedId } = useParams();\r\n  const { user } = useAuth();\r\n  const [showAddUser, setShowAddUser] = useState(false);\r\n\r\n  // Get project ID from either selected project or URL\r\n  const urlProjectId = hashedId ? decode(hashedId as string) : null;\r\n  const projectId = selectedProject?.id || urlProjectId;\r\n  const t = useTranslations();\r\n\r\n  // Fetch project details using the project ID\r\n  const { data: projectData, isLoading: projectLoading } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: async () => {\r\n      const data = await fetchProjectById({ projectId: projectId! });\r\n      return data;\r\n    },\r\n    enabled: !!projectId && !!user?.id,\r\n  });\r\n\r\n  // Fetch project users\r\n  const [projectUsers, setProjectUsers] = useState([]);\r\n  const [usersLoading, setUsersLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      if (!projectId) return;\r\n\r\n      setUsersLoading(true);\r\n      try {\r\n        const response = await axios.get(`/project-users/${projectId}`);\r\n\r\n        if (response.data && response.data.data && response.data.data.AllUser) {\r\n          const users = response.data.data.AllUser || [];\r\n          setProjectUsers(users);\r\n        } else {\r\n          console.warn(\"No users data in response:\", response.data);\r\n          setProjectUsers([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching project users:\", error);\r\n        setProjectUsers([]);\r\n      } finally {\r\n        setUsersLoading(false);\r\n      }\r\n    };\r\n\r\n    if (showModal && projectId) {\r\n      fetchUsers();\r\n    }\r\n  }, [projectId, showModal]);\r\n\r\n  // If loading, show spinner\r\n  if (projectLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // Use the fetched project data or fallback to selected project\r\n  const displayData = projectData || selectedProject;\r\n\r\n  // If we have no data at all, show error\r\n  if (!displayData) {\r\n    return (\r\n      <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n        <div className=\"text-center py-4\">\r\n          <p className=\"text-red-500\">{t('projectNotFound')}</p>\r\n          <Button onClick={onClose} className=\"mt-4\">\r\n            {t('close')}\r\n          </Button>\r\n        </div>\r\n      </Modal>\r\n    );\r\n  }\r\n\r\n  // Generate avatar color based on user name\r\n  const getAvatarColor = (name?: string) => {\r\n    if (!name) return \"bg-gray-500\"; // Default color if no name provided\r\n    const colors = [\r\n      \"bg-green-500\",\r\n      \"bg-blue-500\",\r\n      \"bg-red-500\",\r\n      \"bg-purple-500\",\r\n      \"bg-yellow-500\",\r\n      \"bg-pink-500\",\r\n      \"bg-indigo-500\",\r\n      \"bg-orange-500\",\r\n    ];\r\n    const charCode = name.charCodeAt(0);\r\n    return colors[charCode % colors.length];\r\n  };\r\n\r\n  // Get the first letter of the name for the avatar\r\n  const getInitial = (name?: string) => {\r\n    return name ? name.charAt(0).toUpperCase() : \"?\";\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={showModal} onClose={onClose} className=\"p-6 rounded-md\">\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n        {`${t('sharingProject')}: ${displayData.name || \"\"}`}\r\n      </h2>\r\n\r\n      <div className=\"w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <div className=\"text-xl font-semibold\">{t('whoHasAccess')}</div>\r\n          <div\r\n            className=\"flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50\"\r\n            onClick={() => setShowAddUser(true)}\r\n          >\r\n            <LuPlus size={18} className=\"mr-2\" />\r\n            <div className=\"text-sm\">{t('addUser')}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* User List */}\r\n        <div className=\"space-y-4\">\r\n          {/* Project Owner */}\r\n          {displayData.user && (\r\n            <div className=\"flex items-center\">\r\n              <div\r\n                className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                  displayData.user.name\r\n                )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n              >\r\n                {getInitial(displayData.user.name)}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"font-medium\">\r\n                  {displayData.user.name ||\r\n                    displayData.user.email ||\r\n                    t('unknownUser')}\r\n                </div>\r\n                <div className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\">\r\n                  {t('owner')}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Project Users */}\r\n          {usersLoading ? (\r\n            <div className=\"py-2 text-center\">\r\n              <div className=\"inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin\"></div>\r\n            </div>\r\n          ) : projectUsers && projectUsers.length > 0 ? (\r\n            projectUsers.map((projectUser: any, index: number) => {\r\n              const userName =\r\n                (projectUser.user && projectUser.user.name) ||\r\n                (projectUser.user && projectUser.user.email) ||\r\n                `User ${projectUser.userId}`;\r\n\r\n              return (\r\n                <div key={index} className=\"flex items-center mt-4\">\r\n                  <div\r\n                    className={`w-10 h-10 rounded-full ${getAvatarColor(\r\n                      userName\r\n                    )} flex items-center justify-center text-neutral-100 font-medium mr-3`}\r\n                  >\r\n                    {getInitial(userName)}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium\">{userName}</div>\r\n                    <div className=\"flex flex-wrap gap-1 mt-1\">\r\n                      {projectUser.permission &&\r\n                        Object.entries(projectUser.permission)\r\n                          .filter(([key, value]) => value === true)\r\n                          .map(([key]) => (\r\n                            <div\r\n                              key={key}\r\n                              className=\"inline-block bg-gray-100 text-xs px-2 py-0.5 rounded\"\r\n                            >\r\n                              {key === \"viewForm\"\r\n                                ? t('viewForm')\r\n                                : key === \"editForm\"\r\n                                ? t('editForm')\r\n                                : key === \"viewSubmissions\"\r\n                                ? t('viewSubmissions')\r\n                                : key === \"editSubmissions\"\r\n                                ? t('editSubmissions')\r\n                                : key === \"addSubmissions\"\r\n                                ? t('addSubmissions')\r\n                                : key === \"deleteSubmissions\"\r\n                                ? t('deleteSubmissions')\r\n                                : key === \"validateSubmissions\"\r\n                                ? t('validateSubmissions')\r\n                                : key === \"manageProject\"\r\n                                ? t('manageProject')\r\n                                : key}\r\n                            </div>\r\n                          ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })\r\n          ) : null}\r\n        </div>\r\n\r\n        {/* AddUser form */}\r\n        {showAddUser && projectId && (\r\n          <div className=\"mt-6\">\r\n            <AddUser\r\n              onClose={() => setShowAddUser(false)}\r\n              projectId={projectId}\r\n              onUserAdded={() => {\r\n                // Refetch users when a new user is added\r\n                const fetchUsers = async () => {\r\n                  setUsersLoading(true);\r\n                  try {\r\n                    const response = await axios.get(\r\n                      `/project-users/${projectId}`\r\n                    );\r\n                    if (\r\n                      response.data &&\r\n                      response.data.data &&\r\n                      response.data.data.AllUser\r\n                    ) {\r\n                      const users = response.data.data.AllUser || [];\r\n                      setProjectUsers(users);\r\n                    } else {\r\n                      setProjectUsers([]);\r\n                    }\r\n                  } catch (error) {\r\n                    console.error(\"Error fetching project users:\", error);\r\n                    setProjectUsers([]);\r\n                  } finally {\r\n                    setUsersLoading(false);\r\n                  }\r\n                };\r\n                fetchUsers();\r\n              }}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Anonymous Submissions */}\r\n        <div className=\"mt-8 border-t pt-6\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <div>\r\n              <div className=\"font-medium\">{t('anonymousSubmissions')}</div>\r\n              <div className=\"text-sm text-gray-500 mt-1\">\r\n                {t('allowAnonymousSubmissions')}\r\n              </div>\r\n            </div>\r\n            <div className=\"w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer\">\r\n              <div className=\"w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copy Team Button */}\r\n        <div className=\"mt-8\">\r\n          <div className=\"inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50\">\r\n            {t('copyTeamFromProject')}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ShareProjectModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,OAAO,EACP,eAAe,EAOhB;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qDAAqD;IACrD,MAAM,eAAe,WAAW,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE,YAAsB;IAC7D,MAAM,YAAY,iBAAiB,MAAM;IACzC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,6CAA6C;IAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QACzE,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;0CAAE;gBACP,MAAM,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;gBAC5D,OAAO;YACT;;QACA,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM;IAClC;IAEA,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;0DAAa;oBACjB,IAAI,CAAC,WAAW;oBAEhB,gBAAgB;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;wBAE9D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BACrE,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;4BAC9C,gBAAgB;wBAClB,OAAO;4BACL,QAAQ,IAAI,CAAC,8BAA8B,SAAS,IAAI;4BACxD,gBAAgB,EAAE;wBACpB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,gBAAgB,EAAE;oBACpB,SAAU;wBACR,gBAAgB;oBAClB;gBACF;;YAEA,IAAI,aAAa,WAAW;gBAC1B;YACF;QACF;sCAAG;QAAC;QAAW;KAAU;IAEzB,2BAA2B;IAC3B,IAAI,gBAAgB;QAClB,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,+DAA+D;IAC/D,MAAM,cAAc,eAAe;IAEnC,wCAAwC;IACxC,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC,iIAAA,CAAA,UAAK;YAAC,QAAQ;YAAW,SAAS;YAAS,WAAU;sBACpD,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB,EAAE;;;;;;kCAC/B,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAS;wBAAS,WAAU;kCACjC,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO,eAAe,oCAAoC;QACrE,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,WAAW,KAAK,UAAU,CAAC;QACjC,OAAO,MAAM,CAAC,WAAW,OAAO,MAAM,CAAC;IACzC;IAEA,kDAAkD;IAClD,MAAM,aAAa,CAAC;QAClB,OAAO,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK;IAC/C;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QAAC,QAAQ;QAAW,SAAS;QAAS,WAAU;;0BACpD,6LAAC;gBAAG,WAAU;0BACX,GAAG,EAAE,kBAAkB,EAAE,EAAE,YAAY,IAAI,IAAI,IAAI;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyB,EAAE;;;;;;0CAC1C,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,6LAAC;wCAAI,WAAU;kDAAW,EAAE;;;;;;;;;;;;;;;;;;kCAKhC,6LAAC;wBAAI,WAAU;;4BAEZ,YAAY,IAAI,kBACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,YAAY,IAAI,CAAC,IAAI,EACrB,mEAAmE,CAAC;kDAErE,WAAW,YAAY,IAAI,CAAC,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,YAAY,IAAI,CAAC,IAAI,IACpB,YAAY,IAAI,CAAC,KAAK,IACtB,EAAE;;;;;;0DAEN,6LAAC;gDAAI,WAAU;0DACZ,EAAE;;;;;;;;;;;;;;;;;;4BAOV,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;uCAEf,gBAAgB,aAAa,MAAM,GAAG,IACxC,aAAa,GAAG,CAAC,CAAC,aAAkB;gCAClC,MAAM,WACJ,AAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,IACzC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,KAAK,IAC3C,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE;gCAE9B,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CACC,WAAW,CAAC,uBAAuB,EAAE,eACnC,UACA,mEAAmE,CAAC;sDAErE,WAAW;;;;;;sDAEd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAe;;;;;;8DAC9B,6LAAC;oDAAI,WAAU;8DACZ,YAAY,UAAU,IACrB,OAAO,OAAO,CAAC,YAAY,UAAU,EAClC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,UAAU,MACnC,GAAG,CAAC,CAAC,CAAC,IAAI,iBACT,6LAAC;4DAEC,WAAU;sEAET,QAAQ,aACL,EAAE,cACF,QAAQ,aACR,EAAE,cACF,QAAQ,oBACR,EAAE,qBACF,QAAQ,oBACR,EAAE,qBACF,QAAQ,mBACR,EAAE,oBACF,QAAQ,sBACR,EAAE,uBACF,QAAQ,wBACR,EAAE,yBACF,QAAQ,kBACR,EAAE,mBACF;2DAnBC;;;;;;;;;;;;;;;;;mCAhBT;;;;;4BA0Cd,KACE;;;;;;;oBAIL,eAAe,2BACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,UAAO;4BACN,SAAS,IAAM,eAAe;4BAC9B,WAAW;4BACX,aAAa;gCACX,yCAAyC;gCACzC,MAAM,aAAa;oCACjB,gBAAgB;oCAChB,IAAI;wCACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,eAAe,EAAE,WAAW;wCAE/B,IACE,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,IAAI,IAClB,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,EAC1B;4CACA,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;4CAC9C,gBAAgB;wCAClB,OAAO;4CACL,gBAAgB,EAAE;wCACpB;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,iCAAiC;wCAC/C,gBAAgB,EAAE;oCACpB,SAAU;wCACR,gBAAgB;oCAClB;gCACF;gCACA;4BACF;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAe,EAAE;;;;;;sDAChC,6LAAC;4CAAI,WAAU;sDACZ,EAAE;;;;;;;;;;;;8CAGP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA/QM;;QAYiB,qIAAA,CAAA,YAAS;QACb,oHAAA,CAAA,UAAO;QAMd,yMAAA,CAAA,kBAAe;QAGgC,8KAAA,CAAA,WAAQ;;;KAtB7D", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/project/%5BhashedId%5D/settings/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { JS<PERSON>, useEffect, useState } from \"react\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport axios from \"@/lib/axios\";\r\nimport { use<PERSON>ara<PERSON>, useRouter } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { Project } from \"@/types\";\r\nimport { deployProject, fetchProjectById } from \"@/lib/api/projects\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { Select } from \"@/components/general/Select\";\r\nimport { Globe, Briefcase, FileText } from \"lucide-react\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { SectorLabelMap } from \"@/constants/sectors\";\r\nimport countries from \"@/constants/countryNames.json\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { ShareProjectModal } from \"@/components/modals/ShareProjectModal\";\r\nimport { ConfirmationModal } from \"@/components/modals/ConfirmationModal\";\r\nimport { deleteProject } from \"@/lib/api/projects\";\r\nimport { archiveProject } from \"@/lib/api/projects\";\r\nimport { useTranslations } from \"next-intl\";\r\n// import { addProjectUser } from \"@/lib/api/projects\";\r\n\r\nconst updateProject = async ({\r\n  projectId,\r\n  dataToSend,\r\n}: {\r\n  projectId: number;\r\n  dataToSend: {\r\n    name: string;\r\n    description: string;\r\n    sector: string;\r\n    country: string;\r\n  };\r\n}) => {\r\n  const { data } = await axios.patch(`/projects/${projectId}`, dataToSend);\r\n  return data;\r\n};\r\n\r\nconst ProjectSettingsPage = () => {\r\n  const [hasMounted, setHasMounted] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  const {\r\n    register,\r\n    formState: { isSubmitting, errors, isSubmitted },\r\n    handleSubmit,\r\n    setValue,\r\n    reset,\r\n  } = useForm();\r\n\r\n  const router = useRouter();\r\n  const [isDeleted, setIsDeleted] = useState(false);\r\n  const t = useTranslations();\r\n\r\n  // registering dropdown elements manually\r\n  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);\r\n  const [selectedSector, setSelectedSector] = useState<string | null>(null);\r\n  const [showShareModal, setShowShareModal] = useState(false);\r\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\r\n  const [confirmationModalContent, setConfirmationModalContent] = useState<{\r\n    title: string;\r\n    description: string | JSX.Element;\r\n    confirmButtonText: string;\r\n    confirmButtonClass: string;\r\n    onConfirm: () => void;\r\n  } | null>(null);\r\n\r\n  const handleShareModal = () => {\r\n    setShowShareModal(true);\r\n  };\r\n\r\n  useEffect(() => {\r\n    register(\"country\", { required: \"Please select a country\" });\r\n    register(\"sector\", { required: \"Please select a sector\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"country\", selectedCountry, { shouldValidate: isSubmitted });\r\n    setValue(\"sector\", selectedSector, { shouldValidate: isSubmitted });\r\n  }, [setValue, selectedCountry, selectedSector]);\r\n\r\n  // getting hashed project id and decoding it for api call\r\n  const { hashedId } = useParams();\r\n  const hashedIdString = hashedId as string;\r\n  const projectId = decode(hashedIdString);\r\n\r\n  const { user } = useAuth();\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  // Cancel queries on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (projectId && user?.id) {\r\n        queryClient.cancelQueries({\r\n          queryKey: [\"projects\", user.id, projectId],\r\n        });\r\n      }\r\n    };\r\n  }, [projectId, user?.id, queryClient]);\r\n\r\n  const {\r\n    data: projectData,\r\n    isLoading: projectLoading,\r\n    isError: projectError,\r\n  } = useQuery<Project>({\r\n    queryKey: [\"projects\", user?.id, projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId && !!user?.id && !isDeleted,\r\n  });\r\n\r\n  // populating form fields with project data\r\n  useEffect(() => {\r\n    if (projectData) {\r\n      reset({\r\n        projectName: projectData.name || \"\",\r\n        description: projectData.description || \"\",\r\n        country: projectData.country || \"\",\r\n        sector: projectData.sector || \"\",\r\n      });\r\n\r\n      setSelectedCountry(projectData.country || null);\r\n      setSelectedSector(projectData.sector || null);\r\n    }\r\n  }, [projectData, reset]);\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  // mutation function for updating project data\r\n  const projectMutation = useMutation({\r\n    mutationFn: updateProject,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"projects\", user?.id],\r\n        exact: false,\r\n      });\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectUpdated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error) => {\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            t('projectUpdateFailed') +\r\n            error.message,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  //deploy mutation\r\n  const deployProjectMutation = useMutation<unknown, Error, { isUnarchive?: boolean }>({\r\n    mutationFn: (variables) => deployProject(projectId!, variables?.isUnarchive || false),\r\n    onSuccess: (_, variables) => {\r\n      const isUnarchive = variables?.isUnarchive || false;\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n      dispatch(\r\n        showNotification({\r\n          message: isUnarchive\r\n            ? t('projectUnarchived')\r\n            : t('projectDeployed'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error: any) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectDeployFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  //mutation function for archiving project\r\n  const archiveProjectMutation = useMutation({\r\n    mutationFn: () => archiveProject(projectId!),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectArchived'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n      router.push(\"/dashboard\"); // optional: redirect\r\n\r\n      // Also invalidate the projects list\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Project archive error:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectArchiveFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // mutation function for deleting project\r\n  const deleteProjectMutation = useMutation({\r\n    mutationFn: () => deleteProject(projectId!),\r\n    onSuccess: () => {\r\n      setIsDeleted(true);\r\n      setShowConfirmationModal(false);\r\n\r\n      // Cancel all queries for this project\r\n      queryClient.cancelQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n\r\n      // Completely remove the query from the cache\r\n      queryClient.removeQueries({\r\n        queryKey: [\"projects\", user?.id, projectId],\r\n      });\r\n\r\n      // Also invalidate the projects list\r\n      queryClient.invalidateQueries({ queryKey: [\"projects\", user?.id] });\r\n\r\n      // Show success notification\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectDeleted'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Redirect after short delay for notification to be seen\r\n      setTimeout(() => {\r\n        router.push(\"/dashboard\");\r\n      }, 1000);\r\n    },\r\n    onError: (error: any) => {\r\n      setShowConfirmationModal(false);\r\n      console.error(\"Project deletion error:\", error);\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: t('projectDeleteFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleArchiveClick = () => {\r\n    setConfirmationModalContent({\r\n      title: t('confirmArchive'),\r\n      description: (\r\n        <>\r\n          {t('confirmArchiveMessage')}\r\n        </>\r\n      ),\r\n      confirmButtonText: t('archive'),\r\n      confirmButtonClass: \"btn-primary\",\r\n      onConfirm: () => {\r\n        archiveProjectMutation.mutate();\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const handleDeployClick = () => {\r\n    const isUnarchiving = projectData?.status === \"archived\";\r\n\r\n    let description = t('confirmDeployMessage');\r\n\r\n    if (projectData?.status === \"deployed\") {\r\n      description = t('confirmRedeployMessage');\r\n    } else if (projectData?.status === \"archived\") {\r\n      description = t('confirmDeployMessage');\r\n    }\r\n\r\n    setConfirmationModalContent({\r\n      title: isUnarchiving ? t('confirmUnarchive') : t('confirmDeploy'),\r\n      description,\r\n      confirmButtonText: isUnarchiving ? t('unarchive') : t('deploy'),\r\n      confirmButtonClass: \"btn-primary\",\r\n      onConfirm: () => {\r\n        deployProjectMutation.mutate({ isUnarchive: isUnarchiving });\r\n      },\r\n    });\r\n\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    projectMutation.mutate({\r\n      projectId: projectId!,\r\n      dataToSend: {\r\n        name: data.projectName,\r\n        description: data.description,\r\n        country: data.country,\r\n        sector: data.sector,\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    setConfirmationModalContent({\r\n      title: t('confirmDelete'),\r\n      description: (\r\n        <>\r\n          <p>\r\n            {t('confirmDeleteMessage')}\r\n          </p>\r\n          <ul className=\"list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700\">\r\n            <li>{t('deleteProjectWarning1')}</li>\r\n            <li>{t('deleteProjectWarning2')}</li>\r\n            <li>\r\n              {t('deleteProjectWarning3')}\r\n            </li>\r\n          </ul>\r\n        </>\r\n      ),\r\n      confirmButtonText: t('delete'),\r\n      confirmButtonClass: \"btn-danger\",\r\n      onConfirm: () => {\r\n        deleteProjectMutation.mutate();\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  // To prevent errors from showing when the component is not fully mounted.\r\n  if (!hasMounted) return null;\r\n\r\n  if (isDeleted) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  if (projectLoading) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // If hashedId is missing, show an error\r\n  if (!hashedId || projectId === null) {\r\n    return (\r\n      <div className=\"error-message\">\r\n        <h1 className=\"text-red-500\">{t('invalidProjectIdError')}</h1>\r\n        <p className=\"text-neutral-700\">\r\n          {t('invalidProjectIdMessage')}\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (projectError && !isDeleted) {\r\n    return (\r\n      <p className=\"text-red-500\">{t('fetchProjectFailed')}</p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <form className=\"flex flex-col gap-8\" onSubmit={handleSubmit(onSubmit)}>\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Project Name */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"project-name\" className=\"label-text\">\r\n            <FileText size={16} /> {t('projectName')}\r\n          </label>\r\n          <input\r\n            {...register(\"projectName\", {\r\n              required: t('projectNameRequired'),\r\n            })}\r\n            id=\"project-name\"\r\n            type=\"text\"\r\n            className=\"input-field\"\r\n            placeholder={t('enterProjectName')}\r\n          />\r\n          {errors.projectName && (\r\n            <p className=\"text-red-500 text-sm\">{`${errors.projectName.message}`}</p>\r\n          )}\r\n        </div>\r\n        {/* Project Description */}\r\n        <div className=\"label-input-group group\">\r\n          <label htmlFor=\"description\" className=\"label-text\">\r\n            {t('description')}\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            {...register(\"description\")}\r\n            className=\"input-field resize-none\"\r\n            cols={4}\r\n            placeholder={t('enterProjectDescription')}\r\n          />\r\n        </div>\r\n        {/* Country and Sector */}\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"country\" className=\"label-text\">\r\n              <Globe size={16} />\r\n              {t('country')}\r\n            </label>\r\n            <Select\r\n              id=\"country\"\r\n              options={countries}\r\n              value={selectedCountry}\r\n              onChange={setSelectedCountry}\r\n            />\r\n            {errors.country && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.country.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"sector\" className=\"label-text\">\r\n              <Briefcase size={16} /> {t('sector')}\r\n            </label>\r\n            <Select\r\n              id={`sector`}\r\n              options={Object.values(SectorLabelMap)} // Display labels\r\n              value={\r\n                selectedSector && SectorLabelMap[selectedSector]\r\n                  ? SectorLabelMap[selectedSector]\r\n                  : t('selectOption')\r\n              }\r\n              onChange={(label) => {\r\n                const selectedKey = labelToKey(label, SectorLabelMap);\r\n                setSelectedSector(selectedKey); // Set the enum key for storage\r\n              }}\r\n            />\r\n            {errors.sector && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.sector.message}`}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-4\">\r\n            {projectData?.status === \"deployed\" && (\r\n              <button\r\n                onClick={handleArchiveClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                {t('archive')}\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"deployed\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                {t('redeploy')}\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"archived\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\" //Unarchive button\r\n              >\r\n                {t('deploy')}\r\n              </button>\r\n            )}\r\n\r\n            {projectData?.status === \"draft\" && (\r\n              <button\r\n                onClick={handleDeployClick}\r\n                type=\"button\"\r\n                className=\"btn-outline\"\r\n              >\r\n                {t('deploy')}\r\n              </button>\r\n            )}\r\n\r\n            <button\r\n              type=\"button\"\r\n              className=\"btn-outline\"\r\n              onClick={handleShareModal}\r\n            >\r\n              {t('share')}\r\n            </button>\r\n\r\n            <button\r\n              onClick={handleDeleteClick}\r\n              type=\"button\"\r\n              className=\"btn-danger\"\r\n            >\r\n              {t('delete')}\r\n            </button>\r\n          </div>\r\n          <button type=\"submit\" className=\"btn-primary self-end\">\r\n            {isSubmitting ? (\r\n              <span className=\"flex items-center gap-2\">\r\n                {t('saving')}\r\n                <div className=\"size-4 animate-spin border-x border-neutral-100 rounded-full\"></div>\r\n              </span>\r\n            ) : (\r\n              t('saveChanges')\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <ShareProjectModal\r\n        showModal={showShareModal}\r\n        onClose={() => setShowShareModal(false)}\r\n        onShare={() => {\r\n          setShowShareModal(false);\r\n        }}\r\n      />\r\n\r\n      {confirmationModalContent && (\r\n        <ConfirmationModal\r\n          showModal={showConfirmationModal}\r\n          onClose={() => setShowConfirmationModal(false)}\r\n          title={confirmationModalContent.title}\r\n          description={confirmationModalContent.description}\r\n          confirmButtonText={confirmationModalContent.confirmButtonText}\r\n          confirmButtonClass={confirmationModalContent.confirmButtonClass}\r\n          onConfirm={confirmationModalContent.onConfirm}\r\n        />\r\n      )}\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default ProjectSettingsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;;AAvBA;;;;;;;;;;;;;;;;;;;;;;AAwBA,uDAAuD;AAEvD,MAAM,gBAAgB,OAAO,EAC3B,SAAS,EACT,UAAU,EASX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE;IAC7D,OAAO;AACT;AAEA,MAAM,sBAAsB;;IAC1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,cAAc;QAChB;wCAAG,EAAE;IAEL,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,EAChD,YAAY,EACZ,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD;IAEV,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,yCAAyC;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAM7D;IAEV,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,SAAS,WAAW;gBAAE,UAAU;YAA0B;YAC1D,SAAS,UAAU;gBAAE,UAAU;YAAyB;QAC1D;wCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,SAAS,WAAW,iBAAiB;gBAAE,gBAAgB;YAAY;YACnE,SAAS,UAAU,gBAAgB;gBAAE,gBAAgB;YAAY;QACnE;wCAAG;QAAC;QAAU;QAAiB;KAAe;IAE9C,yDAAyD;IACzD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,iBAAiB;IACvB,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR;iDAAO;oBACL,IAAI,aAAa,MAAM,IAAI;wBACzB,YAAY,aAAa,CAAC;4BACxB,UAAU;gCAAC;gCAAY,KAAK,EAAE;gCAAE;6BAAU;wBAC5C;oBACF;gBACF;;QACF;wCAAG;QAAC;QAAW,MAAM;QAAI;KAAY;IAErC,MAAM,EACJ,MAAM,WAAW,EACjB,WAAW,cAAc,EACzB,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QACpB,UAAU;YAAC;YAAY,MAAM;YAAI;SAAU;QAC3C,OAAO;4CAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACxD,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM,MAAM,CAAC;IACzC;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,aAAa;gBACf,MAAM;oBACJ,aAAa,YAAY,IAAI,IAAI;oBACjC,aAAa,YAAY,WAAW,IAAI;oBACxC,SAAS,YAAY,OAAO,IAAI;oBAChC,QAAQ,YAAY,MAAM,IAAI;gBAChC;gBAEA,mBAAmB,YAAY,OAAO,IAAI;gBAC1C,kBAAkB,YAAY,MAAM,IAAI;YAC1C;QACF;wCAAG;QAAC;QAAa;KAAM;IAEvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;QACZ,SAAS;gEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAY,MAAM;qBAAG;oBAChC,OAAO;gBACT;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,OAAO;gEAAE,CAAC;gBACR,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SACE,EAAE,yBACF,MAAM,OAAO;oBACf,MAAM;gBACR;YAEJ;;IACF;IAEA,iBAAiB;IACjB,MAAM,wBAAwB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA6C;QACnF,UAAU;sEAAE,CAAC,YAAc,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,WAAY,WAAW,eAAe;;QAC/E,SAAS;sEAAE,CAAC,GAAG;gBACb,MAAM,cAAc,WAAW,eAAe;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY,MAAM;qBAAG;gBAAC;gBACjE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,cACL,EAAE,uBACF,EAAE;oBACN,MAAM;gBACR;gBAEF,yBAAyB;YAC3B;;QACA,OAAO;sEAAE,CAAC;gBACR,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,yBAAyB;YAC3B;;IACF;IAEA,yCAAyC;IACzC,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,UAAU;uEAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;;QACjC,SAAS;uEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAY,MAAM;wBAAI;qBAAU;gBAC7C;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,yBAAyB;gBACzB,OAAO,IAAI,CAAC,eAAe,qBAAqB;gBAEhD,oCAAoC;gBACpC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY,MAAM;qBAAG;gBAAC;YACnE;;QACA,OAAO;uEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,yBAAyB;YAC3B;;IACF;IAEA,yCAAyC;IACzC,MAAM,wBAAwB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACxC,UAAU;sEAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;;QAChC,SAAS;sEAAE;gBACT,aAAa;gBACb,yBAAyB;gBAEzB,sCAAsC;gBACtC,YAAY,aAAa,CAAC;oBACxB,UAAU;wBAAC;wBAAY,MAAM;wBAAI;qBAAU;gBAC7C;gBAEA,6CAA6C;gBAC7C,YAAY,aAAa,CAAC;oBACxB,UAAU;wBAAC;wBAAY,MAAM;wBAAI;qBAAU;gBAC7C;gBAEA,oCAAoC;gBACpC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY,MAAM;qBAAG;gBAAC;gBAEjE,4BAA4B;gBAC5B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,yDAAyD;gBACzD;8EAAW;wBACT,OAAO,IAAI,CAAC;oBACd;6EAAG;YACL;;QACA,OAAO;sEAAE,CAAC;gBACR,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,2BAA2B;gBAEzC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,qBAAqB;QACzB,4BAA4B;YAC1B,OAAO,EAAE;YACT,2BACE;0BACG,EAAE;;YAGP,mBAAmB,EAAE;YACrB,oBAAoB;YACpB,WAAW;gBACT,uBAAuB,MAAM;YAC/B;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,oBAAoB;QACxB,MAAM,gBAAgB,aAAa,WAAW;QAE9C,IAAI,cAAc,EAAE;QAEpB,IAAI,aAAa,WAAW,YAAY;YACtC,cAAc,EAAE;QAClB,OAAO,IAAI,aAAa,WAAW,YAAY;YAC7C,cAAc,EAAE;QAClB;QAEA,4BAA4B;YAC1B,OAAO,gBAAgB,EAAE,sBAAsB,EAAE;YACjD;YACA,mBAAmB,gBAAgB,EAAE,eAAe,EAAE;YACtD,oBAAoB;YACpB,WAAW;gBACT,sBAAsB,MAAM,CAAC;oBAAE,aAAa;gBAAc;YAC5D;QACF;QAEA,yBAAyB;IAC3B;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB,MAAM,CAAC;YACrB,WAAW;YACX,YAAY;gBACV,MAAM,KAAK,WAAW;gBACtB,aAAa,KAAK,WAAW;gBAC7B,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM;YACrB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,4BAA4B;YAC1B,OAAO,EAAE;YACT,2BACE;;kCACE,6LAAC;kCACE,EAAE;;;;;;kCAEL,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAI,EAAE;;;;;;0CACP,6LAAC;0CAAI,EAAE;;;;;;0CACP,6LAAC;0CACE,EAAE;;;;;;;;;;;;;;YAKX,mBAAmB,EAAE;YACrB,oBAAoB;YACpB,WAAW;gBACT,sBAAsB,MAAM;YAC9B;QACF;QACA,yBAAyB;IAC3B;IAEA,0EAA0E;IAC1E,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI,WAAW;QACb,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,IAAI,gBAAgB;QAClB,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,wCAAwC;IACxC,IAAI,CAAC,YAAY,cAAc,MAAM;QACnC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAgB,EAAE;;;;;;8BAChC,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;IAIX;IAEA,IAAI,gBAAgB,CAAC,WAAW;QAC9B,qBACE,6LAAC;YAAE,WAAU;sBAAgB,EAAE;;;;;;IAEnC;IAEA,qBACE,6LAAC;QAAK,WAAU;QAAsB,UAAU,aAAa;;0BAC3D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;;kDACtC,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCAAM;oCAAE,EAAE;;;;;;;0CAE5B,6LAAC;gCACE,GAAG,SAAS,eAAe;oCAC1B,UAAU,EAAE;gCACd,EAAE;gCACF,IAAG;gCACH,MAAK;gCACL,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CAAwB,GAAG,OAAO,WAAW,CAAC,OAAO,EAAE;;;;;;;;;;;;kCAIxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CACpC,EAAE;;;;;;0CAEL,6LAAC;gCACC,IAAG;gCACF,GAAG,SAAS,cAAc;gCAC3B,WAAU;gCACV,MAAM;gCACN,aAAa,EAAE;;;;;;;;;;;;kCAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAU,WAAU;;0DACjC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CACZ,EAAE;;;;;;;kDAEL,6LAAC,mIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,iGAAA,CAAA,UAAS;wCAClB,OAAO;wCACP,UAAU;;;;;;oCAEX,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,OAAO,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAS,WAAU;;0DAChC,6LAAC,+MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;4CAAM;4CAAE,EAAE;;;;;;;kDAE7B,6LAAC,mIAAA,CAAA,SAAM;wCACL,IAAI,CAAC,MAAM,CAAC;wCACZ,SAAS,OAAO,MAAM,CAAC,uHAAA,CAAA,iBAAc;wCACrC,OACE,kBAAkB,uHAAA,CAAA,iBAAc,CAAC,eAAe,GAC5C,uHAAA,CAAA,iBAAc,CAAC,eAAe,GAC9B,EAAE;wCAER,UAAU,CAAC;4CACT,MAAM,cAAc,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,uHAAA,CAAA,iBAAc;4CACpD,kBAAkB,cAAc,+BAA+B;wCACjE;;;;;;oCAED,OAAO,MAAM,kBACZ,6LAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,MAAM,CAAC,OAAO,EAAE;;;;;;;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,aAAa,WAAW,4BACvB,6LAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;oCAIN,aAAa,WAAW,4BACvB,6LAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;oCAIN,aAAa,WAAW,4BACvB,6LAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU,cAAc,kBAAkB;;kDAEzC,EAAE;;;;;;oCAIN,aAAa,WAAW,yBACvB,6LAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAIP,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,EAAE;;;;;;kDAGL,6LAAC;wCACC,SAAS;wCACT,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;0CAGP,6LAAC;gCAAO,MAAK;gCAAS,WAAU;0CAC7B,6BACC,6LAAC;oCAAK,WAAU;;wCACb,EAAE;sDACH,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAGjB,EAAE;;;;;;;;;;;;;;;;;;0BAKV,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,kBAAkB;gBACjC,SAAS;oBACP,kBAAkB;gBACpB;;;;;;YAGD,0CACC,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,yBAAyB;gBACxC,OAAO,yBAAyB,KAAK;gBACrC,aAAa,yBAAyB,WAAW;gBACjD,mBAAmB,yBAAyB,iBAAiB;gBAC7D,oBAAoB,yBAAyB,kBAAkB;gBAC/D,WAAW,yBAAyB,SAAS;;;;;;;;;;;;AAKvD;GAhfM;;QAaA,iKAAA,CAAA,UAAO;QAEI,qIAAA,CAAA,YAAS;QAEd,yMAAA,CAAA,kBAAe;QA8BJ,qIAAA,CAAA,YAAS;QAIb,oHAAA,CAAA,UAAO;QAEJ,yLAAA,CAAA,iBAAc;QAiB9B,8KAAA,CAAA,WAAQ;QAqBK,4JAAA,CAAA,cAAW;QAGJ,iLAAA,CAAA,cAAW;QA2BL,iLAAA,CAAA,cAAW;QA2BV,iLAAA,CAAA,cAAW;QA+BZ,iLAAA,CAAA,cAAW;;;KAnLrC;uCAkfS", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/encodeDecode.ts"], "sourcesContent": ["import Hashids from \"hashids\";\r\n\r\nconst salt = process.env.SALT || \"rushan-salt\";\r\n\r\nconst hashids = new Hashids(salt, 12);\r\n\r\nconst encode = (id: number) => {\r\n  return hashids.encode(id);\r\n};\r\n\r\nconst decode = (hash: string) => {\r\n  const decodedNumberLike = hashids.decode(hash)[0];\r\n  const decoded =\r\n    typeof decodedNumberLike === \"bigint\"\r\n      ? decodedNumberLike < Number.MAX_SAFE_INTEGER\r\n        ? Number(decodedNumberLike)\r\n        : null\r\n      : typeof decodedNumberLike === \"number\"\r\n      ? decodedNumberLike\r\n      : null;\r\n  return decoded;\r\n};\r\n\r\nexport { encode, decode };\r\n"], "names": [], "mappings": ";;;;AAEa;AAFb;;AAEA,MAAM,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,IAAI,IAAI;AAEjC,MAAM,UAAU,IAAI,4IAAA,CAAA,UAAO,CAAC,MAAM;AAElC,MAAM,SAAS,CAAC;IACd,OAAO,QAAQ,MAAM,CAAC;AACxB;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,oBAAoB,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;IACjD,MAAM,UACJ,OAAO,sBAAsB,WACzB,oBAAoB,OAAO,gBAAgB,GACzC,OAAO,qBACP,OACF,OAAO,sBAAsB,WAC7B,oBACA;IACN,OAAO;AACT", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAGW;AAHX;;AAEA,MAAM,gBAAgB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n    file?: File;\r\n  };\r\n  position?: number;\r\n}) => {\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  // Validate required fields\r\n  if (!dataToSend.label || !dataToSend.inputType) {\r\n    throw new Error(\"Label and inputType are required\");\r\n  }\r\n\r\n  // Check if this input type requires options\r\n  const needsOptions = [\"selectone\", \"selectmany\"].includes(\r\n    dataToSend.inputType\r\n  );\r\n  const hasFile = dataToSend.file instanceof File;\r\n  const hasOptions =\r\n    Array.isArray(dataToSend.questionOptions) &&\r\n    dataToSend.questionOptions.length > 0;\r\n\r\n  // Validate options based on input type and upload method\r\n  if (needsOptions && !hasFile && !hasOptions) {\r\n    throw new Error(\"Options are required for select input types\");\r\n  }\r\n\r\n  if (hasFile) {\r\n    const formData = new FormData();\r\n\r\n    // Add basic question data\r\n    formData.append(\"label\", dataToSend.label);\r\n    // Convert boolean to string in a way backend can parse\r\n    formData.append(\"isRequired\", dataToSend.isRequired ? \"true\" : \"false\");\r\n    formData.append(\"inputType\", dataToSend.inputType);\r\n    if (dataToSend.hint) formData.append(\"hint\", dataToSend.hint);\r\n    if (dataToSend.placeholder)\r\n      formData.append(\"placeholder\", dataToSend.placeholder);\r\n    // Convert number to string\r\n    formData.append(\"position\", String(position || 1));\r\n\r\n    // Add file with the correct field name\r\n    formData.append(\"file\", dataToSend.file as File);\r\n\r\n    // Important: Do NOT include questionOptions when uploading a file\r\n    // They will be parsed from the file on the server\r\n\r\n    try {\r\n      const { data } = await axios.post(url, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"Upload error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to upload question with file: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  } else {\r\n    // Regular JSON request (no file)\r\n    try {\r\n      const { data } = await axios.post(url, {\r\n        label: dataToSend.label,\r\n        isRequired: dataToSend.isRequired,\r\n        hint: dataToSend.hint,\r\n        placeholder: dataToSend.placeholder,\r\n        inputType: dataToSend.inputType,\r\n        questionOptions: dataToSend.questionOptions,\r\n        position: position || 1,\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"API error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to add question: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  }\r\n};\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n        ? { projectId: contextId }\r\n        : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\r\n      \"Question position updates are only supported for projects\"\r\n    );\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(\r\n    contextType\r\n  )}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Fetch form builder data with ordered structure (groups and questions)\r\nconst fetchFormBuilderData = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/getalldata/${projectId}`);\r\n  return data.data;\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n  fetchFormBuilderData,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAmBT;IACC,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,2BAA2B;IAC3B,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,SAAS,EAAE;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,eAAe;QAAC;QAAa;KAAa,CAAC,QAAQ,CACvD,WAAW,SAAS;IAEtB,MAAM,UAAU,WAAW,IAAI,YAAY;IAC3C,MAAM,aACJ,MAAM,OAAO,CAAC,WAAW,eAAe,KACxC,WAAW,eAAe,CAAC,MAAM,GAAG;IAEtC,yDAAyD;IACzD,IAAI,gBAAgB,CAAC,WAAW,CAAC,YAAY;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS;QACX,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,SAAS,MAAM,CAAC,SAAS,WAAW,KAAK;QACzC,uDAAuD;QACvD,SAAS,MAAM,CAAC,cAAc,WAAW,UAAU,GAAG,SAAS;QAC/D,SAAS,MAAM,CAAC,aAAa,WAAW,SAAS;QACjD,IAAI,WAAW,IAAI,EAAE,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAC5D,IAAI,WAAW,WAAW,EACxB,SAAS,MAAM,CAAC,eAAe,WAAW,WAAW;QACvD,2BAA2B;QAC3B,SAAS,MAAM,CAAC,YAAY,OAAO,YAAY;QAE/C,uCAAuC;QACvC,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAEvC,kEAAkE;QAClE,kDAAkD;QAElD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;gBAC/C,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,yBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,qCAAqC,EACpC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF,OAAO;QACL,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;gBACrC,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU;gBACjC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS;gBAC/B,iBAAiB,WAAW,eAAe;gBAC3C,UAAU,YAAY;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,sBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,wBAAwB,EACvB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF;AACF;AACA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YACd;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAkBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MACR;IAEJ;IAEA,MAAM,MAAM,GAAG,qBACb,aACA,qBAAqB,EAAE,WAAW;IACpC,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF;AAEA,wEAAwE;AACxE,MAAM,uBAAuB,OAAO,EAAE,SAAS,EAAyB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW;IACpE,OAAO,KAAK,IAAI;AAClB", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/projects.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { Project } from \"@/types\";\r\n\r\nconst fetchProjectById = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/${projectId}`);\r\n  return data.project;\r\n};\r\n\r\nconst createProjectFromTemplate = async (dataToSend: {\r\n  templateId: number;\r\n  name: string;\r\n  description: string;\r\n  sector: string;\r\n  country: string;\r\n}) => {\r\n  const { data } = await axios.post(`/projects/from-template`, dataToSend);\r\n  return data;\r\n};\r\n\r\n//Fetch all projects for the current user\r\nconst fetchProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const { data } = await axios.get(`/projects`);\r\n    return data.projects;\r\n  } catch (error) {\r\n    console.error(\"Error fetching projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete project\r\nconst deleteProject = async (projectId: number) => {\r\n  const { data } = await axios.delete(`/projects/delete/${projectId}`);\r\n  return data;\r\n};\r\n\r\n// Delete multiple projects\r\nconst deleteMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.delete(`/projects/delete-multiple`, {\r\n      data: { projectIds },\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Archive project\r\nconst archiveProject = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n//Deploy project\r\nconst deployProject = async (\r\n  projectId: number,\r\n  isUnarchive: boolean = false\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {\r\n      status: \"deployed\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deploying project:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Archive multiple projects\r\nconst archiveMultipleProjects = async (projectIds: number[]) => {\r\n  try {\r\n    const { data } = await axios.patch(`/projects/update-many-status`, {\r\n      projectIds,\r\n      status: \"archived\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error archiving multiple projects:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Check if user exists by email\r\nconst checkUserExists = async (email: string) => {\r\n  try {\r\n    const { data } = await axios.post(`/users/check-email`, { email });\r\n    return data;\r\n  } catch (error: any) {\r\n    // Format error message consistently\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to check user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Add user to project by email\r\nconst addProjectUser = async ({\r\n  projectId,\r\n  email,\r\n  permissions,\r\n}: {\r\n  projectId: number;\r\n  email: string;\r\n  permissions: Record<string, boolean>;\r\n}) => {\r\n  try {\r\n    // First check if the user exists\r\n    const userData = await checkUserExists(email);\r\n\r\n    if (!userData || !userData.success) {\r\n      throw new Error(userData?.message || \"User not found\");\r\n    }\r\n\r\n    // Now use the user ID to add them to the project\r\n    const { data } = await axios.post(`/project-users`, {\r\n      userId: userData.user.id,\r\n      projectId,\r\n      permission: permissions,\r\n    });\r\n\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Error adding user to project:\", error);\r\n    // Format error message as a string\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to add user\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Fetch all users for a specific project\r\nconst fetchProjectUsers = async (projectId: number) => {\r\n  try {\r\n    const { data } = await axios.get(`/project-users/${projectId}`);\r\n    return data.data.AllUser;\r\n  } catch (error: any) {\r\n    console.error(\"Error fetching project users:\", error);\r\n    const errorMessage =\r\n      typeof error.response?.data?.message === \"object\"\r\n        ? JSON.stringify(error.response?.data?.message)\r\n        : error.response?.data?.message ||\r\n          error.message ||\r\n          \"Failed to fetch project users\";\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n};\r\n\r\n// Create answer submission\r\nconst createAnswerSubmission = async (\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/answers/multiple`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update answer submission\r\nconst updateAnswerSubmission = async (\r\n  projectId: number,\r\n  submissionId: number,\r\n  answers: {\r\n    projectId: number;\r\n    questionId: number;\r\n    answerType: string;\r\n    value?: string | number | boolean;\r\n    imageUrl?: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n  }[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/form-submissions/${projectId}/${submissionId}`, { answers });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\n\r\n\r\nexport {\r\n  fetchProjectById,\r\n  fetchProjects,\r\n  deleteProject,\r\n  deleteMultipleProjects,\r\n  archiveMultipleProjects,\r\n  createProjectFromTemplate,\r\n  archiveProject,\r\n  deployProject,\r\n  addProjectUser,\r\n  checkUserExists,\r\n  fetchProjectUsers,\r\n  createAnswerSubmission,\r\n  updateAnswerSubmission,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAGA,MAAM,mBAAmB,OAAO,EAAE,SAAS,EAAyB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW;IACzD,OAAO,KAAK,OAAO;AACrB;AAEA,MAAM,4BAA4B,OAAO;IAOvC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,uBAAuB,CAAC,EAAE;IAC7D,OAAO;AACT;AAEA,yCAAyC;AACzC,MAAM,gBAAgB;IACpB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;QAC5C,OAAO,KAAK,QAAQ;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,gBAAgB,OAAO;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,WAAW;IACnE,OAAO;AACT;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAAO;IACpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC/D,MAAM;gBAAE;YAAW;QACrB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,iBAAiB;AACjB,MAAM,iBAAiB,OAAO;IAC5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,gBAAgB;AAChB,MAAM,gBAAgB,OACpB,WACA,cAAuB,KAAK;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,EAAE;YACzE,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEA,4BAA4B;AAC5B,MAAM,0BAA0B,OAAO;IACrC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;YACjE;YACA,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEA,gCAAgC;AAChC,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAAE;QAAM;QAChE,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,oCAAoC;QACpC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB,OAAO,EAC5B,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;IACC,IAAI;QACF,iCAAiC;QACjC,MAAM,WAAW,MAAM,gBAAgB;QAEvC,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;YAClC,MAAM,IAAI,MAAM,UAAU,WAAW;QACvC;QAEA,iDAAiD;QACjD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,EAAE;YAClD,QAAQ,SAAS,IAAI,CAAC,EAAE;YACxB;YACA,YAAY;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mCAAmC;QACnC,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,yCAAyC;AACzC,MAAM,oBAAoB,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAC9D,OAAO,KAAK,IAAI,CAAC,OAAO;IAC1B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,eACJ,OAAO,MAAM,QAAQ,EAAE,MAAM,YAAY,WACrC,KAAK,SAAS,CAAC,MAAM,QAAQ,EAAE,MAAM,WACrC,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb;QAEN,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACvD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,2BAA2B;AAC3B,MAAM,yBAAyB,OAC7B,WACA,cACA;IAUA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,cAAc,EAAE;YAAE;QAAQ;QAC/F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/Spinner.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Spinner = () => {\r\n  return (\r\n    <div className=\"w-full flex items-center justify-center\">\r\n      <div className=\"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Format a date into a readable string format\r\n * @param date - Date object or string to format\r\n * @param format - Optional format type ('short', 'long', or 'full')\r\n * @returns Formatted date string\r\n */\r\nexport function formatDate(\r\n  date: Date | string,\r\n  format: \"short\" | \"long\" | \"full\" = \"short\"\r\n): string {\r\n  if (!date) return \"\";\r\n\r\n  try {\r\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\r\n\r\n    // Return empty string if invalid date\r\n    if (isNaN(dateObj.getTime())) return \"\";\r\n\r\n    switch (format) {\r\n      case \"short\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"short\",\r\n          day: \"numeric\",\r\n        });\r\n\r\n      case \"long\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n        });\r\n\r\n      case \"full\":\r\n        return dateObj.toLocaleDateString(undefined, {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n          weekday: \"long\",\r\n          hour: \"2-digit\",\r\n          minute: \"2-digit\",\r\n          second: \"2-digit\",\r\n        });\r\n\r\n      default:\r\n        return dateObj.toLocaleDateString();\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting date:\", error);\r\n    return String(date);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,WACd,IAAmB,EACnB,SAAoC,OAAO;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAE5D,sCAAsC;QACtC,IAAI,MAAM,QAAQ,OAAO,KAAK,OAAO;QAErC,OAAQ;YACN,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,MAAM;oBACN,QAAQ;gBACV;YAEF,KAAK;gBACH,OAAO,QAAQ,kBAAkB,CAAC,WAAW;oBAC3C,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,SAAS;oBACT,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACV;YAEF;gBACE,OAAO,QAAQ,kBAAkB;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,OAAO;IAChB;AACF", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label } "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface TextareaProps\r\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\r\n  ({ className, ...props }, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          \"flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mUACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { CheckIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot=\"checkbox\"\r\n      className={cn(\r\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot=\"checkbox-indicator\"\r\n        className=\"flex items-center justify-center text-current transition-none\"\r\n      >\r\n        <CheckIcon className=\"size-3.5\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem } "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yOACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]\",\r\n        \"focus-visible:outline-none\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,qFACA,8BACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAfS", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/table.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nexport interface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\nexport interface TableRow {\r\n  id: number;\r\n  rowsName: string;\r\n}\r\n\r\nexport interface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface TableQuestion {\r\n  id: number;\r\n  label: string;\r\n  inputType: string;\r\n  tableColumns: TableColumn[];\r\n  tableRows: TableRow[];\r\n}\r\n\r\n// Fetch table structure (columns and rows)\r\nexport const fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    if (!questionId || isNaN(questionId)) {\r\n      console.error(\"Invalid questionId:\", questionId);\r\n      throw new Error(\"Invalid question ID provided\");\r\n    }\r\n\r\n    // First try the table-questions endpoint\r\n    try {\r\n      const response = await axios.get(`/table-questions/${questionId}`);\r\n\r\n      // Check if the response has the expected structure\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      } else if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      } else if (response.data && response.data.success) {\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /table-questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the questions endpoint\r\n    try {\r\n      const response = await axios.get(`/questions/${questionId}`);\r\n\r\n      if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the tables endpoint as a last resort\r\n    try {\r\n      const response = await axios.get(`/tables/${questionId}`);\r\n\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /tables/ endpoint:\", err);\r\n    }\r\n\r\n    // If all endpoints fail, throw an error\r\n    console.error(\"All endpoints failed to return valid data\");\r\n    throw new Error(\"Failed to fetch table structure from any endpoint\");\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Save cell values\r\nexport const saveCellValues = async (\r\n  questionId: number,\r\n  cellValues: CellValue[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/table-questions/cells`, {\r\n      questionId,\r\n      cellValues,\r\n    });\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error(\"Error saving cell values:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create a new table\r\n// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices\r\n// (1-based) that reference the position of the parent column in the array.\r\n// For example, if column B is a child of column A, and column A is the first column in the array,\r\n// then column B's parentColumnId should be 1.\r\n// This is different from updateTable, which uses actual database IDs.\r\nexport const createTable = async (\r\n  label: string,\r\n  projectId: number,\r\n  columns: { columnName: string; parentColumnId?: number }[],\r\n  rows?: { rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!projectId || isNaN(projectId)) {\r\n      throw new Error(\"Valid project ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => ({\r\n      columnName: col.columnName,\r\n      parentColumnId: col.parentColumnId,\r\n    }));\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Log the rearranged columns\r\n\r\n    // Use the table-questions endpoint which creates both a question and table structure\r\n    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here\r\n    const { data } = await axios.post(`/table-questions`, {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: rows || [],\r\n    });\r\n\r\n    if (!data || !data.success) {\r\n      throw new Error(data?.message || \"Failed to create table\");\r\n    }\r\n\r\n    return data.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating table:\", error);\r\n\r\n    // Enhance error message with response details if available\r\n    if (error.response) {\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response data:\", error.response.data);\r\n\r\n      // If we have a more specific error message from the server, use it\r\n      if (error.response.data && error.response.data.message) {\r\n        error.message = error.response.data.message;\r\n      }\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete a table\r\nexport const deleteTable = async (tableId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/table-questions/${tableId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting table:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update an existing table\r\n// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.\r\n// For new columns (without an ID), use the position (1-based index) of the parent column in the array.\r\n// For example:\r\n// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.\r\n// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.\r\nexport const updateTable = async (\r\n  tableId: number,\r\n  label: string,\r\n  columns: { id?: number; columnName: string; parentColumnId?: number }[],\r\n  rows?: { id?: number; rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!tableId || isNaN(tableId)) {\r\n      throw new Error(\"Valid table ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // Validate parent-child relationships\r\n    // Check for circular references or invalid parent IDs\r\n    const columnIdMap = new Map();\r\n    const columnPositionMap = new Map();\r\n\r\n    // Map columns by ID and position\r\n    columns.forEach((col, index) => {\r\n      if (col.id) {\r\n        columnIdMap.set(col.id, col);\r\n      }\r\n      // Store 1-based position\r\n      columnPositionMap.set(index + 1, col);\r\n    });\r\n\r\n    // Check each column with a parent\r\n    for (const col of columns) {\r\n      if (col.parentColumnId) {\r\n        // Ensure parentColumnId is a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          throw new Error(\r\n            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`\r\n          );\r\n        }\r\n\r\n        // Try to find parent by ID first\r\n        let parentCol = columns.find((c) => c.id === col.parentColumnId);\r\n\r\n        // If not found by ID, try to find by position (for new columns)\r\n        if (!parentCol && col.parentColumnId <= columns.length) {\r\n          parentCol = columnPositionMap.get(col.parentColumnId);\r\n        }\r\n\r\n        // If we still can't find the parent, it's an error\r\n        if (!parentCol) {\r\n          throw new Error(\r\n            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`\r\n          );\r\n        }\r\n\r\n        // Check for circular references\r\n        // If this column has a parent, and that parent also has a parent,\r\n        // it would create a 3rd level, which we don't support\r\n        if (parentCol.parentColumnId) {\r\n          throw new Error(\r\n            \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => {\r\n      const cleanCol: {\r\n        id?: number;\r\n        columnName: string;\r\n        parentColumnId?: number;\r\n      } = {\r\n        columnName: col.columnName.trim(),\r\n      };\r\n\r\n      if (col.id) {\r\n        cleanCol.id = col.id;\r\n      }\r\n\r\n      if (col.parentColumnId !== undefined) {\r\n        cleanCol.parentColumnId = col.parentColumnId;\r\n      }\r\n\r\n      return cleanCol;\r\n    });\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Use the table-questions endpoint to update the table\r\n    try {\r\n      const { data } = await axios.patch(`/table-questions/${tableId}`, {\r\n        label: label.trim(),\r\n        columns: cleanedColumns,\r\n        rows: rows\r\n          ? rows.map((row) => ({\r\n              ...row,\r\n              rowsName: row.rowsName.trim(),\r\n            }))\r\n          : [],\r\n      });\r\n\r\n      if (!data || !data.success) {\r\n        throw new Error(data?.message || \"Failed to update table\");\r\n      }\r\n\r\n      return data.data;\r\n    } catch (apiError: any) {\r\n      console.error(\"API error updating table:\", apiError);\r\n\r\n      // Enhance error message with response details if available\r\n      if (apiError.response) {\r\n        console.error(\"Response status:\", apiError.response.status);\r\n        console.error(\"Response data:\", apiError.response.data);\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (apiError.response.data && apiError.response.data.message) {\r\n          throw new Error(apiError.response.data.message);\r\n        }\r\n      }\r\n\r\n      // If we don't have a specific error message, throw the original error\r\n      throw apiError;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error updating table:\", error);\r\n\r\n    // Rethrow the error with a clear message\r\n    if (error.message) {\r\n      throw new Error(`Failed to update table: ${error.message}`);\r\n    } else {\r\n      throw new Error(\"Failed to update table due to an unknown error\");\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AA8BO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,IAAI,CAAC,cAAc,MAAM,aAAa;YACpC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YAEjE,mDAAmD;YACnD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACjD,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,oCAAoC;QACtC;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAE3D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QAClD,oCAAoC;QACtC;QAEA,0DAA0D;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY;YAExD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,wCAAwC;QACxC,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAC5B,YACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC1D;YACA;QACF;QACA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,OACA,WACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,aAAa,MAAM,YAAY;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAGA,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,gBAAgB,IAAI,cAAc;YACpC,CAAC;QAED,4CAA4C;QAE5C,6BAA6B;QAE7B,qFAAqF;QACrF,0GAA0G;QAC1G,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,2DAA2D;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YAEnD,mEAAmE;YACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C;QACF;QAEA,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,SACA,OACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,MAAM,UAAU;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QACxB,MAAM,oBAAoB,IAAI;QAE9B,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC,KAAK;YACpB,IAAI,IAAI,EAAE,EAAE;gBACV,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;YACA,yBAAyB;YACzB,kBAAkB,GAAG,CAAC,QAAQ,GAAG;QACnC;QAEA,kCAAkC;QAClC,KAAK,MAAM,OAAO,QAAS;YACzB,IAAI,IAAI,cAAc,EAAE;gBACtB,6CAA6C;gBAC7C,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,IAAI,cAAc,CAAC,4BAA4B,CAAC;gBAEjF;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,cAAc;gBAE/D,gEAAgE;gBAChE,IAAI,CAAC,aAAa,IAAI,cAAc,IAAI,QAAQ,MAAM,EAAE;oBACtD,YAAY,kBAAkB,GAAG,CAAC,IAAI,cAAc;gBACtD;gBAEA,mDAAmD;gBACnD,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,IAAI,cAAc,CAAC,gCAAgC,CAAC;gBAE1F;gBAEA,gCAAgC;gBAChC,kEAAkE;gBAClE,sDAAsD;gBACtD,IAAI,UAAU,cAAc,EAAE;oBAC5B,MAAM,IAAI,MACR;gBAEJ;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAIA,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,SAAS,EAAE,GAAG,IAAI,EAAE;YACtB;YAEA,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,SAAS,cAAc,GAAG,IAAI,cAAc;YAC9C;YAEA,OAAO;QACT;QAEA,4CAA4C;QAE5C,uDAAuD;QACvD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAChE,OAAO,MAAM,IAAI;gBACjB,SAAS;gBACT,MAAM,OACF,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACjB,GAAG,GAAG;wBACN,UAAU,IAAI,QAAQ,CAAC,IAAI;oBAC7B,CAAC,KACD,EAAE;YACR;YAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;YACnC;YAEA,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,2DAA2D;YAC3D,IAAI,SAAS,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC,oBAAoB,SAAS,QAAQ,CAAC,MAAM;gBAC1D,QAAQ,KAAK,CAAC,kBAAkB,SAAS,QAAQ,CAAC,IAAI;gBAEtD,mEAAmE;gBACnE,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,MAAM,IAAI,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAChD;YACF;YAEA,sEAAsE;YACtE,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,yCAAyC;QACzC,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/TableInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow as UITableRow,\r\n} from \"@/components/ui/table\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  fetchTableStructure,\r\n  TableColumn,\r\n  TableRow as TableRowType,\r\n  CellValue,\r\n} from \"../../lib/api/table\";\r\n\r\ninterface TableInputProps {\r\n  questionId: number;\r\n  value: string | CellValue[];\r\n  onChange: (value: CellValue[]) => void;\r\n  required?: boolean;\r\n  tableLabel?: string;\r\n}\r\n\r\nexport function TableInput({\r\n  questionId,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  tableLabel,\r\n}: TableInputProps) {\r\n  // All state hooks at the top of the component\r\n  const [columns, setColumns] = useState<TableColumn[]>([]);\r\n  const [rows, setRows] = useState<TableRowType[]>([]);\r\n  const [cellValues, setCellValues] = useState<Record<string, string>>({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [tableInfo, setTableInfo] = useState<{ label?: string }>({});\r\n\r\n  // Process columns to create a flat structure with parent-child relationships\r\n  const processColumns = (tableData: any) => {\r\n    if (!tableData || !tableData.tableColumns) return [];\r\n\r\n    const flattenedColumns: TableColumn[] = [];\r\n    const parentColumns = tableData.tableColumns.filter(\r\n      (col: TableColumn) =>\r\n        col.parentColumnId === null || col.parentColumnId === undefined\r\n    );\r\n\r\n    // Process each parent column and its children\r\n    parentColumns.forEach((parentCol: TableColumn) => {\r\n      // Add the parent column\r\n      flattenedColumns.push(parentCol);\r\n\r\n      // Add child columns if they exist\r\n      if (parentCol.childColumns && parentCol.childColumns.length > 0) {\r\n        parentCol.childColumns.forEach((childCol: any) => {\r\n          flattenedColumns.push({\r\n            id: childCol.id,\r\n            columnName: childCol.columnName,\r\n            parentColumnId: childCol.parentColumnId,\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    return flattenedColumns;\r\n  };\r\n\r\n  // IMPORTANT: All hooks must be called unconditionally and in the same order every render\r\n  // Group columns by parent-child relationships - always called, never conditional\r\n  const groupedColumns = React.useMemo(() => {\r\n    // Default empty values for when columns are not loaded yet\r\n    if (columns.length === 0) {\r\n      return {\r\n        parentColumns: [],\r\n        columnMap: new Map<number, TableColumn[]>(),\r\n        hasChildColumns: false,\r\n      };\r\n    }\r\n\r\n    // Get all parent columns (those without a parentColumnId)\r\n    const parentColumns = columns.filter(\r\n      (col) => col.parentColumnId === undefined || col.parentColumnId === null\r\n    );\r\n\r\n    // Create a map of parent columns to their child columns\r\n    const columnMap = new Map<number, TableColumn[]>();\r\n\r\n    parentColumns.forEach((parentCol) => {\r\n      // Find all child columns for this parent\r\n      const childColumns = columns.filter(\r\n        (col) => col.parentColumnId === parentCol.id\r\n      );\r\n      columnMap.set(parentCol.id, childColumns);\r\n    });\r\n\r\n    // Check if any parent has child columns\r\n    const hasChildColumns = parentColumns.some(\r\n      (p) => (columnMap.get(p.id) || []).length > 0\r\n    );\r\n\r\n    return { parentColumns, columnMap, hasChildColumns };\r\n  }, [columns]);\r\n\r\n  // Fetch table structure (columns and rows) on component mount or when questionId changes\r\n  useEffect(() => {\r\n    const loadTableStructure = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        const tableData = await fetchTableStructure(questionId);\r\n\r\n        if (tableData) {\r\n          // Check if tableColumns and tableRows exist\r\n          if (!tableData.tableColumns || !tableData.tableRows) {\r\n            console.error(\r\n              \"Missing tableColumns or tableRows in response:\",\r\n              tableData\r\n            );\r\n          }\r\n\r\n          // Process columns to handle parent-child relationships\r\n          const processedColumns = processColumns(tableData);\r\n          setColumns(processedColumns);\r\n          setRows(tableData.tableRows || []);\r\n\r\n          // Store the table label if available\r\n          if (tableData.label) {\r\n            setTableInfo({ label: tableData.label });\r\n          }\r\n\r\n         \r\n        } else {\r\n          console.error(\"No table data returned\");\r\n          setError(\"Failed to load table structure\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching table structure:\", err);\r\n        setError(\"Failed to load table structure\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadTableStructure();\r\n  }, [questionId]); // Only reload when questionId changes, not when value changes\r\n\r\n  // Handle value changes separately without reloading the table structure\r\n  useEffect(() => {\r\n    // Don't process if we're still loading the table structure\r\n    if (loading) return;\r\n\r\n    // Initialize cell values from existing data if available\r\n    const initialCellValues: Record<string, string> = {};\r\n\r\n    // If value is a string, try to parse it as JSON\r\n    let cellData: CellValue[] = [];\r\n    if (typeof value === \"string\") {\r\n      // Only attempt to parse if the string is not empty\r\n      if (value && value.trim() !== \"\") {\r\n        try {\r\n          cellData = JSON.parse(value);\r\n        } catch (e) {\r\n          console.error(\"Error parsing cell data:\", e);\r\n          cellData = [];\r\n        }\r\n      } else {\r\n        console.error(\"Empty string value, using empty array\");\r\n      }\r\n    } else if (Array.isArray(value)) {\r\n      cellData = value;\r\n    }\r\n\r\n    // Convert cell data to a map for easier access\r\n    cellData.forEach((cell) => {\r\n      initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;\r\n    });\r\n\r\n    // Check if the value indicates a form reset (empty array, empty string, or undefined)\r\n    const isFormReset =\r\n      !value ||\r\n      (typeof value === \"string\" && value.trim() === \"\") ||\r\n      (Array.isArray(value) && value.length === 0);\r\n\r\n    if (isFormReset) {\r\n      // Clear all cell values when form is reset\r\n      setCellValues({});\r\n    } else if (Object.keys(initialCellValues).length > 0) {\r\n      // Only update cell values if we have new data and we're not in the middle of editing\r\n      setCellValues((prev) => {\r\n        // Merge with existing values to avoid losing user input\r\n        return { ...initialCellValues, ...prev };\r\n      });\r\n    }\r\n  }, [value, loading]);\r\n\r\n  // Handle cell value change\r\n  const handleCellChange = (\r\n    columnId: number,\r\n    rowId: number,\r\n    newValue: string\r\n  ) => {\r\n    const cellKey = `${columnId}_${rowId}`;\r\n\r\n    // Update the cell values state\r\n    setCellValues((prev) => ({\r\n      ...prev,\r\n      [cellKey]: newValue,\r\n    }));\r\n\r\n    // Use a setTimeout to ensure we're working with the latest state\r\n    // This prevents the race condition where the state update hasn't completed yet\r\n    setTimeout(() => {\r\n      // Get the current state of cellValues after the update\r\n      const currentCellValues = { ...cellValues, [cellKey]: newValue };\r\n\r\n      // Convert the updated cell values to the format expected by the onChange handler\r\n      const updatedCellValues: CellValue[] = [];\r\n\r\n      // Convert all cell values to the expected format\r\n      Object.entries(currentCellValues).forEach(([key, value]) => {\r\n        if (value.trim() !== \"\") {\r\n          const [colId, rowId] = key.split(\"_\").map(Number);\r\n          updatedCellValues.push({\r\n            columnId: colId,\r\n            rowsId: rowId,\r\n            value,\r\n          });\r\n        }\r\n      });\r\n\r\n      // Call the onChange handler with all cell values\r\n      onChange(updatedCellValues);\r\n    }, 0);\r\n  };\r\n\r\n  // Calculate this once, outside of any conditional rendering\r\n  // Only show error when there are no columns - having no rows is valid\r\n  const hasNoColumns = columns.length === 0;\r\n\r\n  // Render the hierarchical table\r\n  // Use a single return statement with conditional rendering inside\r\n  return (\r\n    <div className=\"overflow-x-auto\">\r\n      {loading ? (\r\n        <div className=\"py-4 text-center\">Loading table...</div>\r\n      ) : error ? (\r\n        <div className=\"py-4 text-center text-red-500\">{error}</div>\r\n      ) : hasNoColumns ? (\r\n        <div className=\"py-4 text-center text-amber-600\">\r\n          This table has no columns defined. Please configure the table question\r\n          first.\r\n        </div>\r\n      ) : (\r\n        <Table className=\"border-collapse\">\r\n          <TableHeader>\r\n            {/* First row: Parent column headers starting from leftmost position */}\r\n            <UITableRow>\r\n              {groupedColumns.parentColumns.map((parentCol) => {\r\n                const childColumns =\r\n                  groupedColumns.columnMap.get(parentCol.id) || [];\r\n                // If this parent has children, it spans multiple columns\r\n                const colSpan = childColumns.length || 1;\r\n\r\n                return (\r\n                  <TableHead\r\n                    key={parentCol.id}\r\n                    colSpan={colSpan}\r\n                    className=\"text-center border bg-blue-50 font-medium\"\r\n                  >\r\n                    {parentCol.columnName}\r\n                  </TableHead>\r\n                );\r\n              })}\r\n            </UITableRow>\r\n\r\n            {/* Second row: Child column headers (only if there are child columns) */}\r\n            {groupedColumns.hasChildColumns && (\r\n              <UITableRow>\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render an empty cell to maintain alignment\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableHead\r\n                        key={`empty-${parentCol.id}`}\r\n                        className=\"border bg-blue-50/50 text-sm\"\r\n                      >\r\n                        {/* Empty cell to maintain column alignment */}\r\n                      </TableHead>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableHead\r\n                      key={childCol.id}\r\n                      className=\"border bg-blue-50/50 text-sm\"\r\n                    >\r\n                      {childCol.columnName}\r\n                    </TableHead>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableHeader>\r\n\r\n          <TableBody>\r\n            {rows.length > 0 ? (\r\n              rows.map((row, rowIndex) => (\r\n                <UITableRow\r\n                  key={row.id}\r\n                  className={rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\"}\r\n                >\r\n                  {/* Render cells for each parent column starting from leftmost position */}\r\n                  {groupedColumns.parentColumns.map((parentCol) => {\r\n                    const childColumns =\r\n                      groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                    // If this parent has no children, render a single cell\r\n                    if (childColumns.length === 0) {\r\n                      return (\r\n                        <TableCell\r\n                          key={`cell-${parentCol.id}-${row.id}`}\r\n                          className=\"border p-1\"\r\n                        >\r\n                          <Input\r\n                            value={\r\n                              cellValues[`${parentCol.id}_${row.id}`] || \"\"\r\n                            }\r\n                            onChange={(e) =>\r\n                              handleCellChange(\r\n                                parentCol.id,\r\n                                row.id,\r\n                                e.target.value\r\n                              )\r\n                            }\r\n                            className=\"w-full\"\r\n                            required={required}\r\n                            placeholder=\"Enter value\"\r\n                          />\r\n                        </TableCell>\r\n                      );\r\n                    }\r\n\r\n                    // Otherwise, render cells for each child column\r\n                    return childColumns.map((childCol) => (\r\n                      <TableCell\r\n                        key={`cell-${childCol.id}-${row.id}`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${childCol.id}_${row.id}`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              childCol.id,\r\n                              row.id,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    ));\r\n                  })}\r\n                </UITableRow>\r\n              ))\r\n            ) : (\r\n              // When no rows exist, show a single row with input fields under columns\r\n              <UITableRow>\r\n                {/* Render input cells for each parent column starting from leftmost position */}\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render a single cell\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableCell\r\n                        key={`cell-${parentCol.id}-no-row`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${parentCol.id}_no_row`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              parentCol.id,\r\n                              \"no_row\" as any,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render cells for each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableCell\r\n                      key={`cell-${childCol.id}-no-row`}\r\n                      className=\"border p-1\"\r\n                    >\r\n                      <Input\r\n                        value={cellValues[`${childCol.id}_no_row`] || \"\"}\r\n                        onChange={(e) =>\r\n                          handleCellChange(\r\n                            childCol.id,\r\n                            \"no_row\" as any,\r\n                            e.target.value\r\n                          )\r\n                        }\r\n                        className=\"w-full\"\r\n                        required={required}\r\n                        placeholder=\"Enter value\"\r\n                      />\r\n                    </TableCell>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;;;AAZA;;;;;AA2BO,SAAS,WAAW,EACzB,UAAU,EACV,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,EACM;;IAChB,8CAA8C;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,CAAC;IAEhE,6EAA6E;IAC7E,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU,YAAY,EAAE,OAAO,EAAE;QAEpD,MAAM,mBAAkC,EAAE;QAC1C,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM,CACjD,CAAC,MACC,IAAI,cAAc,KAAK,QAAQ,IAAI,cAAc,KAAK;QAG1D,8CAA8C;QAC9C,cAAc,OAAO,CAAC,CAAC;YACrB,wBAAwB;YACxB,iBAAiB,IAAI,CAAC;YAEtB,kCAAkC;YAClC,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC/D,UAAU,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC9B,iBAAiB,IAAI,CAAC;wBACpB,IAAI,SAAS,EAAE;wBACf,YAAY,SAAS,UAAU;wBAC/B,gBAAgB,SAAS,cAAc;oBACzC;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,yFAAyF;IACzF,iFAAiF;IACjF,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,OAAO;8CAAC;YACnC,2DAA2D;YAC3D,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,OAAO;oBACL,eAAe,EAAE;oBACjB,WAAW,IAAI;oBACf,iBAAiB;gBACnB;YACF;YAEA,0DAA0D;YAC1D,MAAM,gBAAgB,QAAQ,MAAM;oEAClC,CAAC,MAAQ,IAAI,cAAc,KAAK,aAAa,IAAI,cAAc,KAAK;;YAGtE,wDAAwD;YACxD,MAAM,YAAY,IAAI;YAEtB,cAAc,OAAO;sDAAC,CAAC;oBACrB,yCAAyC;oBACzC,MAAM,eAAe,QAAQ,MAAM;2EACjC,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;;oBAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;gBAC9B;;YAEA,wCAAwC;YACxC,MAAM,kBAAkB,cAAc,IAAI;sEACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;;YAG9C,OAAO;gBAAE;gBAAe;gBAAW;YAAgB;QACrD;6CAAG;QAAC;KAAQ;IAEZ,yFAAyF;IACzF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;2DAAqB;oBACzB,IAAI;wBACF,WAAW;wBAEX,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;wBAE5C,IAAI,WAAW;4BACb,4CAA4C;4BAC5C,IAAI,CAAC,UAAU,YAAY,IAAI,CAAC,UAAU,SAAS,EAAE;gCACnD,QAAQ,KAAK,CACX,kDACA;4BAEJ;4BAEA,uDAAuD;4BACvD,MAAM,mBAAmB,eAAe;4BACxC,WAAW;4BACX,QAAQ,UAAU,SAAS,IAAI,EAAE;4BAEjC,qCAAqC;4BACrC,IAAI,UAAU,KAAK,EAAE;gCACnB,aAAa;oCAAE,OAAO,UAAU,KAAK;gCAAC;4BACxC;wBAGF,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,SAAS;wBACX;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG;QAAC;KAAW,GAAG,8DAA8D;IAEhF,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,2DAA2D;YAC3D,IAAI,SAAS;YAEb,yDAAyD;YACzD,MAAM,oBAA4C,CAAC;YAEnD,gDAAgD;YAChD,IAAI,WAAwB,EAAE;YAC9B,IAAI,OAAO,UAAU,UAAU;gBAC7B,mDAAmD;gBACnD,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;oBAChC,IAAI;wBACF,WAAW,KAAK,KAAK,CAAC;oBACxB,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,WAAW,EAAE;oBACf;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC;gBAChB;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,WAAW;YACb;YAEA,+CAA+C;YAC/C,SAAS,OAAO;wCAAC,CAAC;oBAChB,iBAAiB,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,KAAK,KAAK;gBACnE;;YAEA,sFAAsF;YACtF,MAAM,cACJ,CAAC,SACA,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,MAC9C,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK;YAE5C,IAAI,aAAa;gBACf,2CAA2C;gBAC3C,cAAc,CAAC;YACjB,OAAO,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;gBACpD,qFAAqF;gBACrF;4CAAc,CAAC;wBACb,wDAAwD;wBACxD,OAAO;4BAAE,GAAG,iBAAiB;4BAAE,GAAG,IAAI;wBAAC;oBACzC;;YACF;QACF;+BAAG;QAAC;QAAO;KAAQ;IAEnB,2BAA2B;IAC3B,MAAM,mBAAmB,CACvB,UACA,OACA;QAEA,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,OAAO;QAEtC,+BAA+B;QAC/B,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;QAED,iEAAiE;QACjE,+EAA+E;QAC/E,WAAW;YACT,uDAAuD;YACvD,MAAM,oBAAoB;gBAAE,GAAG,UAAU;gBAAE,CAAC,QAAQ,EAAE;YAAS;YAE/D,iFAAiF;YACjF,MAAM,oBAAiC,EAAE;YAEzC,iDAAiD;YACjD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACrD,IAAI,MAAM,IAAI,OAAO,IAAI;oBACvB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;oBAC1C,kBAAkB,IAAI,CAAC;wBACrB,UAAU;wBACV,QAAQ;wBACR;oBACF;gBACF;YACF;YAEA,iDAAiD;YACjD,SAAS;QACX,GAAG;IACL;IAEA,4DAA4D;IAC5D,sEAAsE;IACtE,MAAM,eAAe,QAAQ,MAAM,KAAK;IAExC,gCAAgC;IAChC,kEAAkE;IAClE,qBACE,6LAAC;QAAI,WAAU;kBACZ,wBACC,6LAAC;YAAI,WAAU;sBAAmB;;;;;mBAChC,sBACF,6LAAC;YAAI,WAAU;sBAAiC;;;;;mBAC9C,6BACF,6LAAC;YAAI,WAAU;sBAAkC;;;;;iCAKjD,6LAAC,6HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,6LAAC,6HAAA,CAAA,cAAW;;sCAEV,6LAAC,6HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAClD,yDAAyD;gCACzD,MAAM,UAAU,aAAa,MAAM,IAAI;gCAEvC,qBACE,6LAAC,6HAAA,CAAA,YAAS;oCAER,SAAS;oCACT,WAAU;8CAET,UAAU,UAAU;mCAJhB,UAAU,EAAE;;;;;4BAOvB;;;;;;wBAID,eAAe,eAAe,kBAC7B,6LAAC,6HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,6EAA6E;gCAC7E,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;uCADL,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;;;;;gCAMlC;gCAEA,sCAAsC;gCACtC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAET,SAAS,UAAU;uCAHf,SAAS,EAAE;;;;;4BAMtB;;;;;;;;;;;;8BAKN,6LAAC,6HAAA,CAAA,YAAS;8BACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAU;4BAET,WAAW,WAAW,MAAM,IAAI,aAAa;sCAG5C,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,uDAAuD;gCACvD,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OACE,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CAE7C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAhBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;gCAoB3C;gCAEA,gDAAgD;gCAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CACjD,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;4BAkB1C;2BAvDK,IAAI,EAAE;;;;oCA2Df,wEAAwE;kCACxE,6LAAC,6HAAA,CAAA,WAAU;kCAER,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4BACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4BAElD,uDAAuD;4BACvD,IAAI,aAAa,MAAM,KAAK,GAAG;gCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC/C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC;;;;;4BAkBxC;4BAEA,gDAAgD;4BAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC9C,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC;;;;;wBAkBvC;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAzZgB;KAAA", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/conditionalQuestions.ts"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\n\r\n/**\r\n * Utility functions for handling conditional questions logic\r\n */\r\n\r\n/**\r\n * Get the next question ID based on the selected option\r\n */\r\nexport const getNextQuestionId = (\r\n  question: Question,\r\n  selectedValue: string | string[]\r\n): number | null => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // For selectone, selectedValue is a string\r\n  if (question.inputType === \"selectone\" && typeof selectedValue === \"string\") {\r\n    const selectedOption = question.questionOptions.find(\r\n      (option) => option.label === selectedValue\r\n    );\r\n    return selectedOption?.nextQuestionId || null;\r\n  }\r\n\r\n  // For selectmany, selectedValue is an array - return the first next question found\r\n  if (question.inputType === \"selectmany\" && Array.isArray(selectedValue)) {\r\n    for (const value of selectedValue) {\r\n      const selectedOption = question.questionOptions.find(\r\n        (option) => option.label === value\r\n      );\r\n      if (selectedOption?.nextQuestionId) {\r\n        return selectedOption.nextQuestionId;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n/**\r\n * Get all possible next question IDs for a question (for dependency tracking)\r\n */\r\nexport const getAllNextQuestionIds = (question: Question): number[] => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  return question.questionOptions\r\n    .map((option) => option.nextQuestionId)\r\n    .filter((id): id is number => id !== null && id !== undefined);\r\n};\r\n\r\n/**\r\n * Determine which questions should be visible based on current answers\r\n */\r\nexport const getVisibleQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Question[] => {\r\n  const visibleQuestionIds = new Set<number>();\r\n  const conditionalQuestionIds = new Set<number>();\r\n\r\n  // First, collect all questions that are conditional (have a parent question)\r\n  allQuestions.forEach((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    nextQuestionIds.forEach((id) => conditionalQuestionIds.add(id));\r\n  });\r\n\r\n  // Start with all non-conditional questions (questions that are not triggered by other questions)\r\n  allQuestions.forEach((question) => {\r\n    if (!conditionalQuestionIds.has(question.id)) {\r\n      visibleQuestionIds.add(question.id);\r\n    }\r\n  });\r\n\r\n  // Process answers to determine which conditional questions should be visible\r\n  Object.entries(answers).forEach(([questionIdStr, answer]) => {\r\n    const questionId = parseInt(questionIdStr);\r\n    const question = allQuestions.find((q) => q.id === questionId);\r\n\r\n    if (question && answer) {\r\n      const nextQuestionId = getNextQuestionId(question, answer);\r\n      if (nextQuestionId) {\r\n        visibleQuestionIds.add(nextQuestionId);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Return questions in their original order, filtered by visibility\r\n  return allQuestions.filter((question) => visibleQuestionIds.has(question.id));\r\n};\r\n\r\n/**\r\n * Check if a question should be visible based on current answers\r\n */\r\nexport const isQuestionVisible = (\r\n  question: Question,\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): boolean => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  return visibleQuestions.some((q) => q.id === question.id);\r\n};\r\n\r\n/**\r\n * Get questions that depend on a specific question\r\n */\r\nexport const getDependentQuestions = (\r\n  parentQuestionId: number,\r\n  allQuestions: Question[]\r\n): Question[] => {\r\n  const parentQuestion = allQuestions.find((q) => q.id === parentQuestionId);\r\n  if (!parentQuestion) return [];\r\n\r\n  const nextQuestionIds = getAllNextQuestionIds(parentQuestion);\r\n  return allQuestions.filter((q) => nextQuestionIds.includes(q.id));\r\n};\r\n\r\n/**\r\n * Check if a question is a follow-up question (has a parent question)\r\n */\r\nexport const isFollowUpQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): boolean => {\r\n  return allQuestions.some((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    return nextQuestionIds.includes(questionId);\r\n  });\r\n};\r\n\r\n/**\r\n * Get the parent question for a follow-up question\r\n */\r\nexport const getParentQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): Question | null => {\r\n  return (\r\n    allQuestions.find((question) => {\r\n      const nextQuestionIds = getAllNextQuestionIds(question);\r\n      return nextQuestionIds.includes(questionId);\r\n    }) || null\r\n  );\r\n};\r\n\r\n/**\r\n * Get questions in nested structure for rendering\r\n * Returns questions grouped by parent-child relationships, maintaining order\r\n */\r\nexport const getNestedQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Array<{\r\n  question: Question;\r\n  isVisible: boolean;\r\n  isFollowUp: boolean;\r\n  parentQuestion?: Question;\r\n  followUps: Array<{\r\n    question: Question;\r\n    isVisible: boolean;\r\n  }>;\r\n}> => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // Get all parent questions (questions that are not follow-ups themselves)\r\n  const parentQuestions = allQuestions.filter(\r\n    (question) => !isFollowUpQuestion(question.id, allQuestions)\r\n  );\r\n\r\n  // Sort parent questions by position to maintain order\r\n  const sortedParentQuestions = parentQuestions.sort(\r\n    (a, b) => a.position - b.position\r\n  );\r\n\r\n  return sortedParentQuestions\r\n    .map((parentQuestion) => {\r\n      const followUpQuestions = getDependentQuestions(\r\n        parentQuestion.id,\r\n        allQuestions\r\n      );\r\n      const sortedFollowUps = followUpQuestions.sort(\r\n        (a, b) => a.position - b.position\r\n      );\r\n\r\n      return {\r\n        question: parentQuestion,\r\n        isVisible: visibleQuestionIds.has(parentQuestion.id),\r\n        isFollowUp: false,\r\n        followUps: sortedFollowUps.map((followUp) => ({\r\n          question: followUp,\r\n          isVisible: visibleQuestionIds.has(followUp.id),\r\n        })),\r\n      };\r\n    })\r\n    .filter(\r\n      (group) => group.isVisible || group.followUps.some((f) => f.isVisible)\r\n    );\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible\r\n */\r\nexport const cleanupHiddenAnswers = (\r\n  answers: Record<string, any>,\r\n  visibleQuestions: Question[]\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(answers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible while preserving original data\r\n * This version maintains original submitted values for conditional questions\r\n */\r\nexport const cleanupHiddenAnswersWithPersistence = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(currentAnswers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Restore original answers for questions that have become visible again\r\n * This helps maintain data persistence when toggling conditional questions\r\n */\r\nexport const restoreOriginalAnswers = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const restoredAnswers = { ...currentAnswers };\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // For each visible question, if it doesn't have a current answer but has an original answer, restore it\r\n  visibleQuestionIds.forEach((questionId) => {\r\n    const questionIdStr = questionId.toString();\r\n    const hasCurrentAnswer = currentAnswers[questionIdStr] !== undefined &&\r\n                            currentAnswers[questionIdStr] !== \"\" &&\r\n                            !(Array.isArray(currentAnswers[questionIdStr]) && currentAnswers[questionIdStr].length === 0);\r\n    const hasOriginalAnswer = originalAnswers[questionIdStr] !== undefined &&\r\n                             originalAnswers[questionIdStr] !== \"\" &&\r\n                             !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0);\r\n\r\n    // If the question is visible but has no current answer, restore the original answer\r\n    if (!hasCurrentAnswer && hasOriginalAnswer) {\r\n      restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];\r\n    }\r\n  });\r\n\r\n  return restoredAnswers;\r\n};\r\n\r\n/**\r\n * Validate that all visible required questions have answers\r\n */\r\nexport const validateVisibleQuestions = (\r\n  visibleQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  visibleQuestions.forEach((question) => {\r\n    if (question.isRequired) {\r\n      const value = answers[question.id];\r\n      if (\r\n        (typeof value === \"string\" && !value.trim()) ||\r\n        (Array.isArray(value) && value.length === 0) ||\r\n        value === undefined ||\r\n        value === null\r\n      ) {\r\n        errors[question.id] = `${question.label} is required`;\r\n      }\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AASO,MAAM,oBAAoB,CAC/B,UACA;IAEA,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,SAAS,KAAK,eAAe,OAAO,kBAAkB,UAAU;QAC3E,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;QAE/B,OAAO,gBAAgB,kBAAkB;IAC3C;IAEA,mFAAmF;IACnF,IAAI,SAAS,SAAS,KAAK,gBAAgB,MAAM,OAAO,CAAC,gBAAgB;QACvE,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;YAE/B,IAAI,gBAAgB,gBAAgB;gBAClC,OAAO,eAAe,cAAc;YACtC;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,eAAe,CAC5B,GAAG,CAAC,CAAC,SAAW,OAAO,cAAc,EACrC,MAAM,CAAC,CAAC,KAAqB,OAAO,QAAQ,OAAO;AACxD;AAKO,MAAM,sBAAsB,CACjC,cACA;IAEA,MAAM,qBAAqB,IAAI;IAC/B,MAAM,yBAAyB,IAAI;IAEnC,6EAA6E;IAC7E,aAAa,OAAO,CAAC,CAAC;QACpB,MAAM,kBAAkB,sBAAsB;QAC9C,gBAAgB,OAAO,CAAC,CAAC,KAAO,uBAAuB,GAAG,CAAC;IAC7D;IAEA,iGAAiG;IACjG,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,CAAC,SAAS,EAAE,GAAG;YAC5C,mBAAmB,GAAG,CAAC,SAAS,EAAE;QACpC;IACF;IAEA,6EAA6E;IAC7E,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,eAAe,OAAO;QACtD,MAAM,aAAa,SAAS;QAC5B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAEnD,IAAI,YAAY,QAAQ;YACtB,MAAM,iBAAiB,kBAAkB,UAAU;YACnD,IAAI,gBAAgB;gBAClB,mBAAmB,GAAG,CAAC;YACzB;QACF;IACF;IAEA,mEAAmE;IACnE,OAAO,aAAa,MAAM,CAAC,CAAC,WAAa,mBAAmB,GAAG,CAAC,SAAS,EAAE;AAC7E;AAKO,MAAM,oBAAoB,CAC/B,UACA,cACA;IAEA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,OAAO,iBAAiB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;AAC1D;AAKO,MAAM,wBAAwB,CACnC,kBACA;IAEA,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD,IAAI,CAAC,gBAAgB,OAAO,EAAE;IAE9B,MAAM,kBAAkB,sBAAsB;IAC9C,OAAO,aAAa,MAAM,CAAC,CAAC,IAAM,gBAAgB,QAAQ,CAAC,EAAE,EAAE;AACjE;AAKO,MAAM,qBAAqB,CAChC,YACA;IAEA,OAAO,aAAa,IAAI,CAAC,CAAC;QACxB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC;AACF;AAKO,MAAM,oBAAoB,CAC/B,YACA;IAEA,OACE,aAAa,IAAI,CAAC,CAAC;QACjB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC,MAAM;AAEV;AAMO,MAAM,qBAAqB,CAChC,cACA;IAWA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,0EAA0E;IAC1E,MAAM,kBAAkB,aAAa,MAAM,CACzC,CAAC,WAAa,CAAC,mBAAmB,SAAS,EAAE,EAAE;IAGjD,sDAAsD;IACtD,MAAM,wBAAwB,gBAAgB,IAAI,CAChD,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAGnC,OAAO,sBACJ,GAAG,CAAC,CAAC;QACJ,MAAM,oBAAoB,sBACxB,eAAe,EAAE,EACjB;QAEF,MAAM,kBAAkB,kBAAkB,IAAI,CAC5C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAGnC,OAAO;YACL,UAAU;YACV,WAAW,mBAAmB,GAAG,CAAC,eAAe,EAAE;YACnD,YAAY;YACZ,WAAW,gBAAgB,GAAG,CAAC,CAAC,WAAa,CAAC;oBAC5C,UAAU;oBACV,WAAW,mBAAmB,GAAG,CAAC,SAAS,EAAE;gBAC/C,CAAC;QACH;IACF,GACC,MAAM,CACL,CAAC,QAAU,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,SAAS;AAE3E;AAKO,MAAM,uBAAuB,CAClC,SACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QACnD,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,sCAAsC,CACjD,gBACA,kBACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QAC1D,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,yBAAyB,CACpC,gBACA,kBACA;IAEA,MAAM,kBAAkB;QAAE,GAAG,cAAc;IAAC;IAC5C,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,wGAAwG;IACxG,mBAAmB,OAAO,CAAC,CAAC;QAC1B,MAAM,gBAAgB,WAAW,QAAQ;QACzC,MAAM,mBAAmB,cAAc,CAAC,cAAc,KAAK,aACnC,cAAc,CAAC,cAAc,KAAK,MAClC,CAAC,CAAC,MAAM,OAAO,CAAC,cAAc,CAAC,cAAc,KAAK,cAAc,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QACpH,MAAM,oBAAoB,eAAe,CAAC,cAAc,KAAK,aACpC,eAAe,CAAC,cAAc,KAAK,MACnC,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,CAAC,cAAc,KAAK,eAAe,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QAEvH,oFAAoF;QACpF,IAAI,CAAC,oBAAoB,mBAAmB;YAC1C,eAAe,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc;QACjE;IACF;IAEA,OAAO;AACT;AAKO,MAAM,2BAA2B,CACtC,kBACA;IAEA,MAAM,SAAiC,CAAC;IAExC,iBAAiB,OAAO,CAAC,CAAC;QACxB,IAAI,SAAS,UAAU,EAAE;YACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;YAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;gBACA,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;YACvD;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/NestedQuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\ninterface NestedQuestionRendererProps {\r\n  questionGroup: {\r\n    question: Question;\r\n    isVisible: boolean;\r\n    isFollowUp: boolean;\r\n    followUps: Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n    }>;\r\n  };\r\n  renderQuestionInput: (question: Question) => React.ReactNode;\r\n  errors: Record<number, string>;\r\n  className?: string;\r\n}\r\n\r\nconst NestedQuestionRenderer: React.FC<NestedQuestionRendererProps> = ({\r\n  questionGroup,\r\n  renderQuestionInput,\r\n  errors,\r\n  className = \"\",\r\n}) => {\r\n  const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;\r\n  \r\n  // Don't render anything if neither parent nor any follow-ups are visible\r\n  if (!isParentVisible && !followUps.some(f => f.isVisible)) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={`${className}`}>\r\n      {/* Parent Question */}\r\n      {isParentVisible && (\r\n        <div className=\"border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800\">\r\n          <div className=\"mb-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {parentQuestion.label}\r\n              {parentQuestion.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {parentQuestion.hint && (\r\n              <p className=\"text-sm text-muted-foreground mt-1\">\r\n                {parentQuestion.hint}\r\n              </p>\r\n            )}\r\n            {errors[parentQuestion.id] && (\r\n              <p className=\"text-sm text-red-500 mt-1\">\r\n                {errors[parentQuestion.id]}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div className=\"mt-2\">{renderQuestionInput(parentQuestion)}</div>\r\n          \r\n          {/* Follow-up Questions */}\r\n          {followUps.some(f => f.isVisible) && (\r\n            <div className=\"mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4\">\r\n              {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n                isFollowUpVisible && (\r\n                  <div\r\n                    key={followUpQuestion.id}\r\n                    className=\"border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20\"\r\n                  >\r\n                    <div className=\"mb-2\">\r\n                      <Label className=\"text-sm font-medium text-primary-900 dark:text-primary-100\">\r\n                        {followUpQuestion.label}\r\n                        {followUpQuestion.isRequired && (\r\n                          <span className=\"text-red-500 ml-1\">*</span>\r\n                        )}\r\n                      </Label>\r\n                      {followUpQuestion.hint && (\r\n                        <p className=\"text-xs text-primary-700 dark:text-primary-300 mt-1\">\r\n                          {followUpQuestion.hint}\r\n                        </p>\r\n                      )}\r\n                      {errors[followUpQuestion.id] && (\r\n                        <p className=\"text-xs text-red-500 mt-1\">\r\n                          {errors[followUpQuestion.id]}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n                  </div>\r\n                )\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Render follow-ups without parent if parent is not visible but follow-ups are */}\r\n      {!isParentVisible && followUps.some(f => f.isVisible) && (\r\n        <div className=\"space-y-3\">\r\n          {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n            isFollowUpVisible && (\r\n              <div\r\n                key={followUpQuestion.id}\r\n                className=\"border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800\"\r\n              >\r\n                <div className=\"mb-2\">\r\n                  <Label className=\"text-base font-medium\">\r\n                    {followUpQuestion.label}\r\n                    {followUpQuestion.isRequired && (\r\n                      <span className=\"text-red-500 ml-1\">*</span>\r\n                    )}\r\n                  </Label>\r\n                  {followUpQuestion.hint && (\r\n                    <p className=\"text-sm text-muted-foreground mt-1\">\r\n                      {followUpQuestion.hint}\r\n                    </p>\r\n                  )}\r\n                  {errors[followUpQuestion.id] && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {errors[followUpQuestion.id]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n              </div>\r\n            )\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NestedQuestionRenderer;\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAqBA,MAAM,yBAAgE,CAAC,EACrE,aAAa,EACb,mBAAmB,EACnB,MAAM,EACN,YAAY,EAAE,EACf;IACC,MAAM,EAAE,UAAU,cAAc,EAAE,WAAW,eAAe,EAAE,SAAS,EAAE,GAAG;IAE5E,yEAAyE;IACzE,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG;QACzD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;YAE3B,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;;oCACd,eAAe,KAAK;oCACpB,eAAe,UAAU,kBACxB,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAGvC,eAAe,IAAI,kBAClB,6LAAC;gCAAE,WAAU;0CACV,eAAe,IAAI;;;;;;4BAGvB,MAAM,CAAC,eAAe,EAAE,CAAC,kBACxB,6LAAC;gCAAE,WAAU;0CACV,MAAM,CAAC,eAAe,EAAE,CAAC;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCAAQ,oBAAoB;;;;;;oBAG1C,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAC9B,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;oDACd,iBAAiB,KAAK;oDACtB,iBAAiB,UAAU,kBAC1B,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,iBAAiB,IAAI,kBACpB,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,IAAI;;;;;;4CAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;gDAAE,WAAU;0DACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;kDAIlC,6LAAC;wCAAI,WAAU;kDAAQ,oBAAoB;;;;;;;+BArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;YA+BrC,CAAC,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAClD,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,iBAAiB,KAAK;4CACtB,iBAAiB,UAAU,kBAC1B,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;oCAGvC,iBAAiB,IAAI,kBACpB,6LAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI;;;;;;oCAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;wCAAE,WAAU;kDACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;0CAAQ,oBAAoB;;;;;;;uBArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;AA6BxC;KA7GM;uCA+GS", "debugId": null}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/NestedGroupRenderer.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\nimport { ChevronDown, ChevronRight } from \"lucide-react\";\nimport NestedQuestionRenderer from \"./NestedQuestionRenderer\";\n\ninterface NestedGroupRendererProps {\n  group: QuestionGroup;\n  nestingLevel?: number;\n  visibleQuestions: Question[];\n  nestedQuestions: Array<{\n    question: Question;\n    isVisible: boolean;\n    isFollowUp: boolean;\n    followUps: Array<{\n      question: Question;\n      isVisible: boolean;\n    }>;\n  }>;\n  renderQuestionInput: (question: Question) => React.ReactNode;\n  errors: Record<string, string>;\n  onToggleExpansion?: (groupId: number) => void;\n  isExpanded?: boolean;\n  expandedGroups?: Record<number, boolean>; // Add this to pass expansion state for subgroups\n  className?: string;\n}\n\nconst NestedGroupRenderer: React.FC<NestedGroupRendererProps> = ({\n  group,\n  nestingLevel = 0,\n  visibleQuestions,\n  nestedQuestions,\n  renderQuestionInput,\n  errors,\n  onToggleExpansion,\n  isExpanded: controlledExpanded,\n  expandedGroups,\n  className = \"\",\n}) => {\n  const [internalExpanded, setInternalExpanded] = useState(true);\n  \n  // Use controlled expansion if provided, otherwise use internal state\n  const isExpanded = controlledExpanded !== undefined ? controlledExpanded : internalExpanded;\n  \n  const handleToggleExpansion = () => {\n    if (onToggleExpansion) {\n      onToggleExpansion(group.id);\n    } else {\n      setInternalExpanded(!internalExpanded);\n    }\n  };\n\n  // Get questions for this group\n  const groupQuestions = group.question || [];\n  const visibleGroupQuestions = groupQuestions.filter((q) =>\n    visibleQuestions.some((vq) => vq.id === q.id)\n  );\n\n  // Get subgroups for this group\n  const subGroups = group.subGroups || [];\n  const visibleSubGroups = subGroups.filter((subGroup) => {\n    const subGroupQuestions = subGroup.question || [];\n    return subGroupQuestions.some((q) =>\n      visibleQuestions.some((vq) => vq.id === q.id)\n    );\n  });\n\n  // Don't render if no visible questions in this group or its subgroups\n  if (visibleGroupQuestions.length === 0 && visibleSubGroups.length === 0) {\n    return null;\n  }\n\n  return (\n    <div\n      className={`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${\n        nestingLevel > 0 ? `ml-8 border-l-4 border-l-primary-300` : ''\n      } ${className}`}\n    >\n      {/* Group Header */}\n      <div\n        className=\"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600\"\n        onClick={handleToggleExpansion}\n      >\n        <div className=\"flex items-center space-x-2\">\n          {isExpanded ? (\n            <ChevronDown className=\"h-5 w-5 text-neutral-700 dark:text-neutral-300\" />\n          ) : (\n            <ChevronRight className=\"h-5 w-5 text-neutral-700 dark:text-neutral-300\" />\n          )}\n          <h3 className=\"text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\n            {group.title}\n          </h3>\n          <span className=\"text-sm text-neutral-700 dark:text-neutral-400\">\n            ({visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg) => acc + (sg.question?.length || 0), 0)} visible question\n            {(visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg) => acc + (sg.question?.length || 0), 0)) !== 1 ? \"s\" : \"\"})\n          </span>\n        </div>\n      </div>\n\n      {/* Group Content */}\n      {isExpanded && (\n        <div className=\"p-4 space-y-4\">\n          {/* Render subgroups first */}\n          {visibleSubGroups\n            .sort((a, b) => a.order - b.order)\n            .map((subGroup) => {\n              // Get expansion state for this subgroup from parent's expandedGroups\n              const subGroupExpanded = expandedGroups ? expandedGroups[subGroup.id] : undefined;\n\n              return (\n                <NestedGroupRenderer\n                  key={subGroup.id}\n                  group={subGroup}\n                  nestingLevel={nestingLevel + 1}\n                  visibleQuestions={visibleQuestions}\n                  nestedQuestions={nestedQuestions}\n                  renderQuestionInput={renderQuestionInput}\n                  errors={errors}\n                  onToggleExpansion={onToggleExpansion}\n                  isExpanded={subGroupExpanded} // Use expansion state from parent\n                  expandedGroups={expandedGroups} // Pass through for deeper nesting\n                  className={className}\n                />\n              );\n            })}\n\n          {/* Render questions in this group */}\n          {nestedQuestions\n            .filter((nq) =>\n              groupQuestions.some((gq) => gq.id === nq.question.id)\n            )\n            .map((questionGroup) => (\n              <NestedQuestionRenderer\n                key={questionGroup.question.id}\n                questionGroup={questionGroup}\n                renderQuestionInput={renderQuestionInput}\n                errors={errors}\n                className=\"\"\n              />\n            ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NestedGroupRenderer;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;;;AALA;;;;AA4BA,MAAM,sBAA0D,CAAC,EAC/D,KAAK,EACL,eAAe,CAAC,EAChB,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,MAAM,EACN,iBAAiB,EACjB,YAAY,kBAAkB,EAC9B,cAAc,EACd,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qEAAqE;IACrE,MAAM,aAAa,uBAAuB,YAAY,qBAAqB;IAE3E,MAAM,wBAAwB;QAC5B,IAAI,mBAAmB;YACrB,kBAAkB,MAAM,EAAE;QAC5B,OAAO;YACL,oBAAoB,CAAC;QACvB;IACF;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,MAAM,QAAQ,IAAI,EAAE;IAC3C,MAAM,wBAAwB,eAAe,MAAM,CAAC,CAAC,IACnD,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,EAAE,EAAE;IAG9C,+BAA+B;IAC/B,MAAM,YAAY,MAAM,SAAS,IAAI,EAAE;IACvC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAC;QACzC,MAAM,oBAAoB,SAAS,QAAQ,IAAI,EAAE;QACjD,OAAO,kBAAkB,IAAI,CAAC,CAAC,IAC7B,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,EAAE,EAAE;IAEhD;IAEA,sEAAsE;IACtE,IAAI,sBAAsB,MAAM,KAAK,KAAK,iBAAiB,MAAM,KAAK,GAAG;QACvE,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,4DAA4D,EACtE,eAAe,IAAI,CAAC,oCAAoC,CAAC,GAAG,GAC7D,CAAC,EAAE,WAAW;;0BAGf,6LAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,2BACC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCAE1B,6LAAC;4BAAG,WAAU;sCACX,MAAM,KAAK;;;;;;sCAEd,6LAAC;4BAAK,WAAU;;gCAAiD;gCAC7D,sBAAsB,MAAM,GAAG,iBAAiB,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC,GAAG;gCAAG;gCACzG,sBAAsB,MAAM,GAAG,iBAAiB,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,CAAC,GAAG,QAAQ,EAAE,UAAU,CAAC,GAAG,OAAQ,IAAI,MAAM;gCAAG;;;;;;;;;;;;;;;;;;YAMlI,4BACC,6LAAC;gBAAI,WAAU;;oBAEZ,iBACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAC;wBACJ,qEAAqE;wBACrE,MAAM,mBAAmB,iBAAiB,cAAc,CAAC,SAAS,EAAE,CAAC,GAAG;wBAExE,qBACE,6LAAC;4BAEC,OAAO;4BACP,cAAc,eAAe;4BAC7B,kBAAkB;4BAClB,iBAAiB;4BACjB,qBAAqB;4BACrB,QAAQ;4BACR,mBAAmB;4BACnB,YAAY;4BACZ,gBAAgB;4BAChB,WAAW;2BAVN,SAAS,EAAE;;;;;oBAatB;oBAGD,gBACE,MAAM,CAAC,CAAC,KACP,eAAe,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE,GAErD,GAAG,CAAC,CAAC,8BACJ,6LAAC,0JAAA,CAAA,UAAsB;4BAErB,eAAe;4BACf,qBAAqB;4BACrB,QAAQ;4BACR,WAAU;2BAJL,cAAc,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;AAW9C;GArHM;KAAA;uCAuHS", "debugId": null}}, {"offset": {"line": 2106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/utils/nestedGroups.ts"], "sourcesContent": ["import { Question, QuestionGroup } from \"@/types/formBuilder\";\n\n/**\n * Build nested group structure from flat array of groups and questions\n * This function creates a hierarchical structure where subgroups are nested within their parent groups\n */\nexport const buildNestedGroups = (\n  groups: QuestionGroup[],\n  questions: Question[]\n): QuestionGroup[] => {\n  const groupMap = new Map<number, QuestionGroup>();\n  \n  // Create a map of all groups with their subGroups and questions initialized\n  groups.forEach(group => {\n    // Get questions for this group\n    const groupQuestions = questions\n      .filter((q) => q.questionGroupId === group.id)\n      .sort((a, b) => a.position - b.position);\n    \n    groupMap.set(group.id, { \n      ...group, \n      subGroups: [],\n      question: groupQuestions\n    });\n  });\n\n  // Build the nested structure\n  const topLevelGroups: QuestionGroup[] = [];\n  groups.forEach(group => {\n    const groupWithSubGroups = groupMap.get(group.id)!;\n    \n    if (group.parentGroupId) {\n      // This is a child group, add it to its parent's subGroups\n      const parentGroup = groupMap.get(group.parentGroupId);\n      if (parentGroup) {\n        parentGroup.subGroups = parentGroup.subGroups || [];\n        parentGroup.subGroups.push(groupWithSubGroups);\n      }\n    } else {\n      // This is a top-level group\n      topLevelGroups.push(groupWithSubGroups);\n    }\n  });\n\n  return topLevelGroups;\n};\n\n/**\n * Create unified form items (groups and ungrouped questions) for rendering\n * This maintains the same ordering logic as the form builder\n */\nexport const createUnifiedFormItems = (\n  nestedGroups: QuestionGroup[],\n  ungroupedQuestions: Question[]\n): Array<{\n  type: 'group' | 'question';\n  data: QuestionGroup | Question;\n  order: number;\n  originalPosition?: number;\n}> => {\n  const items: Array<{\n    type: 'group' | 'question';\n    data: QuestionGroup | Question;\n    order: number;\n    originalPosition?: number;\n  }> = [];\n\n  // Add question groups\n  nestedGroups.forEach((group: QuestionGroup) => {\n    // For groups, find the minimum position of questions in the group (including subgroups)\n    const getAllGroupQuestions = (g: QuestionGroup): Question[] => {\n      const directQuestions = g.question || [];\n      const subGroupQuestions = (g.subGroups || []).flatMap(getAllGroupQuestions);\n      return [...directQuestions, ...subGroupQuestions];\n    };\n\n    const allGroupQuestions = getAllGroupQuestions(group);\n    const minQuestionPosition = allGroupQuestions.length > 0\n      ? Math.min(...allGroupQuestions.map(q => q.position))\n      : group.order;\n\n    items.push({\n      type: 'group',\n      data: group,\n      order: minQuestionPosition,\n      originalPosition: minQuestionPosition\n    });\n  });\n\n  // Add ungrouped questions\n  ungroupedQuestions.forEach((question: Question) => {\n    items.push({\n      type: 'question',\n      data: question,\n      order: question.position,\n      originalPosition: question.position\n    });\n  });\n\n  // Sort by order/position with secondary sort for consistency\n  return items.sort((a, b) => {\n    if (a.order === b.order) {\n      return (a.originalPosition || a.order) - (b.originalPosition || b.order);\n    }\n    return a.order - b.order;\n  });\n};\n\n/**\n * Get ungrouped questions (questions not belonging to any group)\n */\nexport const getUngroupedQuestions = (questions: Question[]): Question[] => {\n  return questions.filter(\n    (q) => q.questionGroupId === null || q.questionGroupId === undefined\n  );\n};\n\n/**\n * Get all group IDs from nested group structure (including subgroups)\n */\nexport const getAllGroupIds = (groups: QuestionGroup[]): number[] => {\n  const ids: number[] = [];\n  groups.forEach(group => {\n    ids.push(group.id);\n    if (group.subGroups && group.subGroups.length > 0) {\n      ids.push(...getAllGroupIds(group.subGroups));\n    }\n  });\n  return ids;\n};\n\n/**\n * Initialize expansion state for all groups (including nested ones)\n */\nexport const initializeGroupExpansionState = (\n  nestedGroups: QuestionGroup[],\n  defaultExpanded: boolean = true\n): Record<number, boolean> => {\n  const initialExpandedState: Record<number, boolean> = {};\n  const allGroupIds = getAllGroupIds(nestedGroups);\n  allGroupIds.forEach((groupId) => {\n    initialExpandedState[groupId] = defaultExpanded;\n  });\n  return initialExpandedState;\n};\n"], "names": [], "mappings": ";;;;;;;AAMO,MAAM,oBAAoB,CAC/B,QACA;IAEA,MAAM,WAAW,IAAI;IAErB,4EAA4E;IAC5E,OAAO,OAAO,CAAC,CAAA;QACb,+BAA+B;QAC/B,MAAM,iBAAiB,UACpB,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE,EAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAEzC,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE;YACrB,GAAG,KAAK;YACR,WAAW,EAAE;YACb,UAAU;QACZ;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAkC,EAAE;IAC1C,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,qBAAqB,SAAS,GAAG,CAAC,MAAM,EAAE;QAEhD,IAAI,MAAM,aAAa,EAAE;YACvB,0DAA0D;YAC1D,MAAM,cAAc,SAAS,GAAG,CAAC,MAAM,aAAa;YACpD,IAAI,aAAa;gBACf,YAAY,SAAS,GAAG,YAAY,SAAS,IAAI,EAAE;gBACnD,YAAY,SAAS,CAAC,IAAI,CAAC;YAC7B;QACF,OAAO;YACL,4BAA4B;YAC5B,eAAe,IAAI,CAAC;QACtB;IACF;IAEA,OAAO;AACT;AAMO,MAAM,yBAAyB,CACpC,cACA;IAOA,MAAM,QAKD,EAAE;IAEP,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAC;QACpB,wFAAwF;QACxF,MAAM,uBAAuB,CAAC;YAC5B,MAAM,kBAAkB,EAAE,QAAQ,IAAI,EAAE;YACxC,MAAM,oBAAoB,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE,OAAO,CAAC;YACtD,OAAO;mBAAI;mBAAoB;aAAkB;QACnD;QAEA,MAAM,oBAAoB,qBAAqB;QAC/C,MAAM,sBAAsB,kBAAkB,MAAM,GAAG,IACnD,KAAK,GAAG,IAAI,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACjD,MAAM,KAAK;QAEf,MAAM,IAAI,CAAC;YACT,MAAM;YACN,MAAM;YACN,OAAO;YACP,kBAAkB;QACpB;IACF;IAEA,0BAA0B;IAC1B,mBAAmB,OAAO,CAAC,CAAC;QAC1B,MAAM,IAAI,CAAC;YACT,MAAM;YACN,MAAM;YACN,OAAO,SAAS,QAAQ;YACxB,kBAAkB,SAAS,QAAQ;QACrC;IACF;IAEA,6DAA6D;IAC7D,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;QACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;YACvB,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;QACzE;QACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;IAC1B;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,OAAO,UAAU,MAAM,CACrB,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;AAE/D;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,MAAgB,EAAE;IACxB,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,IAAI,CAAC,MAAM,EAAE;QACjB,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;YACjD,IAAI,IAAI,IAAI,eAAe,MAAM,SAAS;QAC5C;IACF;IACA,OAAO;AACT;AAKO,MAAM,gCAAgC,CAC3C,cACA,kBAA2B,IAAI;IAE/B,MAAM,uBAAgD,CAAC;IACvD,MAAM,cAAc,eAAe;IACnC,YAAY,OAAO,CAAC,CAAC;QACnB,oBAAoB,CAAC,QAAQ,GAAG;IAClC;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/useAuth.tsx"], "sourcesContent": ["import {\r\n  setAuthenticatedUser,\r\n  setAuthError,\r\n  setAuthLoading,\r\n  setUnauthenticated,\r\n} from \"@/redux/slices/authSlice\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { UserSession } from \"@/types/authTypes\";\r\nimport { AxiosError, isAxiosError } from \"axios\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst useAuth = (options?: { skipFetchUser?: boolean }) => {\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { status, user, error } = useSelector((state: RootState) => state.auth);\r\n\r\n  const fetchUserData = async () => {\r\n    try {\r\n      dispatch(setAuthLoading());\r\n      const response = await axios.get(`/users/me`);\r\n      const userData: UserSession = response.data;\r\n      dispatch(setAuthenticatedUser(userData));\r\n    } catch (error) {\r\n      // Handle errors, especially 401 Unauthorized\r\n      dispatch(setUnauthenticated());\r\n\r\n      if (isAxiosError(error)) {\r\n        console.error(\r\n          \"Auth error:\",\r\n          error.response?.status,\r\n          error.response?.data\r\n        );\r\n\r\n        // If error is 401 Unauthorized (including expired token)\r\n        if (error.response?.status === 401) {\r\n          // Check if we're on a form-test route\r\n          if (pathname.startsWith(\"/form-submission\")) {\r\n            // Don't redirect for form-test routes, let the component handle it\r\n            return;\r\n          }\r\n          router.push(\"/\");\r\n        } else {\r\n          // For other errors\r\n          dispatch(\r\n            setAuthError(error.response?.data?.message || error.message)\r\n          );\r\n        }\r\n      } else {\r\n        dispatch(\r\n          setAuthError(\r\n            error instanceof Error\r\n              ? error.message\r\n              : \"An unknown error occurred.\"\r\n          )\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!options?.skipFetchUser) {\r\n      fetchUserData();\r\n    }\r\n  }, [options?.skipFetchUser]);\r\n\r\n  // Add event listener for storage changes to handle logout across tabs\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === \"logout\" && e.newValue === \"true\") {\r\n        dispatch(setUnauthenticated());\r\n        // Check if we're on a form-test route\r\n        if (pathname.startsWith(\"/form-submission\")) {\r\n          // For form-test routes, redirect to the sign-in page of the same form\r\n          const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n          if (hashedId) {\r\n            router.push(`/form-submission/${hashedId}/sign-in`);\r\n          } else {\r\n            router.push(\"/\");\r\n          }\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch, router, pathname]);\r\n\r\n  const refreshAuthState = () => {\r\n    fetchUserData();\r\n  };\r\n\r\n  const signin = async (\r\n    data: { email: string; password: string },\r\n    onSuccess?: () => void,\r\n    onError?: (errorType?: string) => void\r\n  ) => {\r\n    try {\r\n      await axios.post(`/users/login`, data);\r\n      await fetchUserData();\r\n      onSuccess?.();\r\n    } catch (error) {\r\n      if (error instanceof AxiosError) {\r\n        const errorType = error.response?.data?.errorType;\r\n        onError?.(errorType);\r\n      } else {\r\n        onError?.();\r\n      }\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await axios.post(`/users/logout`);\r\n      // Notify other tabs about logout\r\n      localStorage.setItem(\"logout\", \"true\");\r\n      // Remove the flag immediately to ensure future logout events still trigger\r\n      setTimeout(() => localStorage.removeItem(\"logout\"), 100);\r\n    } finally {\r\n      dispatch(setUnauthenticated());\r\n      // Check if we're on a form-test route\r\n      if (pathname.startsWith(\"/form-submission\")) {\r\n        // For form-test routes, redirect to the sign-in page of the same form\r\n        const hashedId = pathname.split(\"/\")[2]; // Extract hashedId from path\r\n        if (hashedId) {\r\n          router.push(`/form-submission/${hashedId}/sign-in`);\r\n        } else {\r\n          router.push(\"/\");\r\n        }\r\n      } else {\r\n        router.push(\"/\");\r\n      }\r\n    }\r\n  };\r\n\r\n  return {\r\n    status,\r\n    user,\r\n    error,\r\n    isAuthenticated: status === \"authenticated\",\r\n    isLoading: status === \"loading\",\r\n    refreshAuthState,\r\n    signin,\r\n    logout,\r\n  };\r\n};\r\n\r\nexport { useAuth };\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,UAAU,CAAC;;IACf,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;+BAAE,CAAC,QAAqB,MAAM,IAAI;;IAE5E,MAAM,gBAAgB;QACpB,IAAI;YACF,SAAS,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YACtB,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YAC5C,MAAM,WAAwB,SAAS,IAAI;YAC3C,SAAS,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;YAE1B,IAAI,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACvB,QAAQ,KAAK,CACX,eACA,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE;gBAGlB,yDAAyD;gBACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,sCAAsC;oBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;wBAC3C,mEAAmE;wBACnE;oBACF;oBACA,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,mBAAmB;oBACnB,SACE,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;gBAE/D;YACF,OAAO;gBACL,SACE,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EACT,iBAAiB,QACb,MAAM,OAAO,GACb;YAGV;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,SAAS,eAAe;gBAC3B;YACF;QACF;4BAAG;QAAC,SAAS;KAAc;IAE3B,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;yDAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAC/C,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;wBAC1B,sCAAsC;wBACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;4BAC3C,sEAAsE;4BACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;4BACtE,IAAI,UAAU;gCACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;4BACpD,OAAO;gCACL,OAAO,IAAI,CAAC;4BACd;wBACF,OAAO;4BACL,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;qCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;4BAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,SAAS,OACb,MACA,WACA;QAEA,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE;YACjC,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,iJAAA,CAAA,aAAU,EAAE;gBAC/B,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;gBACxC,UAAU;YACZ,OAAO;gBACL;YACF;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;YAChC,iCAAiC;YACjC,aAAa,OAAO,CAAC,UAAU;YAC/B,2EAA2E;YAC3E,WAAW,IAAM,aAAa,UAAU,CAAC,WAAW;QACtD,SAAU;YACR,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;YAC1B,sCAAsC;YACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB;gBAC3C,sEAAsE;gBACtE,MAAM,WAAW,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;gBACtE,IAAI,UAAU;oBACZ,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;gBACpD,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA,iBAAiB,WAAW;QAC5B,WAAW,WAAW;QACtB;QACA;QACA;IACF;AACF;GA3IM;;QACa,4JAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACI,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/form-submission/%5BhashedId%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo, useC<PERSON>back } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { use<PERSON>ara<PERSON>, useRouter } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { fetchFormBuilderData } from \"@/lib/api/form-builder\";\r\nimport { createAnswerSubmission, fetchProjectById } from \"@/lib/api/projects\";\r\nimport { Project } from \"@/types\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport { ChevronDown, ChevronRight, ArrowRight } from \"lucide-react\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { TableInput } from \"@/components/form-inputs/TableInput\";\r\nimport {\r\n  getVisibleQuestions,\r\n  cleanupHiddenAnswers,\r\n  validateVisibleQuestions,\r\n  getNestedQuestions,\r\n} from \"@/lib/conditionalQuestions\";\r\nimport NestedQuestionRenderer from \"@/components/form-inputs/NestedQuestionRenderer\";\r\nimport NestedGroupRenderer from \"@/components/form-inputs/NestedGroupRenderer\";\r\nimport {\r\n  buildNestedGroups,\r\n  createUnifiedFormItems,\r\n  getUngroupedQuestions,\r\n  initializeGroupExpansionState\r\n} from \"@/lib/utils/nestedGroups\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\n\r\nexport default function FormTestPage() {\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n  const { hashedId } = useParams();\r\n  const hashedIdString = hashedId as string;\r\n  const projectId = decode(hashedIdString);\r\n\r\n  // Authentication check\r\n  const { isAuthenticated, isLoading: authLoading } = useAuth();\r\n\r\n  // Redirect to sign-in if not authenticated\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push(`/form-submission/${hashedId}/sign-in`);\r\n    }\r\n  }, [authLoading, isAuthenticated, router, hashedId]);\r\n\r\n  const [answers, setAnswers] = useState<Record<string, any>>({});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);\r\n  const [nestedQuestions, setNestedQuestions] = useState<\r\n    Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n      isFollowUp: boolean;\r\n      followUps: Array<{\r\n        question: Question;\r\n        isVisible: boolean;\r\n      }>;\r\n    }>\r\n  >([]);\r\n  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(\r\n    {}\r\n  );\r\n\r\n  // Query keys\r\n  const formBuilderDataQueryKey = [\"formBuilderData\", projectId];\r\n\r\n  // Fetch form builder data (unified questions and groups)\r\n  const { data: formBuilderData, isLoading, isError } = useQuery({\r\n    queryKey: formBuilderDataQueryKey,\r\n    queryFn: () => fetchFormBuilderData({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  // Extract questions and groups from the unified data\r\n  const questionsData: Question[] = React.useMemo(() => {\r\n    if (!formBuilderData?.items) {\r\n      return [];\r\n    }\r\n\r\n    // Extract questions from the items array\r\n    const extractedQuestions: Question[] = [];\r\n\r\n    formBuilderData.items.forEach((item: any) => {\r\n      if (item.type === \"question\") {\r\n        extractedQuestions.push(item);\r\n      } else if (item.type === \"group\" && item.questions) {\r\n        // Add questions from groups\r\n        item.questions.forEach((question: any) => {\r\n          extractedQuestions.push(question);\r\n        });\r\n      }\r\n    });\r\n\r\n    return extractedQuestions;\r\n  }, [formBuilderData]);\r\n\r\n  // Extract question groups from the unified data\r\n  const questionGroups: QuestionGroup[] = React.useMemo(() => {\r\n    if (!formBuilderData?.items) {\r\n      return [];\r\n    }\r\n\r\n    return formBuilderData.items\r\n      .filter((item: any) => item.type === \"group\")\r\n      .map((item: any) => ({\r\n        ...item,\r\n        question: item.questions || []\r\n      }));\r\n  }, [formBuilderData]);\r\n\r\n  // Fetch project data to get the project name\r\n  const { data: projectData } = useQuery<Project>({\r\n    queryKey: [\"project\", projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  // Show loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-muted-foreground\">Loading form data...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (isError) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-red-500 mb-4\">Error loading form data. Please try again.</p>\r\n          <button\r\n            onClick={() => queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey })}\r\n            className=\"btn-primary\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Don't render if not authenticated\r\n  if (authLoading || !isAuthenticated) {\r\n    return <Spinner />;\r\n  }\r\n\r\n  // Initialize answers when questions change\r\n  useEffect(() => {\r\n    if (questionsData && questionsData.length > 0) {\r\n      const initialAnswers: Record<string, any> = {};\r\n      questionsData.forEach((question) => {\r\n        initialAnswers[question.id] =\r\n          question.inputType === \"selectmany\" ? [] : \"\";\r\n      });\r\n      setAnswers(initialAnswers);\r\n    }\r\n  }, [questionsData.length]); // Only depend on questionsData.length to avoid infinite loops\r\n\r\n  // Update visible questions when answers or questions change\r\n  useEffect(() => {\r\n    if (questionsData && questionsData.length > 0) {\r\n      const newVisibleQuestions = getVisibleQuestions(questionsData, answers);\r\n      setVisibleQuestions(newVisibleQuestions);\r\n\r\n      // Calculate nested question structure\r\n      const newNestedQuestions = getNestedQuestions(questionsData, answers);\r\n      setNestedQuestions(newNestedQuestions);\r\n    }\r\n  }, [questionsData, answers]);\r\n\r\n  // Separate effect for cleaning up hidden answers to prevent infinite loops\r\n  useEffect(() => {\r\n    if (visibleQuestions.length > 0 && Object.keys(answers).length > 0) {\r\n      const cleanedAnswers = cleanupHiddenAnswers(answers, visibleQuestions);\r\n      const answersChanged = Object.keys(cleanedAnswers).length !== Object.keys(answers).length ||\r\n        Object.keys(cleanedAnswers).some(key => cleanedAnswers[key] !== answers[key]);\r\n\r\n      if (answersChanged) {\r\n        setAnswers(cleanedAnswers);\r\n      }\r\n    }\r\n  }, [visibleQuestions]); // Only depend on visibleQuestions, not answers\r\n\r\n  // Build nested group structure - memoized to prevent recalculation\r\n  const nestedQuestionGroups = useMemo(() => {\r\n    return buildNestedGroups(questionGroups, questionsData || []);\r\n  }, [questionGroups, questionsData]);\r\n\r\n  // Initialize all groups (including nested ones) as expanded when questionGroups change\r\n  useEffect(() => {\r\n    if (nestedQuestionGroups.length > 0) {\r\n      const initialExpandedState = initializeGroupExpansionState(nestedQuestionGroups, true);\r\n      setExpandedGroups(initialExpandedState);\r\n    }\r\n  }, [nestedQuestionGroups.length]); // Only depend on length to avoid infinite loops\r\n\r\n  // Get ungrouped questions - memoized to prevent recalculation\r\n  const ungroupedQuestions = useMemo(() => {\r\n    return getUngroupedQuestions(questionsData || []);\r\n  }, [questionsData]);\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const unifiedFormItems = useMemo(() => {\r\n    return createUnifiedFormItems(nestedQuestionGroups, ungroupedQuestions);\r\n  }, [nestedQuestionGroups, ungroupedQuestions]);\r\n\r\n  // Toggle group expansion - memoized to prevent unnecessary re-renders\r\n  const toggleGroupExpansion = useCallback((groupId: number) => {\r\n    setExpandedGroups((prev) => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId],\r\n    }));\r\n  }, []);\r\n\r\n  const submitAnswersMutation = useMutation({\r\n    mutationFn: async (answers: Record<string, any>) => {\r\n      // Transform answers into the format expected by createAnswerSubmission\r\n      // Only submit answers for visible questions\r\n      const formattedAnswers =\r\n        questionsData\r\n          ?.map((question) => {\r\n            const answerValue = answers[question.id];\r\n            const isSelectMany = question.inputType === \"selectmany\";\r\n            const isSelectOne = question.inputType === \"selectone\";\r\n\r\n            // For non-select questions, skip if no value\r\n            if (!isSelectMany && !isSelectOne) {\r\n              if (answerValue === undefined || answerValue === null || answerValue === \"\") {\r\n                return null;\r\n              }\r\n            }\r\n\r\n            // For selectone, skip if no value\r\n            if (isSelectOne && (!answerValue || answerValue.trim() === \"\")) {\r\n              return null;\r\n            }\r\n\r\n            // For selectmany, we'll always include it even if empty (backend expects it)\r\n\r\n            // Initialize questionOptionId\r\n            let questionOptionId: number | number[] | undefined;\r\n\r\n            // Handle questionOptionId for selectmany and selectone\r\n            if (isSelectMany && Array.isArray(answerValue) && question.questionOptions) {\r\n              const optionIds = answerValue\r\n                .map((val: string) => {\r\n                  const option = question.questionOptions.find(\r\n                    (opt) => opt.label === val\r\n                  );\r\n                  return option?.id;\r\n                })\r\n                .filter((id: number | undefined) => id !== undefined) as number[];\r\n\r\n              // For selectmany, questionOptionId MUST be an array (even if empty)\r\n              questionOptionId = optionIds.length > 0 ? optionIds : [];\r\n            } else if (isSelectOne && answerValue && question.questionOptions) {\r\n              const option = question.questionOptions.find(\r\n                (opt) => opt.label === answerValue\r\n              );\r\n              // For selectone, questionOptionId MUST be a single number\r\n              questionOptionId = option?.id;\r\n\r\n              // If we can't find the option ID, skip this answer\r\n              if (questionOptionId === undefined) {\r\n                console.warn(`Could not find option ID for selectone question ${question.id} with value \"${answerValue}\"`);\r\n                return null;\r\n              }\r\n            }\r\n\r\n            // Convert value to the correct type\r\n            let formattedValue: string | number | boolean | undefined;\r\n            if (isSelectMany) {\r\n              // For selectmany, send the selected labels as a comma-separated string\r\n              formattedValue = Array.isArray(answerValue) ? answerValue.join(\", \") : \"\";\r\n            } else if (question.inputType === \"number\" || question.inputType === \"decimal\") {\r\n              formattedValue = answerValue ? Number(answerValue) : undefined;\r\n            } else if (\r\n              question.inputType === \"date\" ||\r\n              question.inputType === \"dateandtime\"\r\n            ) {\r\n              formattedValue = answerValue || undefined;\r\n            } else if (question.inputType === \"table\") {\r\n              // For table input type, convert the array of cell values to JSON string\r\n              formattedValue =\r\n                Array.isArray(answerValue) && answerValue.length > 0\r\n                  ? JSON.stringify(answerValue)\r\n                  : undefined;\r\n            } else {\r\n              formattedValue = answerValue ? String(answerValue) : undefined;\r\n            }\r\n\r\n            // Ensure we have a valid value\r\n            if (formattedValue === undefined || formattedValue === null) {\r\n              return null;\r\n            }\r\n\r\n            // Determine questionOptionId value based on question type\r\n            let finalQuestionOptionId: number | number[] | undefined;\r\n\r\n            if (isSelectMany) {\r\n              // For selectmany, questionOptionId MUST always be an array (backend requirement)\r\n              finalQuestionOptionId = Array.isArray(questionOptionId) ? questionOptionId : [];\r\n            } else if (isSelectOne) {\r\n              // For selectone, questionOptionId must be a number or undefined\r\n              finalQuestionOptionId = typeof questionOptionId === \"number\" ? questionOptionId : undefined;\r\n            } else {\r\n              // For other input types, questionOptionId should be undefined\r\n              finalQuestionOptionId = undefined;\r\n            }\r\n\r\n            // Create the answer object with all required fields for backend validation\r\n            const answer: any = {\r\n              projectId: Number(projectId),\r\n              questionId: question.id,\r\n              answerType: String(question.inputType),\r\n              value: formattedValue,\r\n              isOtherOption: false,\r\n            };\r\n\r\n            // Add questionOptionId only if it's defined and valid\r\n            if (finalQuestionOptionId !== undefined) {\r\n              answer.questionOptionId = finalQuestionOptionId;\r\n            }\r\n\r\n            return answer;\r\n          })\r\n          .filter((answer) => answer !== null) || []; // Remove null entries\r\n\r\n      // Validate that we have at least some answers to submit\r\n      if (formattedAnswers.length === 0) {\r\n        throw new Error(\"No valid answers to submit. Please fill out at least one field.\");\r\n      }\r\n\r\n      // Log submission data for debugging\r\n\r\n      // Call createAnswerSubmission\r\n      return await createAnswerSubmission(formattedAnswers);\r\n    },\r\n    onSuccess: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Form submitted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setAnswers({}); // Reset form\r\n\r\n      // Dispatch custom event to notify other components about the submission\r\n      window.dispatchEvent(new Event(\"form-submitted\"));\r\n\r\n      // Additionally, store in localStorage to notify other tabs/windows\r\n      localStorage.setItem(\"form_submitted\", Date.now().toString());\r\n    },\r\n    onError: (error: any) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to submit form. Please try again.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      console.error(\"Submission Error:\", error);\r\n    },\r\n    onSettled: () => {\r\n      setIsSubmitting(false);\r\n    },\r\n  });\r\n\r\n  // This duplicate useEffect has been removed to prevent infinite loops\r\n\r\n  const handleInputChange = useCallback((questionId: number, value: any) => {\r\n    setAnswers((prev) => ({\r\n      ...prev,\r\n      [questionId]: value,\r\n    }));\r\n    setErrors((prev) => ({\r\n      ...prev,\r\n      [questionId]: \"\",\r\n    }));\r\n  }, []);\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n    let isValid = true;\r\n\r\n    // Only validate visible questions\r\n    visibleQuestions.forEach((question) => {\r\n      if (question.isRequired) {\r\n        const value = answers[question.id];\r\n        if (\r\n          (typeof value === \"string\" && !value.trim()) ||\r\n          (Array.isArray(value) && value.length === 0) ||\r\n          value === undefined ||\r\n          value === null\r\n        ) {\r\n          newErrors[question.id] = `${question.label} is required`;\r\n          isValid = false;\r\n        }\r\n      }\r\n    });\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!validateForm()) return;\r\n    setIsSubmitting(true);\r\n    submitAnswersMutation.mutate(answers);\r\n  };\r\n\r\n  // Helper function to check if a question is a follow-up question\r\n  const isFollowUpQuestion = (questionId: number): boolean => {\r\n    if (!questionsData) return false;\r\n    return questionsData.some((q) =>\r\n      q.questionOptions?.some((option) => option.nextQuestionId === questionId)\r\n    );\r\n  };\r\n\r\n  // Helper function to check if a question has follow-up questions\r\n  const hasFollowUpQuestions = (question: Question): boolean => {\r\n    return question.questionOptions?.some((option) => option.nextQuestionId) || false;\r\n  };\r\n\r\n  const renderQuestionInput = (question: Question) => {\r\n    const value =\r\n      answers[question.id] ?? (question.inputType === \"selectmany\" ? [] : \"\");\r\n\r\n    switch (question.inputType) {\r\n      case \"text\":\r\n        if (question.hint?.includes(\"multiline\")) {\r\n          return (\r\n            <Textarea\r\n              value={value}\r\n              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>\r\n                handleInputChange(question.id, e.target.value)\r\n              }\r\n              placeholder={question.placeholder || \"Your answer\"}\r\n              required={question.isRequired}\r\n            />\r\n          );\r\n        }\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"number\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"number\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"decimal\":\r\n        return (\r\n          <input\r\n            className=\"input-field w-full\"\r\n            type=\"number\"\r\n            step={\"any\"}\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.placeholder || \"Your answer\"}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"selectone\":\r\n        return (\r\n          <RadioGroup\r\n            value={value}\r\n            onValueChange={(val: string) => handleInputChange(question.id, val)}\r\n            required={question.isRequired}\r\n          >\r\n            <div className=\"space-y-2\">\r\n              {question.questionOptions?.map((option) => (\r\n                <div key={`option-${option.id}`} className=\"flex items-center space-x-2\">\r\n                  <RadioGroupItem\r\n                    value={option.label}\r\n                    id={`option-${question.id}-${option.id}`}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`option-${question.id}-${option.id}`}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    {option.label}\r\n                  </Label>\r\n                  {option.sublabel && (\r\n                    <p className=\"text-sm text-neutral-700 ml-4\">\r\n                      {`(${option.sublabel})`}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </RadioGroup>\r\n        );\r\n\r\n      case \"selectmany\":\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            {question.questionOptions?.map((option) => (\r\n              <div key={option.id} className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  id={`option-${option.id}`}\r\n                  checked={(value || []).includes(option.label)}\r\n                  onCheckedChange={(checked) => {\r\n                    const currentValues = value || [];\r\n                    const newValues = checked\r\n                      ? [...currentValues, option.label]\r\n                      : currentValues.filter((v: string) => v !== option.label);\r\n                    handleInputChange(question.id, newValues);\r\n                  }}\r\n                />\r\n                <Label\r\n                  htmlFor={`option-${option.id}`}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  {option.label}\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n\r\n      case \"date\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"date\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.placeholder || \"Select date\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"dateandtime\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <input\r\n              className=\"input-field w-full\"\r\n              type=\"time\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.placeholder || \"Select time\"}\r\n              required={question.isRequired}\r\n            />\r\n          </div>\r\n        );\r\n\r\n      case \"table\":\r\n        return (\r\n          <TableInput\r\n            questionId={question.id}\r\n            value={value}\r\n            onChange={(cellValues) =>\r\n              handleInputChange(question.id, cellValues)\r\n            }\r\n            required={question.isRequired}\r\n            tableLabel={question.label}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Render a single question with its input and visual indicators\r\n  const renderQuestion = (question: Question) => {\r\n    const isFollowUp = isFollowUpQuestion(question.id);\r\n    const hasFollowUps = hasFollowUpQuestions(question);\r\n\r\n    return (\r\n      <div\r\n        key={question.id}\r\n        className={`border rounded-md p-4 ${\r\n          isFollowUp\r\n            ? \"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20\"\r\n            : \"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\"\r\n        }`}\r\n      >\r\n        <div className=\"mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {question.label}\r\n              {question.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {/* Visual indicators */}\r\n            {isFollowUp && (\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200\">\r\n                <ArrowRight className=\"w-3 h-3 mr-1\" />\r\n                Follow-up\r\n              </span>\r\n            )}\r\n            {hasFollowUps && (\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200\">\r\n                Has conditions\r\n              </span>\r\n            )}\r\n          </div>\r\n          {question.hint && (\r\n            <p className={`text-sm mt-1 ${\r\n              isFollowUp\r\n                ? \"text-primary-700 dark:text-primary-300\"\r\n                : \"text-muted-foreground\"\r\n            }`}>\r\n              {question.hint}\r\n            </p>\r\n          )}\r\n          {errors[question.id] && (\r\n            <p className=\"text-sm text-red-500 mt-1\">\r\n              {errors[question.id]}\r\n            </p>\r\n          )}\r\n        </div>\r\n        <div className=\"mt-2\">{renderQuestionInput(question)}</div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Show loading spinner while checking authentication or loading data\r\n  if (authLoading || isLoading) return <Spinner />;\r\n\r\n  // Don't render anything if not authenticated (redirect will happen)\r\n  if (!isAuthenticated) return null;\r\n\r\n  if (isError || !questionsData) {\r\n    return (\r\n      <p className=\"text-sm text-red-500\">\r\n        Error loading form. Please try again.\r\n      </p>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6\">\r\n      <div className=\"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700\">\r\n        <h2 className=\"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        Form Submission {projectData?.name ? ` for ${projectData.name}` : \"\"}\r\n        </h2>\r\n        <form onSubmit={handleSubmit} className=\"p-6\">\r\n          <div className=\"space-y-6\">\r\n            {!questionsData || questionsData.length === 0 ? (\r\n              <div className=\"text-center py-12\">\r\n                <p className=\"text-muted-foreground\">\r\n                  This form has no questions yet.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              // Render unified form items (groups and individual questions) with conditional logic\r\n              unifiedFormItems.map((item) => {\r\n                if (item.type === \"group\") {\r\n                  const group = item.data as QuestionGroup;\r\n                  const isExpanded = expandedGroups[group.id];\r\n\r\n                  return (\r\n                    <NestedGroupRenderer\r\n                      key={`group-${group.id}`}\r\n                      group={group}\r\n                      nestingLevel={0}\r\n                      visibleQuestions={visibleQuestions}\r\n                      nestedQuestions={nestedQuestions}\r\n                      renderQuestionInput={renderQuestionInput}\r\n                      errors={errors}\r\n                      onToggleExpansion={toggleGroupExpansion}\r\n                      isExpanded={isExpanded}\r\n                      expandedGroups={expandedGroups}\r\n                      className=\"\"\r\n                    />\r\n                  );\r\n                } else {\r\n                  const question = item.data as Question;\r\n                  // Only render ungrouped questions that are visible\r\n                  if (!visibleQuestions.some((vq) => vq.id === question.id)) {\r\n                    return null;\r\n                  }\r\n\r\n                  // Find the nested question structure for this question\r\n                  const nestedQuestion = nestedQuestions.find(\r\n                    (nq) => nq.question.id === question.id\r\n                  );\r\n\r\n                  if (nestedQuestion) {\r\n                    return (\r\n                      <NestedQuestionRenderer\r\n                        key={question.id}\r\n                        questionGroup={nestedQuestion}\r\n                        renderQuestionInput={renderQuestionInput}\r\n                        errors={errors}\r\n                        className=\"\"\r\n                      />\r\n                    );\r\n                  }\r\n\r\n                  return renderQuestion(question);\r\n                }\r\n              })\r\n            )}\r\n\r\n            {/* Submit Button and Status Messages */}\r\n            {questionsData.length > 0 && (\r\n              <div className=\"mt-6 flex justify-end\">\r\n                <button\r\n                  className=\"btn-primary\"\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                >\r\n                  {isSubmitting ? \"Submitting...\" : \"Submit Form\"}\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n            {questionsData.length === 0 && (\r\n              <div className=\"text-center py-12\">\r\n                <p className=\"text-muted-foreground\">\r\n                  This form has no questions yet.\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {questionsData &&\r\n              questionsData.length > 0 &&\r\n              visibleQuestions.length === 0 && (\r\n                <div className=\"text-center py-12\">\r\n                  <p className=\"text-muted-foreground\">\r\n                    No questions are currently visible. Please check your form\r\n                    configuration.\r\n                  </p>\r\n                </div>\r\n              )}\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAMA;AACA;AACA;AAMA;;;AAlCA;;;;;;;;;;;;;;;;;;;;;AAoCe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,iBAAiB;IACvB,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,uBAAuB;IACvB,MAAM,EAAE,eAAe,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAE1D,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,SAAS,QAAQ,CAAC;YACpD;QACF;iCAAG;QAAC;QAAa;QAAiB;QAAQ;KAAS;IAEnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAUnD,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAGH,aAAa;IACb,MAAM,0BAA0B;QAAC;QAAmB;KAAU;IAE9D,yDAAyD;IACzD,MAAM,EAAE,MAAM,eAAe,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC7D,UAAU;QACV,OAAO;qCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QAC5D,SAAS,CAAC,CAAC;IACb;IAEA,qDAAqD;IACrD,MAAM,gBAA4B,6JAAA,CAAA,UAAK,CAAC,OAAO;+CAAC;YAC9C,IAAI,CAAC,iBAAiB,OAAO;gBAC3B,OAAO,EAAE;YACX;YAEA,yCAAyC;YACzC,MAAM,qBAAiC,EAAE;YAEzC,gBAAgB,KAAK,CAAC,OAAO;uDAAC,CAAC;oBAC7B,IAAI,KAAK,IAAI,KAAK,YAAY;wBAC5B,mBAAmB,IAAI,CAAC;oBAC1B,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,SAAS,EAAE;wBAClD,4BAA4B;wBAC5B,KAAK,SAAS,CAAC,OAAO;mEAAC,CAAC;gCACtB,mBAAmB,IAAI,CAAC;4BAC1B;;oBACF;gBACF;;YAEA,OAAO;QACT;8CAAG;QAAC;KAAgB;IAEpB,gDAAgD;IAChD,MAAM,iBAAkC,6JAAA,CAAA,UAAK,CAAC,OAAO;gDAAC;YACpD,IAAI,CAAC,iBAAiB,OAAO;gBAC3B,OAAO,EAAE;YACX;YAEA,OAAO,gBAAgB,KAAK,CACzB,MAAM;wDAAC,CAAC,OAAc,KAAK,IAAI,KAAK;uDACpC,GAAG;wDAAC,CAAC,OAAc,CAAC;wBACnB,GAAG,IAAI;wBACP,UAAU,KAAK,SAAS,IAAI,EAAE;oBAChC,CAAC;;QACL;+CAAG;QAAC;KAAgB;IAEpB,6CAA6C;IAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QAC9C,UAAU;YAAC;YAAW;SAAU;QAChC,OAAO;qCAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACxD,SAAS,CAAC,CAAC;IACb;IAEA,qBAAqB;IACrB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,mBAAmB;IACnB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAoB;;;;;;kCACjC,6LAAC;wBACC,SAAS,IAAM,YAAY,iBAAiB,CAAC;gCAAE,UAAU;4BAAwB;wBACjF,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,oCAAoC;IACpC,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IACjB;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;gBAC7C,MAAM,iBAAsC,CAAC;gBAC7C,cAAc,OAAO;8CAAC,CAAC;wBACrB,cAAc,CAAC,SAAS,EAAE,CAAC,GACzB,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG;oBAC/C;;gBACA,WAAW;YACb;QACF;iCAAG;QAAC,cAAc,MAAM;KAAC,GAAG,8DAA8D;IAE1F,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;gBAC7C,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;gBAC/D,oBAAoB;gBAEpB,sCAAsC;gBACtC,MAAM,qBAAqB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;gBAC7D,mBAAmB;YACrB;QACF;iCAAG;QAAC;QAAe;KAAQ;IAE3B,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;gBAClE,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS;gBACrD,MAAM,iBAAiB,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,IACvF,OAAO,IAAI,CAAC,gBAAgB,IAAI;8CAAC,CAAA,MAAO,cAAc,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;;gBAE9E,IAAI,gBAAgB;oBAClB,WAAW;gBACb;YACF;QACF;iCAAG;QAAC;KAAiB,GAAG,+CAA+C;IAEvE,mEAAmE;IACnE,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YACnC,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB,iBAAiB,EAAE;QAC9D;qDAAG;QAAC;QAAgB;KAAc;IAElC,uFAAuF;IACvF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,qBAAqB,MAAM,GAAG,GAAG;gBACnC,MAAM,uBAAuB,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE,sBAAsB;gBACjF,kBAAkB;YACpB;QACF;iCAAG;QAAC,qBAAqB,MAAM;KAAC,GAAG,gDAAgD;IAEnF,8DAA8D;IAC9D,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YACjC,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,EAAE;QAClD;mDAAG;QAAC;KAAc;IAElB,6FAA6F;IAC7F,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YAC/B,OAAO,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE,sBAAsB;QACtD;iDAAG;QAAC;QAAsB;KAAmB;IAE7C,sEAAsE;IACtE,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACxC;kEAAkB,CAAC,OAAS,CAAC;wBAC3B,GAAG,IAAI;wBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;oBAC3B,CAAC;;QACH;yDAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACxC,UAAU;+DAAE,OAAO;gBACjB,uEAAuE;gBACvE,4CAA4C;gBAC5C,MAAM,mBACJ,eACI;uEAAI,CAAC;wBACL,MAAM,cAAc,OAAO,CAAC,SAAS,EAAE,CAAC;wBACxC,MAAM,eAAe,SAAS,SAAS,KAAK;wBAC5C,MAAM,cAAc,SAAS,SAAS,KAAK;wBAE3C,6CAA6C;wBAC7C,IAAI,CAAC,gBAAgB,CAAC,aAAa;4BACjC,IAAI,gBAAgB,aAAa,gBAAgB,QAAQ,gBAAgB,IAAI;gCAC3E,OAAO;4BACT;wBACF;wBAEA,kCAAkC;wBAClC,IAAI,eAAe,CAAC,CAAC,eAAe,YAAY,IAAI,OAAO,EAAE,GAAG;4BAC9D,OAAO;wBACT;wBAEA,6EAA6E;wBAE7E,8BAA8B;wBAC9B,IAAI;wBAEJ,uDAAuD;wBACvD,IAAI,gBAAgB,MAAM,OAAO,CAAC,gBAAgB,SAAS,eAAe,EAAE;4BAC1E,MAAM,YAAY,YACf,GAAG;6FAAC,CAAC;oCACJ,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI;4GAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;;oCAEzB,OAAO,QAAQ;gCACjB;4FACC,MAAM;6FAAC,CAAC,KAA2B,OAAO;;4BAE7C,oEAAoE;4BACpE,mBAAmB,UAAU,MAAM,GAAG,IAAI,YAAY,EAAE;wBAC1D,OAAO,IAAI,eAAe,eAAe,SAAS,eAAe,EAAE;4BACjE,MAAM,SAAS,SAAS,eAAe,CAAC,IAAI;0FAC1C,CAAC,MAAQ,IAAI,KAAK,KAAK;;4BAEzB,0DAA0D;4BAC1D,mBAAmB,QAAQ;4BAE3B,mDAAmD;4BACnD,IAAI,qBAAqB,WAAW;gCAClC,QAAQ,IAAI,CAAC,CAAC,gDAAgD,EAAE,SAAS,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;gCACzG,OAAO;4BACT;wBACF;wBAEA,oCAAoC;wBACpC,IAAI;wBACJ,IAAI,cAAc;4BAChB,uEAAuE;4BACvE,iBAAiB,MAAM,OAAO,CAAC,eAAe,YAAY,IAAI,CAAC,QAAQ;wBACzE,OAAO,IAAI,SAAS,SAAS,KAAK,YAAY,SAAS,SAAS,KAAK,WAAW;4BAC9E,iBAAiB,cAAc,OAAO,eAAe;wBACvD,OAAO,IACL,SAAS,SAAS,KAAK,UACvB,SAAS,SAAS,KAAK,eACvB;4BACA,iBAAiB,eAAe;wBAClC,OAAO,IAAI,SAAS,SAAS,KAAK,SAAS;4BACzC,wEAAwE;4BACxE,iBACE,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,IAC/C,KAAK,SAAS,CAAC,eACf;wBACR,OAAO;4BACL,iBAAiB,cAAc,OAAO,eAAe;wBACvD;wBAEA,+BAA+B;wBAC/B,IAAI,mBAAmB,aAAa,mBAAmB,MAAM;4BAC3D,OAAO;wBACT;wBAEA,0DAA0D;wBAC1D,IAAI;wBAEJ,IAAI,cAAc;4BAChB,iFAAiF;4BACjF,wBAAwB,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;wBACjF,OAAO,IAAI,aAAa;4BACtB,gEAAgE;4BAChE,wBAAwB,OAAO,qBAAqB,WAAW,mBAAmB;wBACpF,OAAO;4BACL,8DAA8D;4BAC9D,wBAAwB;wBAC1B;wBAEA,2EAA2E;wBAC3E,MAAM,SAAc;4BAClB,WAAW,OAAO;4BAClB,YAAY,SAAS,EAAE;4BACvB,YAAY,OAAO,SAAS,SAAS;4BACrC,OAAO;4BACP,eAAe;wBACjB;wBAEA,sDAAsD;wBACtD,IAAI,0BAA0B,WAAW;4BACvC,OAAO,gBAAgB,GAAG;wBAC5B;wBAEA,OAAO;oBACT;sEACC;uEAAO,CAAC,SAAW,WAAW;yEAAS,EAAE,EAAE,sBAAsB;gBAEtE,wDAAwD;gBACxD,IAAI,iBAAiB,MAAM,KAAK,GAAG;oBACjC,MAAM,IAAI,MAAM;gBAClB;gBAEA,oCAAoC;gBAEpC,8BAA8B;gBAC9B,OAAO,MAAM,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YACtC;;QACA,SAAS;+DAAE;gBACT,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,WAAW,CAAC,IAAI,aAAa;gBAE7B,wEAAwE;gBACxE,OAAO,aAAa,CAAC,IAAI,MAAM;gBAE/B,mEAAmE;gBACnE,aAAa,OAAO,CAAC,kBAAkB,KAAK,GAAG,GAAG,QAAQ;YAC5D;;QACA,OAAO;+DAAE,CAAC;gBACR,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,QAAQ,KAAK,CAAC,qBAAqB;YACrC;;QACA,SAAS;+DAAE;gBACT,gBAAgB;YAClB;;IACF;IAEA,sEAAsE;IAEtE,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,YAAoB;YACzD;+DAAW,CAAC,OAAS,CAAC;wBACpB,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE;oBAChB,CAAC;;YACD;+DAAU,CAAC,OAAS,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,WAAW,EAAE;oBAChB,CAAC;;QACH;sDAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAC3C,IAAI,UAAU;QAEd,kCAAkC;QAClC,iBAAiB,OAAO,CAAC,CAAC;YACxB,IAAI,SAAS,UAAU,EAAE;gBACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;oBACA,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;oBACxD,UAAU;gBACZ;YACF;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QACrB,gBAAgB;QAChB,sBAAsB,MAAM,CAAC;IAC/B;IAEA,iEAAiE;IACjE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,cAAc,IAAI,CAAC,CAAC,IACzB,EAAE,eAAe,EAAE,KAAK,CAAC,SAAW,OAAO,cAAc,KAAK;IAElE;IAEA,iEAAiE;IACjE,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,eAAe,EAAE,KAAK,CAAC,SAAW,OAAO,cAAc,KAAK;IAC9E;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QACJ,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG,EAAE;QAExE,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,IAAI,SAAS,IAAI,EAAE,SAAS,cAAc;oBACxC,qBACE,6LAAC,gIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,UAAU,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAE/C,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;gBAGnC;gBACA,qBACE,6LAAC;oBACC,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,MAAM;oBACN,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,WAAW,IAAI;oBACrC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,sIAAA,CAAA,aAAU;oBACT,OAAO;oBACP,eAAe,CAAC,MAAgB,kBAAkB,SAAS,EAAE,EAAE;oBAC/D,UAAU,SAAS,UAAU;8BAE7B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;gCAAgC,WAAU;;kDACzC,6LAAC,sIAAA,CAAA,iBAAc;wCACb,OAAO,OAAO,KAAK;wCACnB,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;;;;;;kDAE1C,6LAAC,6HAAA,CAAA,QAAK;wCACJ,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;wCAC7C,WAAU;kDAET,OAAO,KAAK;;;;;;oCAEd,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDACV,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;;;;;;;+BAbnB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;;;;;;;;;;;;;;;YAsBzC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC,gIAAA,CAAA,WAAQ;oCACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCACzB,SAAS,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK;oCAC5C,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,MAAM,YAAY,UACd;+CAAI;4CAAe,OAAO,KAAK;yCAAC,GAChC,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM,OAAO,KAAK;wCAC1D,kBAAkB,SAAS,EAAE,EAAE;oCACjC;;;;;;8CAEF,6LAAC,6HAAA,CAAA,QAAK;oCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCAC9B,WAAU;8CAET,OAAO,KAAK;;;;;;;2BAhBP,OAAO,EAAE;;;;;;;;;;YAuB3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAC9D,aAAa,SAAS,WAAW,IAAI;wBACrC,UAAU,SAAS,UAAU;;;;;;;;;;;YAKrC,KAAK;gBACH,qBACE,6LAAC,8IAAA,CAAA,aAAU;oBACT,YAAY,SAAS,EAAE;oBACvB,OAAO;oBACP,UAAU,CAAC,aACT,kBAAkB,SAAS,EAAE,EAAE;oBAEjC,UAAU,SAAS,UAAU;oBAC7B,YAAY,SAAS,KAAK;;;;;;YAIhC;gBACE,OAAO;QACX;IACF;IAEA,gEAAgE;IAChE,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,mBAAmB,SAAS,EAAE;QACjD,MAAM,eAAe,qBAAqB;QAE1C,qBACE,6LAAC;YAEC,WAAW,CAAC,sBAAsB,EAChC,aACI,qFACA,kEACJ;;8BAEF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6HAAA,CAAA,QAAK;oCAAC,WAAU;;wCACd,SAAS,KAAK;wCACd,SAAS,UAAU,kBAClB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;gCAIvC,4BACC,6LAAC;oCAAK,WAAU;;sDACd,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAI1C,8BACC,6LAAC;oCAAK,WAAU;8CAA+I;;;;;;;;;;;;wBAKlK,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAW,CAAC,aAAa,EAC1B,aACI,2CACA,yBACJ;sCACC,SAAS,IAAI;;;;;;wBAGjB,MAAM,CAAC,SAAS,EAAE,CAAC,kBAClB,6LAAC;4BAAE,WAAU;sCACV,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;;;;;;;8BAI1B,6LAAC;oBAAI,WAAU;8BAAQ,oBAAoB;;;;;;;WA3CtC,SAAS,EAAE;;;;;IA8CtB;IAEA,qEAAqE;IACrE,IAAI,eAAe,WAAW,qBAAO,6LAAC,oIAAA,CAAA,UAAO;;;;;IAE7C,oEAAoE;IACpE,IAAI,CAAC,iBAAiB,OAAO;IAE7B,IAAI,WAAW,CAAC,eAAe;QAC7B,qBACE,6LAAC;YAAE,WAAU;sBAAuB;;;;;;IAIxC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;;wBAA2E;wBACxE,aAAa,OAAO,CAAC,KAAK,EAAE,YAAY,IAAI,EAAE,GAAG;;;;;;;8BAElE,6LAAC;oBAAK,UAAU;oBAAc,WAAU;8BACtC,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,CAAC,iBAAiB,cAAc,MAAM,KAAK,kBAC1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;uCAKvC,qFAAqF;4BACrF,iBAAiB,GAAG,CAAC,CAAC;gCACpB,IAAI,KAAK,IAAI,KAAK,SAAS;oCACzB,MAAM,QAAQ,KAAK,IAAI;oCACvB,MAAM,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC;oCAE3C,qBACE,6LAAC,uJAAA,CAAA,UAAmB;wCAElB,OAAO;wCACP,cAAc;wCACd,kBAAkB;wCAClB,iBAAiB;wCACjB,qBAAqB;wCACrB,QAAQ;wCACR,mBAAmB;wCACnB,YAAY;wCACZ,gBAAgB;wCAChB,WAAU;uCAVL,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;gCAa9B,OAAO;oCACL,MAAM,WAAW,KAAK,IAAI;oCAC1B,mDAAmD;oCACnD,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,SAAS,EAAE,GAAG;wCACzD,OAAO;oCACT;oCAEA,uDAAuD;oCACvD,MAAM,iBAAiB,gBAAgB,IAAI,CACzC,CAAC,KAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;oCAGxC,IAAI,gBAAgB;wCAClB,qBACE,6LAAC,0JAAA,CAAA,UAAsB;4CAErB,eAAe;4CACf,qBAAqB;4CACrB,QAAQ;4CACR,WAAU;2CAJL,SAAS,EAAE;;;;;oCAOtB;oCAEA,OAAO,eAAe;gCACxB;4BACF;4BAID,cAAc,MAAM,GAAG,mBACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,UAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;4BAKvC,cAAc,MAAM,KAAK,mBACxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;4BAMxC,iBACC,cAAc,MAAM,GAAG,KACvB,iBAAiB,MAAM,KAAK,mBAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;GA1tBwB;;QACL,4JAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACJ,yLAAA,CAAA,iBAAc;QACb,qIAAA,CAAA,YAAS;QAKsB,oHAAA,CAAA,UAAO;QAgCL,8KAAA,CAAA,WAAQ;QA4ChC,8KAAA,CAAA,WAAQ;QA4GR,iLAAA,CAAA,cAAW;;;KAjMnB", "debugId": null}}]}
"use client";

import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createQuestionGroup, updateQuestionGroup } from "@/lib/api/question-groups";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { Question } from "@/types/formBuilder";
import { ContextType } from "@/types";
import { useTranslations } from "next-intl";
interface QuestionGroupModalProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  contextType: ContextType; // Kept for future use if needed
  contextId: number;
  existingGroup?: {
    id: number;
    title: string;
    order: number;
  };
  questions: Question[];
  questionGroups?: Array<{
    id: number;
    title: string;
  }>;
}

const QuestionGroupModal = ({
  showModal,
  setShowModal,
  contextType,
  contextId,
  existingGroup,
  questions,
  questionGroups = [],
}: QuestionGroupModalProps) => {
  const [title, setTitle] = useState("");
  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);

  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const t = useTranslations();

  // Reset form when modal opens/closes or when editing a different group
  useEffect(() => {
    if (showModal) {
      if (existingGroup) {
        setTitle(existingGroup.title);

        // Fetch questions that belong to this group
        const questionsInGroup = questions.filter(q => q.questionGroupId === existingGroup.id);
        setSelectedQuestionIds(questionsInGroup.map(q => q.id));
      } else {
        setTitle("");
        setSelectedQuestionIds([]);
      }
    }
  }, [showModal, existingGroup, questions]);

  // Make all questions available for selection, regardless of their group status
  // This allows moving questions between groups
  const availableQuestions = questions;

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: createQuestionGroup,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["questionGroups", contextId] });
      queryClient.invalidateQueries({ queryKey: ["questions", contextId] });
      // Also invalidate form builder data for project contexts
      if (contextType === "project") {
        queryClient.invalidateQueries({ queryKey: ["formBuilderData", contextId] });
      }
      dispatch(
        showNotification({
          message: t('questionGroupCreated'),
          type: "success",
        })
      );
      setShowModal(false);
    },
    onError: (error) => {
      console.error("Error creating question group:", error);
      dispatch(
        showNotification({
          message: t('questionGroupCreationFailed'),
          type: "error",
        })
      );
    },
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: updateQuestionGroup,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["questionGroups", contextId] });
      queryClient.invalidateQueries({ queryKey: ["questions", contextId] });
      // Also invalidate form builder data for project contexts
      if (contextType === "project") {
        queryClient.invalidateQueries({ queryKey: ["formBuilderData", contextId] });
      }
      dispatch(
        showNotification({
          message: t('questionGroupUpdated'),
          type: "success",
        })
      );
      setShowModal(false);
    },
    onError: (error) => {
      console.error("Error updating question group:", error);
      dispatch(
        showNotification({
          message: t('questionGroupUpdateFailed'),
          type: "error",
        })
      );
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      dispatch(
        showNotification({
          message: t('groupTitleRequired'),
          type: "error",
        })
      );
      return;
    }

    if (existingGroup) {
     

      updateGroupMutation.mutate({
        id: existingGroup.id,
        title,
        order: existingGroup.order,
        selectedQuestionIds,
      });
    } else {
     

      createGroupMutation.mutate({
        title,
        order: questionGroups.length + 1, // Set order to be after existing groups
        projectId: contextId,
        selectedQuestionIds,
      });
    }
  };

  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4">
        <div className="flex justify-between items-center p-4 ">
          <h2 className="text-xl font-semibold">
            {existingGroup ? t('editQuestionGroup') : t('createQuestionGroup')}
          </h2>
          <button
            onClick={() => setShowModal(false)}
            className="text-gray-500 hover:text-gray-700 cursor-pointer"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="group label-input-group ">
            <label htmlFor="title">
              {t('groupTitle')}
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className=" input-field w-full"
              placeholder={t('enterGroupTitle')}
              required
            />
          </div>



          <div className="mt-8 label-input-group">
            <label>
              {t('selectQuestionsForGroup')}
            </label>
            <div className="border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto">
              {availableQuestions.length > 0 ? (
                availableQuestions.map((question) => {
                  // Find the group this question belongs to (if any)
                  const questionGroup = question.questionGroupId
                    ? questionGroups.find(g => g.id === question.questionGroupId)
                    : null;

                  return (
                    <div key={question.id} className="flex gap-2 items-center mb-3 p-2 border-b border-neutral-300">
                      <input
                        type="checkbox"
                        id={`question-${question.id}`}
                        checked={selectedQuestionIds.includes(question.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedQuestionIds([...selectedQuestionIds, question.id]);
                          } else {
                            setSelectedQuestionIds(selectedQuestionIds.filter(id => id !== question.id));
                          }
                        }}
                        className="mr-2 cursor-pointer w-5 h-5"
                      />
                      <div>
                        <label htmlFor={`question-${question.id}`} className="text-sm">
                          {question.label}
                        </label>

                        {/* Show which group the question belongs to */}
                        {questionGroup && questionGroup.id !== (existingGroup?.id || -1) && (
                          <div className="text-xs text-neutral-700 mt-1">
                            {t('currentlyInGroup')}: <span className="font-medium text-amber-600">{questionGroup.title}</span>
                            <span className="ml-1 text-neutral-700">({t('willBeMovedToThisGroup')})</span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })
              ) : (
                <p className="text-gray-500 text-sm p-2">
                  {t('noAvailableQuestions')}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2 mt-4">
            <button
              type="button"
              onClick={() => setShowModal(false)}
              className="btn-outline"
            >
              {t('cancel')}
            </button>
            <button
              type="submit"
              className="px-4 py-2 btn-primary"
              disabled={createGroupMutation.isPending || updateGroupMutation.isPending}
            >
              {createGroupMutation.isPending || updateGroupMutation.isPending
                ? t('saving')
                : existingGroup
                ? t('updateGroup')
                : t('createGroup')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export { QuestionGroupModal };
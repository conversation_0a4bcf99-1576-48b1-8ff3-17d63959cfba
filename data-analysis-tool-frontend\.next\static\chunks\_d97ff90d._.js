(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/axios.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:4000/api") || "http://localhost:4000/api",
    headers: {
        "Content-Type": "application/json"
    },
    withCredentials: true
});
// Add request interceptor to handle auth token
axiosInstance.interceptors.request.use((config)=>{
    // You can add auth token here if needed
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add response interceptor to handle errors
axiosInstance.interceptors.response.use((response)=>response, (error)=>{
    if (error.code === "ERR_NETWORK") {
        console.error("Network error - Please check if the backend server is running");
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = axiosInstance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/encodeDecode.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "decode": (()=>decode),
    "encode": (()=>encode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hashids$2f$esm$2f$hashids$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/hashids/esm/hashids.js [app-client] (ecmascript)");
;
const salt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.SALT || "rushan-salt";
const hashids = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hashids$2f$esm$2f$hashids$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](salt, 12);
const encode = (id)=>{
    return hashids.encode(id);
};
const decode = (hash)=>{
    const decodedNumberLike = hashids.decode(hash)[0];
    const decoded = typeof decodedNumberLike === "bigint" ? decodedNumberLike < Number.MAX_SAFE_INTEGER ? Number(decodedNumberLike) : null : typeof decodedNumberLike === "number" ? decodedNumberLike : null;
    return decoded;
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/form-builder.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addQuestion": (()=>addQuestion),
    "deleteQuestion": (()=>deleteQuestion),
    "duplicateQuestion": (()=>duplicateQuestion),
    "fetchFormBuilderData": (()=>fetchFormBuilderData),
    "fetchQuestionBlockQuestions": (()=>fetchQuestionBlockQuestions),
    "fetchQuestions": (()=>fetchQuestions),
    "fetchTemplateQuestions": (()=>fetchTemplateQuestions),
    "updateQuestion": (()=>updateQuestion),
    "updateQuestionPositions": (()=>updateQuestionPositions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
const getQuestionsEndPoint = (contextType)=>{
    if (contextType === "project") return "/questions";
    else if (contextType === "template") return "/template-questions";
    else if (contextType === "questionBlock") return "/question-blocks";
    throw new Error("Unsupported context type");
};
const fetchQuestions = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/questions/${projectId}`);
    return data.questions;
};
const fetchTemplateQuestions = async ({ templateId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/template-questions/${templateId}`);
    return data.questions;
};
const addQuestion = async ({ contextType, contextId, dataToSend, position })=>{
    const url = contextType === "questionBlock" ? `${getQuestionsEndPoint(contextType)}` : `${getQuestionsEndPoint(contextType)}/${contextId}`;
    // Validate required fields
    if (!dataToSend.label || !dataToSend.inputType) {
        throw new Error("Label and inputType are required");
    }
    // Check if this input type requires options
    const needsOptions = [
        "selectone",
        "selectmany"
    ].includes(dataToSend.inputType);
    const hasFile = dataToSend.file instanceof File;
    const hasOptions = Array.isArray(dataToSend.questionOptions) && dataToSend.questionOptions.length > 0;
    // Validate options based on input type and upload method
    if (needsOptions && !hasFile && !hasOptions) {
        throw new Error("Options are required for select input types");
    }
    if (hasFile) {
        const formData = new FormData();
        // Add basic question data
        formData.append("label", dataToSend.label);
        // Convert boolean to string in a way backend can parse
        formData.append("isRequired", dataToSend.isRequired ? "true" : "false");
        formData.append("inputType", dataToSend.inputType);
        if (dataToSend.hint) formData.append("hint", dataToSend.hint);
        if (dataToSend.placeholder) formData.append("placeholder", dataToSend.placeholder);
        // Convert number to string
        formData.append("position", String(position || 1));
        // Add file with the correct field name
        formData.append("file", dataToSend.file);
        // Important: Do NOT include questionOptions when uploading a file
        // They will be parsed from the file on the server
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, formData, {
                headers: {
                    "Content-Type": "multipart/form-data"
                }
            });
            return data;
        } catch (error) {
            console.error("Upload error details:", error.response?.data || error.message);
            throw new Error(`Failed to upload question with file: ${error.response?.data?.message || error.message}`);
        }
    } else {
        // Regular JSON request (no file)
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, {
                label: dataToSend.label,
                isRequired: dataToSend.isRequired,
                hint: dataToSend.hint,
                placeholder: dataToSend.placeholder,
                inputType: dataToSend.inputType,
                questionOptions: dataToSend.questionOptions,
                position: position || 1
            });
            return data;
        } catch (error) {
            console.error("API error details:", error.response?.data || error.message);
            throw new Error(`Failed to add question: ${error.response?.data?.message || error.message}`);
        }
    }
};
const deleteQuestion = async ({ contextType, id, projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`);
    return data;
};
const duplicateQuestion = async ({ id, contextType, contextId })=>{
    // For question blocks, we don't need to send the contextId in the body
    // The userId is taken from the authenticated user in the backend
    const requestBody = contextType === "questionBlock" ? {} : contextType === "project" ? {
        projectId: contextId
    } : {
        templateId: contextId
    };
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${getQuestionsEndPoint(contextType)}/duplicate/${id}?projectId=${contextId}`, requestBody);
    return data;
};
const updateQuestion = async ({ id, contextType, dataToSend, contextId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`, dataToSend);
    return data;
};
const fetchQuestionBlockQuestions = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/question-blocks`);
        return response.data.questions || [];
    } catch (error) {
        console.error("Error fetching question block questions:", error);
        throw error;
    }
};
const updateQuestionPositions = async ({ contextType, contextId, questionPositions })=>{
    // Only support position updates for projects currently
    if (contextType !== "project") {
        throw new Error("Question position updates are only supported for projects");
    }
    const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;
    const payload = {
        questionPositions
    };
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(url, payload);
        return data;
    } catch (error) {
        console.error("Update failed - Full error:", error);
        console.error("Update failed - Error details:", {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            config: {
                url: error.config?.url,
                method: error.config?.method,
                data: error.config?.data
            }
        });
        throw error;
    }
};
// Fetch form builder data with ordered structure (groups and questions)
const fetchFormBuilderData = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/projects/getalldata/${projectId}`);
    return data.data;
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/general/Spinner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const Spinner = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"
        }, void 0, false, {
            fileName: "[project]/components/general/Spinner.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/general/Spinner.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
};
_c = Spinner;
const __TURBOPACK__default__export__ = Spinner;
var _c;
__turbopack_context__.k.register(_c, "Spinner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatDate": (()=>formatDate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date, format = "short") {
    if (!date) return "";
    try {
        const dateObj = typeof date === "string" ? new Date(date) : date;
        // Return empty string if invalid date
        if (isNaN(dateObj.getTime())) return "";
        switch(format){
            case "short":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "short",
                    day: "numeric"
                });
            case "long":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit"
                });
            case "full":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                    hour: "2-digit",
                    minute: "2-digit",
                    second: "2-digit"
                });
            default:
                return dateObj.toLocaleDateString();
        }
    } catch (error) {
        console.error("Error formatting date:", error);
        return String(date);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/label.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/label.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Label;
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Label$React.forwardRef");
__turbopack_context__.k.register(_c1, "Label");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500", className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/textarea.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
_c1 = Textarea;
Textarea.displayName = "Textarea";
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textarea$React.forwardRef");
__turbopack_context__.k.register(_c1, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/checkbox.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Checkbox": (()=>Checkbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-checkbox/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as CheckIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
function Checkbox({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "checkbox",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            "data-slot": "checkbox-indicator",
            className: "flex items-center justify-center text-current transition-none",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckIcon$3e$__["CheckIcon"], {
                className: "size-3.5"
            }, void 0, false, {
                fileName: "[project]/components/ui/checkbox.tsx",
                lineNumber: 26,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ui/checkbox.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/checkbox.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_c = Checkbox;
;
var _c;
__turbopack_context__.k.register(_c, "Checkbox");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/radio-group.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroup": (()=>RadioGroup),
    "RadioGroupItem": (()=>RadioGroupItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-radio-group/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-client] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/components/ui/radio-group.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
});
_c1 = RadioGroup;
RadioGroup.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
const RadioGroupItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c2 = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                className: "h-2.5 w-2.5 fill-current text-current"
            }, void 0, false, {
                fileName: "[project]/components/ui/radio-group.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ui/radio-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/radio-group.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
});
_c3 = RadioGroupItem;
RadioGroupItem.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"].displayName;
;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "RadioGroup$React.forwardRef");
__turbopack_context__.k.register(_c1, "RadioGroup");
__turbopack_context__.k.register(_c2, "RadioGroupItem$React.forwardRef");
__turbopack_context__.k.register(_c3, "RadioGroupItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/table.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Table": (()=>Table),
    "TableBody": (()=>TableBody),
    "TableCaption": (()=>TableCaption),
    "TableCell": (()=>TableCell),
    "TableFooter": (()=>TableFooter),
    "TableHead": (()=>TableHead),
    "TableHeader": (()=>TableHeader),
    "TableRow": (()=>TableRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
function Table({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "table-container",
        className: "relative w-full overflow-x-auto",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
            "data-slot": "table",
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("w-full caption-bottom text-sm", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/table.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = Table;
function TableHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
        "data-slot": "table-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("[&_tr]:border-b", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c1 = TableHeader;
function TableBody({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
        "data-slot": "table-body",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("[&_tr:last-child]:border-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_c2 = TableBody;
function TableFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tfoot", {
        "data-slot": "table-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c3 = TableFooter;
function TableRow({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
        "data-slot": "table-row",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_c4 = TableRow;
function TableHead({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
        "data-slot": "table-head",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_c5 = TableHead;
function TableCell({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
        "data-slot": "table-cell",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
_c6 = TableCell;
function TableCaption({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("caption", {
        "data-slot": "table-caption",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground mt-4 text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, this);
}
_c7 = TableCaption;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "Table");
__turbopack_context__.k.register(_c1, "TableHeader");
__turbopack_context__.k.register(_c2, "TableBody");
__turbopack_context__.k.register(_c3, "TableFooter");
__turbopack_context__.k.register(_c4, "TableRow");
__turbopack_context__.k.register(_c5, "TableHead");
__turbopack_context__.k.register(_c6, "TableCell");
__turbopack_context__.k.register(_c7, "TableCaption");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]", "focus-visible:outline-none", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Input;
;
var _c;
__turbopack_context__.k.register(_c, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/table.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createTable": (()=>createTable),
    "deleteTable": (()=>deleteTable),
    "fetchTableStructure": (()=>fetchTableStructure),
    "saveCellValues": (()=>saveCellValues),
    "updateTable": (()=>updateTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
const fetchTableStructure = async (questionId)=>{
    try {
        if (!questionId || isNaN(questionId)) {
            console.error("Invalid questionId:", questionId);
            throw new Error("Invalid question ID provided");
        }
        // First try the table-questions endpoint
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/table-questions/${questionId}`);
            // Check if the response has the expected structure
            if (response.data && response.data.data && response.data.data.question) {
                return response.data.data.question;
            } else if (response.data && response.data.data) {
                return response.data.data;
            } else if (response.data && response.data.success) {
                return response.data;
            }
        } catch (err) {
            console.error("Error from /table-questions/ endpoint:", err);
        // Continue to try the next endpoint
        }
        // If that fails, try the questions endpoint
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/questions/${questionId}`);
            if (response.data && response.data.data) {
                return response.data.data;
            }
        } catch (err) {
            console.error("Error from /questions/ endpoint:", err);
        // Continue to try the next endpoint
        }
        // If that fails, try the tables endpoint as a last resort
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/tables/${questionId}`);
            if (response.data && response.data.data && response.data.data.question) {
                return response.data.data.question;
            }
        } catch (err) {
            console.error("Error from /tables/ endpoint:", err);
        }
        // If all endpoints fail, throw an error
        console.error("All endpoints failed to return valid data");
        throw new Error("Failed to fetch table structure from any endpoint");
    } catch (error) {
        console.error("Error fetching table structure:", error);
        throw error;
    }
};
const saveCellValues = async (questionId, cellValues)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/table-questions/cells`, {
            questionId,
            cellValues
        });
        return data.data;
    } catch (error) {
        console.error("Error saving cell values:", error);
        throw error;
    }
};
const createTable = async (label, projectId, columns, rows)=>{
    try {
        // Validate inputs before sending to API
        if (!label || !label.trim()) {
            throw new Error("Table label is required");
        }
        if (!projectId || isNaN(projectId)) {
            throw new Error("Valid project ID is required");
        }
        if (!columns || !Array.isArray(columns) || columns.length === 0) {
            throw new Error("At least one column is required");
        }
        // Rows are now optional - validate only if provided
        if (rows && !Array.isArray(rows)) {
            throw new Error("Rows must be an array if provided");
        }
        // Ensure all columns have valid names
        const invalidColumns = columns.filter((col)=>!col.columnName || !col.columnName.trim());
        if (invalidColumns.length > 0) {
            throw new Error("All columns must have valid names");
        }
        // Ensure all rows have valid names if rows are provided
        if (rows) {
            const invalidRows = rows.filter((row)=>!row.rowsName || !row.rowsName.trim());
            if (invalidRows.length > 0) {
                throw new Error("All rows must have valid names");
            }
        }
        // The columns are already ordered correctly with parent-child relationships
        // We just need to pass them through to the backend
        // Create a clean version of the columns to send to the backend
        const cleanedColumns = columns.map((col)=>({
                columnName: col.columnName,
                parentColumnId: col.parentColumnId
            }));
        // Log the columns being sent to the backend
        // Log the rearranged columns
        // Use the table-questions endpoint which creates both a question and table structure
        // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/table-questions`, {
            label,
            projectId,
            columns: cleanedColumns,
            rows: rows || []
        });
        if (!data || !data.success) {
            throw new Error(data?.message || "Failed to create table");
        }
        return data.data;
    } catch (error) {
        console.error("Error creating table:", error);
        // Enhance error message with response details if available
        if (error.response) {
            console.error("Response status:", error.response.status);
            console.error("Response data:", error.response.data);
            // If we have a more specific error message from the server, use it
            if (error.response.data && error.response.data.message) {
                error.message = error.response.data.message;
            }
        }
        throw error;
    }
};
const deleteTable = async (tableId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/table-questions/${tableId}`);
        return data;
    } catch (error) {
        console.error("Error deleting table:", error);
        throw error;
    }
};
const updateTable = async (tableId, label, columns, rows)=>{
    try {
        // Validate inputs before sending to API
        if (!label || !label.trim()) {
            throw new Error("Table label is required");
        }
        if (!tableId || isNaN(tableId)) {
            throw new Error("Valid table ID is required");
        }
        if (!columns || !Array.isArray(columns) || columns.length === 0) {
            throw new Error("At least one column is required");
        }
        // Rows are now optional - validate only if provided
        if (rows && !Array.isArray(rows)) {
            throw new Error("Rows must be an array if provided");
        }
        // Ensure all columns have valid names
        const invalidColumns = columns.filter((col)=>!col.columnName || !col.columnName.trim());
        if (invalidColumns.length > 0) {
            throw new Error("All columns must have valid names");
        }
        // Ensure all rows have valid names if rows are provided
        if (rows) {
            const invalidRows = rows.filter((row)=>!row.rowsName || !row.rowsName.trim());
            if (invalidRows.length > 0) {
                throw new Error("All rows must have valid names");
            }
        }
        // Validate parent-child relationships
        // Check for circular references or invalid parent IDs
        const columnIdMap = new Map();
        const columnPositionMap = new Map();
        // Map columns by ID and position
        columns.forEach((col, index)=>{
            if (col.id) {
                columnIdMap.set(col.id, col);
            }
            // Store 1-based position
            columnPositionMap.set(index + 1, col);
        });
        // Check each column with a parent
        for (const col of columns){
            if (col.parentColumnId) {
                // Ensure parentColumnId is a positive number
                if (col.parentColumnId <= 0) {
                    throw new Error(`Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`);
                }
                // Try to find parent by ID first
                let parentCol = columns.find((c)=>c.id === col.parentColumnId);
                // If not found by ID, try to find by position (for new columns)
                if (!parentCol && col.parentColumnId <= columns.length) {
                    parentCol = columnPositionMap.get(col.parentColumnId);
                }
                // If we still can't find the parent, it's an error
                if (!parentCol) {
                    throw new Error(`Parent column with ID/position ${col.parentColumnId} not found in the columns array.`);
                }
                // Check for circular references
                // If this column has a parent, and that parent also has a parent,
                // it would create a 3rd level, which we don't support
                if (parentCol.parentColumnId) {
                    throw new Error("Cannot create more than 2 levels of nested columns (parent → child → grandchild)");
                }
            }
        }
        // The columns are already ordered correctly with parent-child relationships
        // We just need to pass them through to the backend
        // Create a clean version of the columns to send to the backend
        const cleanedColumns = columns.map((col)=>{
            const cleanCol = {
                columnName: col.columnName.trim()
            };
            if (col.id) {
                cleanCol.id = col.id;
            }
            if (col.parentColumnId !== undefined) {
                cleanCol.parentColumnId = col.parentColumnId;
            }
            return cleanCol;
        });
        // Log the columns being sent to the backend
        // Use the table-questions endpoint to update the table
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/table-questions/${tableId}`, {
                label: label.trim(),
                columns: cleanedColumns,
                rows: rows ? rows.map((row)=>({
                        ...row,
                        rowsName: row.rowsName.trim()
                    })) : []
            });
            if (!data || !data.success) {
                throw new Error(data?.message || "Failed to update table");
            }
            return data.data;
        } catch (apiError) {
            console.error("API error updating table:", apiError);
            // Enhance error message with response details if available
            if (apiError.response) {
                console.error("Response status:", apiError.response.status);
                console.error("Response data:", apiError.response.data);
                // If we have a more specific error message from the server, use it
                if (apiError.response.data && apiError.response.data.message) {
                    throw new Error(apiError.response.data.message);
                }
            }
            // If we don't have a specific error message, throw the original error
            throw apiError;
        }
    } catch (error) {
        console.error("Error updating table:", error);
        // Rethrow the error with a clear message
        if (error.message) {
            throw new Error(`Failed to update table: ${error.message}`);
        } else {
            throw new Error("Failed to update table due to an unknown error");
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/form-inputs/TableInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableInput": (()=>TableInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/table.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$table$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/table.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
function TableInput({ questionId, value, onChange, required = false, tableLabel }) {
    _s();
    // All state hooks at the top of the component
    const [columns, setColumns] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [rows, setRows] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [cellValues, setCellValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [tableInfo, setTableInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    // Process columns to create a flat structure with parent-child relationships
    const processColumns = (tableData)=>{
        if (!tableData || !tableData.tableColumns) return [];
        const flattenedColumns = [];
        const parentColumns = tableData.tableColumns.filter((col)=>col.parentColumnId === null || col.parentColumnId === undefined);
        // Process each parent column and its children
        parentColumns.forEach((parentCol)=>{
            // Add the parent column
            flattenedColumns.push(parentCol);
            // Add child columns if they exist
            if (parentCol.childColumns && parentCol.childColumns.length > 0) {
                parentCol.childColumns.forEach((childCol)=>{
                    flattenedColumns.push({
                        id: childCol.id,
                        columnName: childCol.columnName,
                        parentColumnId: childCol.parentColumnId
                    });
                });
            }
        });
        return flattenedColumns;
    };
    // IMPORTANT: All hooks must be called unconditionally and in the same order every render
    // Group columns by parent-child relationships - always called, never conditional
    const groupedColumns = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "TableInput.useMemo[groupedColumns]": ()=>{
            // Default empty values for when columns are not loaded yet
            if (columns.length === 0) {
                return {
                    parentColumns: [],
                    columnMap: new Map(),
                    hasChildColumns: false
                };
            }
            // Get all parent columns (those without a parentColumnId)
            const parentColumns = columns.filter({
                "TableInput.useMemo[groupedColumns].parentColumns": (col)=>col.parentColumnId === undefined || col.parentColumnId === null
            }["TableInput.useMemo[groupedColumns].parentColumns"]);
            // Create a map of parent columns to their child columns
            const columnMap = new Map();
            parentColumns.forEach({
                "TableInput.useMemo[groupedColumns]": (parentCol)=>{
                    // Find all child columns for this parent
                    const childColumns = columns.filter({
                        "TableInput.useMemo[groupedColumns].childColumns": (col)=>col.parentColumnId === parentCol.id
                    }["TableInput.useMemo[groupedColumns].childColumns"]);
                    columnMap.set(parentCol.id, childColumns);
                }
            }["TableInput.useMemo[groupedColumns]"]);
            // Check if any parent has child columns
            const hasChildColumns = parentColumns.some({
                "TableInput.useMemo[groupedColumns].hasChildColumns": (p)=>(columnMap.get(p.id) || []).length > 0
            }["TableInput.useMemo[groupedColumns].hasChildColumns"]);
            return {
                parentColumns,
                columnMap,
                hasChildColumns
            };
        }
    }["TableInput.useMemo[groupedColumns]"], [
        columns
    ]);
    // Fetch table structure (columns and rows) on component mount or when questionId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TableInput.useEffect": ()=>{
            const loadTableStructure = {
                "TableInput.useEffect.loadTableStructure": async ()=>{
                    try {
                        setLoading(true);
                        const tableData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$table$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchTableStructure"])(questionId);
                        if (tableData) {
                            // Check if tableColumns and tableRows exist
                            if (!tableData.tableColumns || !tableData.tableRows) {
                                console.error("Missing tableColumns or tableRows in response:", tableData);
                            }
                            // Process columns to handle parent-child relationships
                            const processedColumns = processColumns(tableData);
                            setColumns(processedColumns);
                            setRows(tableData.tableRows || []);
                            // Store the table label if available
                            if (tableData.label) {
                                setTableInfo({
                                    label: tableData.label
                                });
                            }
                        } else {
                            console.error("No table data returned");
                            setError("Failed to load table structure");
                        }
                    } catch (err) {
                        console.error("Error fetching table structure:", err);
                        setError("Failed to load table structure");
                    } finally{
                        setLoading(false);
                    }
                }
            }["TableInput.useEffect.loadTableStructure"];
            loadTableStructure();
        }
    }["TableInput.useEffect"], [
        questionId
    ]); // Only reload when questionId changes, not when value changes
    // Handle value changes separately without reloading the table structure
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TableInput.useEffect": ()=>{
            // Don't process if we're still loading the table structure
            if (loading) return;
            // Initialize cell values from existing data if available
            const initialCellValues = {};
            // If value is a string, try to parse it as JSON
            let cellData = [];
            if (typeof value === "string") {
                // Only attempt to parse if the string is not empty
                if (value && value.trim() !== "") {
                    try {
                        cellData = JSON.parse(value);
                    } catch (e) {
                        console.error("Error parsing cell data:", e);
                        cellData = [];
                    }
                } else {
                    console.error("Empty string value, using empty array");
                }
            } else if (Array.isArray(value)) {
                cellData = value;
            }
            // Convert cell data to a map for easier access
            cellData.forEach({
                "TableInput.useEffect": (cell)=>{
                    initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;
                }
            }["TableInput.useEffect"]);
            // Check if the value indicates a form reset (empty array, empty string, or undefined)
            const isFormReset = !value || typeof value === "string" && value.trim() === "" || Array.isArray(value) && value.length === 0;
            if (isFormReset) {
                // Clear all cell values when form is reset
                setCellValues({});
            } else if (Object.keys(initialCellValues).length > 0) {
                // Only update cell values if we have new data and we're not in the middle of editing
                setCellValues({
                    "TableInput.useEffect": (prev)=>{
                        // Merge with existing values to avoid losing user input
                        return {
                            ...initialCellValues,
                            ...prev
                        };
                    }
                }["TableInput.useEffect"]);
            }
        }
    }["TableInput.useEffect"], [
        value,
        loading
    ]);
    // Handle cell value change
    const handleCellChange = (columnId, rowId, newValue)=>{
        const cellKey = `${columnId}_${rowId}`;
        // Update the cell values state
        setCellValues((prev)=>({
                ...prev,
                [cellKey]: newValue
            }));
        // Use a setTimeout to ensure we're working with the latest state
        // This prevents the race condition where the state update hasn't completed yet
        setTimeout(()=>{
            // Get the current state of cellValues after the update
            const currentCellValues = {
                ...cellValues,
                [cellKey]: newValue
            };
            // Convert the updated cell values to the format expected by the onChange handler
            const updatedCellValues = [];
            // Convert all cell values to the expected format
            Object.entries(currentCellValues).forEach(([key, value])=>{
                if (value.trim() !== "") {
                    const [colId, rowId] = key.split("_").map(Number);
                    updatedCellValues.push({
                        columnId: colId,
                        rowsId: rowId,
                        value
                    });
                }
            });
            // Call the onChange handler with all cell values
            onChange(updatedCellValues);
        }, 0);
    };
    // Calculate this once, outside of any conditional rendering
    // Only show error when there are no columns - having no rows is valid
    const hasNoColumns = columns.length === 0;
    // Render the hierarchical table
    // Use a single return statement with conditional rendering inside
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "overflow-x-auto",
        children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center",
            children: "Loading table..."
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 250,
            columnNumber: 9
        }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center text-red-500",
            children: error
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 252,
            columnNumber: 9
        }, this) : hasNoColumns ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center text-amber-600",
            children: "This table has no columns defined. Please configure the table question first."
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 254,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
            className: "border-collapse",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                            children: groupedColumns.parentColumns.map((parentCol)=>{
                                const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                // If this parent has children, it spans multiple columns
                                const colSpan = childColumns.length || 1;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                    colSpan: colSpan,
                                    className: "text-center border bg-blue-50 font-medium",
                                    children: parentCol.columnName
                                }, parentCol.id, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 270,
                                    columnNumber: 19
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 262,
                            columnNumber: 13
                        }, this),
                        groupedColumns.hasChildColumns && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                            children: groupedColumns.parentColumns.map((parentCol)=>{
                                const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                // If this parent has no children, render an empty cell to maintain alignment
                                if (childColumns.length === 0) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                        className: "border bg-blue-50/50 text-sm"
                                    }, `empty-${parentCol.id}`, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 291,
                                        columnNumber: 23
                                    }, this);
                                }
                                // Otherwise, render each child column
                                return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                        className: "border bg-blue-50/50 text-sm",
                                        children: childCol.columnName
                                    }, childCol.id, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 302,
                                        columnNumber: 21
                                    }, this));
                            })
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 283,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                    lineNumber: 260,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                    children: rows.length > 0 ? rows.map((row, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                            className: rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50",
                            children: groupedColumns.parentColumns.map((parentCol)=>{
                                const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                // If this parent has no children, render a single cell
                                if (childColumns.length === 0) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                        className: "border p-1",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                            value: cellValues[`${parentCol.id}_${row.id}`] || "",
                                            onChange: (e)=>handleCellChange(parentCol.id, row.id, e.target.value),
                                            className: "w-full",
                                            required: required,
                                            placeholder: "Enter value"
                                        }, void 0, false, {
                                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                                            lineNumber: 333,
                                            columnNumber: 27
                                        }, this)
                                    }, `cell-${parentCol.id}-${row.id}`, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 329,
                                        columnNumber: 25
                                    }, this);
                                }
                                // Otherwise, render cells for each child column
                                return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                        className: "border p-1",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                            value: cellValues[`${childCol.id}_${row.id}`] || "",
                                            onChange: (e)=>handleCellChange(childCol.id, row.id, e.target.value),
                                            className: "w-full",
                                            required: required,
                                            placeholder: "Enter value"
                                        }, void 0, false, {
                                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                                            lineNumber: 358,
                                            columnNumber: 25
                                        }, this)
                                    }, `cell-${childCol.id}-${row.id}`, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 354,
                                        columnNumber: 23
                                    }, this));
                            })
                        }, row.id, false, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 317,
                            columnNumber: 17
                        }, this)) : // When no rows exist, show a single row with input fields under columns
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                        children: groupedColumns.parentColumns.map((parentCol)=>{
                            const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                            // If this parent has no children, render a single cell
                            if (childColumns.length === 0) {
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                    className: "border p-1",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                        value: cellValues[`${parentCol.id}_no_row`] || "",
                                        onChange: (e)=>handleCellChange(parentCol.id, "no_row", e.target.value),
                                        className: "w-full",
                                        required: required,
                                        placeholder: "Enter value"
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 391,
                                        columnNumber: 25
                                    }, this)
                                }, `cell-${parentCol.id}-no-row`, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 387,
                                    columnNumber: 23
                                }, this);
                            }
                            // Otherwise, render cells for each child column
                            return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                    className: "border p-1",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                        value: cellValues[`${childCol.id}_no_row`] || "",
                                        onChange: (e)=>handleCellChange(childCol.id, "no_row", e.target.value),
                                        className: "w-full",
                                        required: required,
                                        placeholder: "Enter value"
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                                        lineNumber: 414,
                                        columnNumber: 23
                                    }, this)
                                }, `cell-${childCol.id}-no-row`, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 410,
                                    columnNumber: 21
                                }, this));
                        })
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 378,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                    lineNumber: 314,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 259,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/form-inputs/TableInput.tsx",
        lineNumber: 248,
        columnNumber: 5
    }, this);
}
_s(TableInput, "yZFgEElWqtMoclZmrIv8P1OAN0w=");
_c = TableInput;
var _c;
__turbopack_context__.k.register(_c, "TableInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/submission.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteFormSubmission": (()=>deleteFormSubmission),
    "deleteMultipleFormSubmissions": (()=>deleteMultipleFormSubmissions),
    "updateAnswer": (()=>updateAnswer),
    "updateMultipleAnswers": (()=>updateMultipleAnswers),
    "updateMultipleAnswersWithEndpoint": (()=>updateMultipleAnswersWithEndpoint)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
// Delete form submission
const deleteFormSubmission = async (submissionId, projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/form-submissions/${submissionId}?projectId=${projectId}`);
        return data;
    } catch (error) {
        console.error("Error deleting form submission:", error);
        throw error;
    }
};
// Delete multiple form submissions
const deleteMultipleFormSubmissions = async (submissionIds, projectId)=>{
    try {
        const deletePromises = submissionIds.map((id)=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/form-submissions/${id}?projectId=${projectId}`));
        const results = await Promise.all(deletePromises);
        return results.map((result)=>result.data);
    } catch (error) {
        console.error("Error deleting multiple form submissions:", error);
        throw error;
    }
};
// Update a single answer
const updateAnswer = async (answerData, projectId)=>{
    try {
        if (!answerData.submissionId || !answerData.questionId) {
            throw new Error("submissionId and questionId are required");
        }
        const formattedData = {
            ...answerData
        };
        if (formattedData.questionOptionId === null) {
            delete formattedData.questionOptionId;
        } else if (Array.isArray(formattedData.questionOptionId)) {
            formattedData.questionOptionId = formattedData.questionOptionId.filter((id)=>id != null);
            if (formattedData.questionOptionId.length === 0) {
                delete formattedData.questionOptionId;
            }
        }
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/${answerData.questionId}?projectId=${projectId}`, formattedData);
        return data;
    } catch (error) {
        console.error("Error updating answer:", error);
        throw error;
    }
};
// Update multiple answers for a single question (for select_many type)
const updateMultipleAnswers = async (answerData)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/${answerData.questionId}`, {
            ...answerData,
            answerType: "selectmany"
        });
        return data;
    } catch (error) {
        console.error("Error updating multiple answers:", error);
        throw error;
    }
};
// Update multiple answers using the /answers/multiple endpoint
const updateMultipleAnswersWithEndpoint = async (answers, projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/multiple?projectId=${projectId}`, answers);
        return data;
    } catch (error) {
        console.error("Error updating multiple answers with endpoint:", error);
        throw error;
    }
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/question-groups.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createQuestionGroup": (()=>createQuestionGroup),
    "deleteQuestionAndGroup": (()=>deleteQuestionAndGroup),
    "deleteQuestionGroup": (()=>deleteQuestionGroup),
    "fetchQuestionGroups": (()=>fetchQuestionGroups),
    "moveGroupInsideGroup": (()=>moveGroupInsideGroup),
    "moveQuestionBetweenGroups": (()=>moveQuestionBetweenGroups),
    "removeGroupFromParent": (()=>removeGroupFromParent),
    "removeQuestionFromGroup": (()=>removeQuestionFromGroup),
    "updateGroupPositions": (()=>updateGroupPositions),
    "updateQuestionGroup": (()=>updateQuestionGroup)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
const fetchQuestionGroups = async ({ projectId })=>{
    try {
        // Use the project endpoint to fetch the project with its question groups
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/projects/form/${projectId}`);
        // Extract question groups from the project data
        const questionGroups = data.data?.project?.questionGroup || [];
        return questionGroups;
    } catch (error) {
        console.error("Error fetching question groups from project endpoint:", error);
        // Fallback to direct question groups endpoint
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/question-groups`, {
                projectId
            });
            return data.data?.projectGroup || [];
        } catch (fallbackError) {
            console.error("Error in fallback fetch:", fallbackError);
            // Last resort: create a dummy group for debugging
            if ("TURBOPACK compile-time truthy", 1) {
                return [];
            }
            "TURBOPACK unreachable";
        }
    }
};
const createQuestionGroup = async ({ title, order, projectId, selectedQuestionIds, parentGroupId })=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/question-groups`, {
            title,
            order,
            projectId,
            selectedQuestionIds: selectedQuestionIds || [],
            parentGroupId
        });
        return data;
    } catch (error) {
        console.error("Error creating question group:", error);
        throw error;
    }
};
const updateQuestionGroup = async ({ id, title, order, selectedQuestionIds })=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups`, {
            id,
            title,
            order,
            selectedQuestionIds
        });
        return data;
    } catch (error) {
        console.error("Error updating question group:", error);
        throw error;
    }
};
const deleteQuestionGroup = async ({ id })=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/question-groups/${id}`);
        return data;
    } catch (error) {
        console.error("Error deleting question group:", error);
        throw error;
    }
};
const deleteQuestionAndGroup = async ({ id })=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/question-groups/group/question/${id}`);
        return data;
    } catch (error) {
        console.error("Error deleting question group and questions:", error);
        throw error;
    }
};
const removeQuestionFromGroup = async ({ groupId, questionId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/question/remove`, {
        groupId,
        questionId
    });
    return data;
};
const moveQuestionBetweenGroups = async ({ groupId, newGroupId, questionId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/question/move`, {
        groupId,
        newGroupId,
        questionId
    });
    return data;
};
const moveGroupInsideGroup = async ({ childGroupId, parentGroupId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/group/add`, {
        childGroupId,
        ParentGroupId: parentGroupId
    });
    return data;
};
const removeGroupFromParent = async ({ groupId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/group/remove`, {
        groupId
    });
    return data;
};
const updateGroupPositions = async ({ projectId, groupPositions })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/positions`, {
        projectId,
        groupPositions
    });
    return data;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/projects.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addProjectUser": (()=>addProjectUser),
    "archiveMultipleProjects": (()=>archiveMultipleProjects),
    "archiveProject": (()=>archiveProject),
    "checkUserExists": (()=>checkUserExists),
    "createAnswerSubmission": (()=>createAnswerSubmission),
    "createProjectFromTemplate": (()=>createProjectFromTemplate),
    "deleteMultipleProjects": (()=>deleteMultipleProjects),
    "deleteProject": (()=>deleteProject),
    "deployProject": (()=>deployProject),
    "fetchProjectById": (()=>fetchProjectById),
    "fetchProjectUsers": (()=>fetchProjectUsers),
    "fetchProjects": (()=>fetchProjects),
    "updateAnswerSubmission": (()=>updateAnswerSubmission)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
;
const fetchProjectById = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/projects/${projectId}`);
    return data.project;
};
const createProjectFromTemplate = async (dataToSend)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/projects/from-template`, dataToSend);
    return data;
};
//Fetch all projects for the current user
const fetchProjects = async ()=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/projects`);
        return data.projects;
    } catch (error) {
        console.error("Error fetching projects:", error);
        throw error;
    }
};
// Delete project
const deleteProject = async (projectId)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/projects/delete/${projectId}`);
    return data;
};
// Delete multiple projects
const deleteMultipleProjects = async (projectIds)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/projects/delete-multiple`, {
            data: {
                projectIds
            }
        });
        return data;
    } catch (error) {
        console.error("Error deleting multiple projects:", error);
        throw error;
    }
};
//Archive project
const archiveProject = async (projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/projects/change-status/${projectId}`, {
            status: "archived"
        });
        return data;
    } catch (error) {
        console.error("Error archiving project:", error);
        throw error;
    }
};
//Deploy project
const deployProject = async (projectId, isUnarchive = false)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/projects/change-status/${projectId}`, {
            status: "deployed"
        });
        return data;
    } catch (error) {
        console.error("Error deploying project:", error);
        throw error;
    }
};
// Archive multiple projects
const archiveMultipleProjects = async (projectIds)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/projects/update-many-status`, {
            projectIds,
            status: "archived"
        });
        return data;
    } catch (error) {
        console.error("Error archiving multiple projects:", error);
        throw error;
    }
};
// Check if user exists by email
const checkUserExists = async (email)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/users/check-email`, {
            email
        });
        return data;
    } catch (error) {
        // Format error message consistently
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to check user";
        throw new Error(errorMessage);
    }
};
// Add user to project by email
const addProjectUser = async ({ projectId, email, permissions })=>{
    try {
        // First check if the user exists
        const userData = await checkUserExists(email);
        if (!userData || !userData.success) {
            throw new Error(userData?.message || "User not found");
        }
        // Now use the user ID to add them to the project
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/project-users`, {
            userId: userData.user.id,
            projectId,
            permission: permissions
        });
        return data;
    } catch (error) {
        console.error("Error adding user to project:", error);
        // Format error message as a string
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to add user";
        throw new Error(errorMessage);
    }
};
// Fetch all users for a specific project
const fetchProjectUsers = async (projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/project-users/${projectId}`);
        return data.data.AllUser;
    } catch (error) {
        console.error("Error fetching project users:", error);
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to fetch project users";
        throw new Error(errorMessage);
    }
};
// Create answer submission
const createAnswerSubmission = async (answers)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/answers/multiple`, answers);
        return data;
    } catch (error) {
        console.error("Error creating answer submission:", error);
        throw error;
    }
};
// Update answer submission
const updateAnswerSubmission = async (projectId, submissionId, answers)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/form-submissions/${projectId}/${submissionId}`, {
            answers
        });
        return data;
    } catch (error) {
        console.error("Error updating answer submission:", error);
        throw error;
    }
};
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/conditionalQuestions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanupHiddenAnswers": (()=>cleanupHiddenAnswers),
    "cleanupHiddenAnswersWithPersistence": (()=>cleanupHiddenAnswersWithPersistence),
    "getAllNextQuestionIds": (()=>getAllNextQuestionIds),
    "getDependentQuestions": (()=>getDependentQuestions),
    "getNestedQuestions": (()=>getNestedQuestions),
    "getNextQuestionId": (()=>getNextQuestionId),
    "getParentQuestion": (()=>getParentQuestion),
    "getVisibleQuestions": (()=>getVisibleQuestions),
    "isFollowUpQuestion": (()=>isFollowUpQuestion),
    "isQuestionVisible": (()=>isQuestionVisible),
    "restoreOriginalAnswers": (()=>restoreOriginalAnswers),
    "validateVisibleQuestions": (()=>validateVisibleQuestions)
});
const getNextQuestionId = (question, selectedValue)=>{
    if (!question.questionOptions || question.questionOptions.length === 0) {
        return null;
    }
    // For selectone, selectedValue is a string
    if (question.inputType === "selectone" && typeof selectedValue === "string") {
        const selectedOption = question.questionOptions.find((option)=>option.label === selectedValue);
        return selectedOption?.nextQuestionId || null;
    }
    // For selectmany, selectedValue is an array - return the first next question found
    if (question.inputType === "selectmany" && Array.isArray(selectedValue)) {
        for (const value of selectedValue){
            const selectedOption = question.questionOptions.find((option)=>option.label === value);
            if (selectedOption?.nextQuestionId) {
                return selectedOption.nextQuestionId;
            }
        }
    }
    return null;
};
const getAllNextQuestionIds = (question)=>{
    if (!question.questionOptions || question.questionOptions.length === 0) {
        return [];
    }
    return question.questionOptions.map((option)=>option.nextQuestionId).filter((id)=>id !== null && id !== undefined);
};
const getVisibleQuestions = (allQuestions, answers)=>{
    const visibleQuestionIds = new Set();
    const conditionalQuestionIds = new Set();
    // First, collect all questions that are conditional (have a parent question)
    allQuestions.forEach((question)=>{
        const nextQuestionIds = getAllNextQuestionIds(question);
        nextQuestionIds.forEach((id)=>conditionalQuestionIds.add(id));
    });
    // Start with all non-conditional questions (questions that are not triggered by other questions)
    allQuestions.forEach((question)=>{
        if (!conditionalQuestionIds.has(question.id)) {
            visibleQuestionIds.add(question.id);
        }
    });
    // Process answers to determine which conditional questions should be visible
    Object.entries(answers).forEach(([questionIdStr, answer])=>{
        const questionId = parseInt(questionIdStr);
        const question = allQuestions.find((q)=>q.id === questionId);
        if (question && answer) {
            const nextQuestionId = getNextQuestionId(question, answer);
            if (nextQuestionId) {
                visibleQuestionIds.add(nextQuestionId);
            }
        }
    });
    // Return questions in their original order, filtered by visibility
    return allQuestions.filter((question)=>visibleQuestionIds.has(question.id));
};
const isQuestionVisible = (question, allQuestions, answers)=>{
    const visibleQuestions = getVisibleQuestions(allQuestions, answers);
    return visibleQuestions.some((q)=>q.id === question.id);
};
const getDependentQuestions = (parentQuestionId, allQuestions)=>{
    const parentQuestion = allQuestions.find((q)=>q.id === parentQuestionId);
    if (!parentQuestion) return [];
    const nextQuestionIds = getAllNextQuestionIds(parentQuestion);
    return allQuestions.filter((q)=>nextQuestionIds.includes(q.id));
};
const isFollowUpQuestion = (questionId, allQuestions)=>{
    return allQuestions.some((question)=>{
        const nextQuestionIds = getAllNextQuestionIds(question);
        return nextQuestionIds.includes(questionId);
    });
};
const getParentQuestion = (questionId, allQuestions)=>{
    return allQuestions.find((question)=>{
        const nextQuestionIds = getAllNextQuestionIds(question);
        return nextQuestionIds.includes(questionId);
    }) || null;
};
const getNestedQuestions = (allQuestions, answers)=>{
    const visibleQuestions = getVisibleQuestions(allQuestions, answers);
    const visibleQuestionIds = new Set(visibleQuestions.map((q)=>q.id));
    // Get all parent questions (questions that are not follow-ups themselves)
    const parentQuestions = allQuestions.filter((question)=>!isFollowUpQuestion(question.id, allQuestions));
    // Sort parent questions by position to maintain order
    const sortedParentQuestions = parentQuestions.sort((a, b)=>a.position - b.position);
    return sortedParentQuestions.map((parentQuestion)=>{
        const followUpQuestions = getDependentQuestions(parentQuestion.id, allQuestions);
        const sortedFollowUps = followUpQuestions.sort((a, b)=>a.position - b.position);
        return {
            question: parentQuestion,
            isVisible: visibleQuestionIds.has(parentQuestion.id),
            isFollowUp: false,
            followUps: sortedFollowUps.map((followUp)=>({
                    question: followUp,
                    isVisible: visibleQuestionIds.has(followUp.id)
                }))
        };
    }).filter((group)=>group.isVisible || group.followUps.some((f)=>f.isVisible));
};
const cleanupHiddenAnswers = (answers, visibleQuestions)=>{
    const visibleQuestionIds = new Set(visibleQuestions.map((q)=>q.id));
    const cleanedAnswers = {};
    Object.entries(answers).forEach(([questionId, answer])=>{
        if (visibleQuestionIds.has(parseInt(questionId))) {
            cleanedAnswers[questionId] = answer;
        }
    });
    return cleanedAnswers;
};
const cleanupHiddenAnswersWithPersistence = (currentAnswers, visibleQuestions, originalAnswers)=>{
    const visibleQuestionIds = new Set(visibleQuestions.map((q)=>q.id));
    const cleanedAnswers = {};
    Object.entries(currentAnswers).forEach(([questionId, answer])=>{
        if (visibleQuestionIds.has(parseInt(questionId))) {
            cleanedAnswers[questionId] = answer;
        }
    });
    return cleanedAnswers;
};
const restoreOriginalAnswers = (currentAnswers, visibleQuestions, originalAnswers)=>{
    const restoredAnswers = {
        ...currentAnswers
    };
    const visibleQuestionIds = new Set(visibleQuestions.map((q)=>q.id));
    // For each visible question, if it doesn't have a current answer but has an original answer, restore it
    visibleQuestionIds.forEach((questionId)=>{
        const questionIdStr = questionId.toString();
        const hasCurrentAnswer = currentAnswers[questionIdStr] !== undefined && currentAnswers[questionIdStr] !== "" && !(Array.isArray(currentAnswers[questionIdStr]) && currentAnswers[questionIdStr].length === 0);
        const hasOriginalAnswer = originalAnswers[questionIdStr] !== undefined && originalAnswers[questionIdStr] !== "" && !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0);
        // If the question is visible but has no current answer, restore the original answer
        if (!hasCurrentAnswer && hasOriginalAnswer) {
            restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];
        }
    });
    return restoredAnswers;
};
const validateVisibleQuestions = (visibleQuestions, answers)=>{
    const errors = {};
    visibleQuestions.forEach((question)=>{
        if (question.isRequired) {
            const value = answers[question.id];
            if (typeof value === "string" && !value.trim() || Array.isArray(value) && value.length === 0 || value === undefined || value === null) {
                errors[question.id] = `${question.label} is required`;
            }
        }
    });
    return errors;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/form-inputs/NestedQuestionRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/label.tsx [app-client] (ecmascript)");
"use client";
;
;
const NestedQuestionRenderer = ({ questionGroup, renderQuestionInput, errors, className = "" })=>{
    const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;
    // Don't render anything if neither parent nor any follow-ups are visible
    if (!isParentVisible && !followUps.some((f)=>f.isVisible)) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className}`,
        children: [
            isParentVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                className: "text-base font-medium",
                                children: [
                                    parentQuestion.label,
                                    parentQuestion.isRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-red-500 ml-1",
                                        children: "*"
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 44,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 41,
                                columnNumber: 13
                            }, this),
                            parentQuestion.hint && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-muted-foreground mt-1",
                                children: parentQuestion.hint
                            }, void 0, false, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 48,
                                columnNumber: 15
                            }, this),
                            errors[parentQuestion.id] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm text-red-500 mt-1",
                                children: errors[parentQuestion.id]
                            }, void 0, false, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 53,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                        lineNumber: 40,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2",
                        children: renderQuestionInput(parentQuestion)
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this),
                    followUps.some((f)=>f.isVisible) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4",
                        children: followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible })=>isFollowUpVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                className: "text-sm font-medium text-primary-900 dark:text-primary-100",
                                                children: [
                                                    followUpQuestion.label,
                                                    followUpQuestion.isRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-red-500 ml-1",
                                                        children: "*"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                                        lineNumber: 73,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                                lineNumber: 70,
                                                columnNumber: 23
                                            }, this),
                                            followUpQuestion.hint && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-primary-700 dark:text-primary-300 mt-1",
                                                children: followUpQuestion.hint
                                            }, void 0, false, {
                                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                                lineNumber: 77,
                                                columnNumber: 25
                                            }, this),
                                            errors[followUpQuestion.id] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-red-500 mt-1",
                                                children: errors[followUpQuestion.id]
                                            }, void 0, false, {
                                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                                lineNumber: 82,
                                                columnNumber: 25
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 69,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2",
                                        children: renderQuestionInput(followUpQuestion)
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 87,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, followUpQuestion.id, true, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 65,
                                columnNumber: 19
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                        lineNumber: 62,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                lineNumber: 39,
                columnNumber: 9
            }, this),
            !isParentVisible && followUps.some((f)=>f.isVisible) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible })=>isFollowUpVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                        className: "text-base font-medium",
                                        children: [
                                            followUpQuestion.label,
                                            followUpQuestion.isRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-red-500 ml-1",
                                                children: "*"
                                            }, void 0, false, {
                                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                                lineNumber: 109,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 106,
                                        columnNumber: 19
                                    }, this),
                                    followUpQuestion.hint && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-muted-foreground mt-1",
                                        children: followUpQuestion.hint
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 113,
                                        columnNumber: 21
                                    }, this),
                                    errors[followUpQuestion.id] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-red-500 mt-1",
                                        children: errors[followUpQuestion.id]
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                        lineNumber: 118,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 105,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-2",
                                children: renderQuestionInput(followUpQuestion)
                            }, void 0, false, {
                                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                                lineNumber: 123,
                                columnNumber: 17
                            }, this)
                        ]
                    }, followUpQuestion.id, true, {
                        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                        lineNumber: 101,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
                lineNumber: 98,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/form-inputs/NestedQuestionRenderer.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_c = NestedQuestionRenderer;
const __TURBOPACK__default__export__ = NestedQuestionRenderer;
var _c;
__turbopack_context__.k.register(_c, "NestedQuestionRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/form-inputs/NestedGroupRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-client] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedQuestionRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-inputs/NestedQuestionRenderer.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const NestedGroupRenderer = ({ group, nestingLevel = 0, visibleQuestions, nestedQuestions, renderQuestionInput, errors, onToggleExpansion, isExpanded: controlledExpanded, expandedGroups, className = "" })=>{
    _s();
    const [internalExpanded, setInternalExpanded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Use controlled expansion if provided, otherwise use internal state
    const isExpanded = controlledExpanded !== undefined ? controlledExpanded : internalExpanded;
    const handleToggleExpansion = ()=>{
        if (onToggleExpansion) {
            onToggleExpansion(group.id);
        } else {
            setInternalExpanded(!internalExpanded);
        }
    };
    // Get questions for this group
    const groupQuestions = group.question || [];
    const visibleGroupQuestions = groupQuestions.filter((q)=>visibleQuestions.some((vq)=>vq.id === q.id));
    // Get subgroups for this group
    const subGroups = group.subGroups || [];
    const visibleSubGroups = subGroups.filter((subGroup)=>{
        const subGroupQuestions = subGroup.question || [];
        return subGroupQuestions.some((q)=>visibleQuestions.some((vq)=>vq.id === q.id));
    });
    // Don't render if no visible questions in this group or its subgroups
    if (visibleGroupQuestions.length === 0 && visibleSubGroups.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${nestingLevel > 0 ? `ml-8 border-l-4 border-l-primary-300` : ''} ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",
                onClick: handleToggleExpansion,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2",
                    children: [
                        isExpanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                            className: "h-5 w-5 text-neutral-700 dark:text-neutral-300"
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 87,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                            className: "h-5 w-5 text-neutral-700 dark:text-neutral-300"
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 89,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100",
                            children: group.title
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 91,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm text-neutral-700 dark:text-neutral-400",
                            children: [
                                "(",
                                visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg)=>acc + (sg.question?.length || 0), 0),
                                " visible question",
                                visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg)=>acc + (sg.question?.length || 0), 0) !== 1 ? "s" : "",
                                ")"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                    lineNumber: 85,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                lineNumber: 81,
                columnNumber: 7
            }, this),
            isExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 space-y-4",
                children: [
                    visibleSubGroups.sort((a, b)=>a.order - b.order).map((subGroup)=>{
                        // Get expansion state for this subgroup from parent's expandedGroups
                        const subGroupExpanded = expandedGroups ? expandedGroups[subGroup.id] : undefined;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NestedGroupRenderer, {
                            group: subGroup,
                            nestingLevel: nestingLevel + 1,
                            visibleQuestions: visibleQuestions,
                            nestedQuestions: nestedQuestions,
                            renderQuestionInput: renderQuestionInput,
                            errors: errors,
                            onToggleExpansion: onToggleExpansion,
                            isExpanded: subGroupExpanded,
                            expandedGroups: expandedGroups,
                            className: className
                        }, subGroup.id, false, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 112,
                            columnNumber: 17
                        }, this);
                    }),
                    nestedQuestions.filter((nq)=>groupQuestions.some((gq)=>gq.id === nq.question.id)).map((questionGroup)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedQuestionRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            questionGroup: questionGroup,
                            renderQuestionInput: renderQuestionInput,
                            errors: errors,
                            className: ""
                        }, questionGroup.question.id, false, {
                            fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                            lineNumber: 134,
                            columnNumber: 15
                        }, this))
                ]
            }, void 0, true, {
                fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/form-inputs/NestedGroupRenderer.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
};
_s(NestedGroupRenderer, "3DfA9plvNdY87thXEWdTxUzgy4E=");
_c = NestedGroupRenderer;
const __TURBOPACK__default__export__ = NestedGroupRenderer;
var _c;
__turbopack_context__.k.register(_c, "NestedGroupRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils/nestedGroups.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildNestedGroups": (()=>buildNestedGroups),
    "createUnifiedFormItems": (()=>createUnifiedFormItems),
    "getAllGroupIds": (()=>getAllGroupIds),
    "getUngroupedQuestions": (()=>getUngroupedQuestions),
    "initializeGroupExpansionState": (()=>initializeGroupExpansionState)
});
const buildNestedGroups = (groups, questions)=>{
    const groupMap = new Map();
    // Create a map of all groups with their subGroups and questions initialized
    groups.forEach((group)=>{
        // Get questions for this group
        const groupQuestions = questions.filter((q)=>q.questionGroupId === group.id).sort((a, b)=>a.position - b.position);
        groupMap.set(group.id, {
            ...group,
            subGroups: [],
            question: groupQuestions
        });
    });
    // Build the nested structure
    const topLevelGroups = [];
    groups.forEach((group)=>{
        const groupWithSubGroups = groupMap.get(group.id);
        if (group.parentGroupId) {
            // This is a child group, add it to its parent's subGroups
            const parentGroup = groupMap.get(group.parentGroupId);
            if (parentGroup) {
                parentGroup.subGroups = parentGroup.subGroups || [];
                parentGroup.subGroups.push(groupWithSubGroups);
            }
        } else {
            // This is a top-level group
            topLevelGroups.push(groupWithSubGroups);
        }
    });
    return topLevelGroups;
};
const createUnifiedFormItems = (nestedGroups, ungroupedQuestions)=>{
    const items = [];
    // Add question groups
    nestedGroups.forEach((group)=>{
        // For groups, find the minimum position of questions in the group (including subgroups)
        const getAllGroupQuestions = (g)=>{
            const directQuestions = g.question || [];
            const subGroupQuestions = (g.subGroups || []).flatMap(getAllGroupQuestions);
            return [
                ...directQuestions,
                ...subGroupQuestions
            ];
        };
        const allGroupQuestions = getAllGroupQuestions(group);
        const minQuestionPosition = allGroupQuestions.length > 0 ? Math.min(...allGroupQuestions.map((q)=>q.position)) : group.order;
        items.push({
            type: 'group',
            data: group,
            order: minQuestionPosition,
            originalPosition: minQuestionPosition
        });
    });
    // Add ungrouped questions
    ungroupedQuestions.forEach((question)=>{
        items.push({
            type: 'question',
            data: question,
            order: question.position,
            originalPosition: question.position
        });
    });
    // Sort by order/position with secondary sort for consistency
    return items.sort((a, b)=>{
        if (a.order === b.order) {
            return (a.originalPosition || a.order) - (b.originalPosition || b.order);
        }
        return a.order - b.order;
    });
};
const getUngroupedQuestions = (questions)=>{
    return questions.filter((q)=>q.questionGroupId === null || q.questionGroupId === undefined);
};
const getAllGroupIds = (groups)=>{
    const ids = [];
    groups.forEach((group)=>{
        ids.push(group.id);
        if (group.subGroups && group.subGroups.length > 0) {
            ids.push(...getAllGroupIds(group.subGroups));
        }
    });
    return ids;
};
const initializeGroupExpansionState = (nestedGroups, defaultExpanded = true)=>{
    const initialExpandedState = {};
    const allGroupIds = getAllGroupIds(nestedGroups);
    allGroupIds.forEach((groupId)=>{
        initialExpandedState[groupId] = defaultExpanded;
    });
    return initialExpandedState;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/form-preview/editForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EditForm": (()=>EditForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/checkbox.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/radio-group.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/redux/slices/notificationSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$TableInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-inputs/TableInput.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/submission.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$question$2d$groups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/question-groups.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/projects.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$conditionalQuestions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/conditionalQuestions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedQuestionRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-inputs/NestedQuestionRenderer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedGroupRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-inputs/NestedGroupRenderer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$nestedGroups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/nestedGroups.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function EditForm({ questions, submission, projectId, submissionId, onClose, onSave }) {
    _s();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const [answers, setAnswers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [originalAnswers, setOriginalAnswers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [visibleQuestions, setVisibleQuestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [nestedQuestions, setNestedQuestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [expandedGroups, setExpandedGroups] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    // Track which fields are being actively edited by the user
    const [userEditedFields, setUserEditedFields] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    // Use refs to track previous state without causing re-renders
    const previousVisibleQuestionIdsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Set());
    const isInitializedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    // Track the last answers hash to prevent infinite loops
    const lastAnswersHashRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])("");
    // Fetch question groups for the project
    const { data: questionGroups = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "questionGroups",
            projectId
        ],
        queryFn: {
            "EditForm.useQuery": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$question$2d$groups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchQuestionGroups"])({
                    projectId
                })
        }["EditForm.useQuery"],
        enabled: !!projectId
    });
    // Fetch project data to get the project name
    const { data: projectData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "project",
            projectId
        ],
        queryFn: {
            "EditForm.useQuery": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchProjectById"])({
                    projectId
                })
        }["EditForm.useQuery"],
        enabled: !!projectId
    });
    // Initialize answers from submission
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditForm.useEffect": ()=>{
            const initialAnswers = {};
            questions.forEach({
                "EditForm.useEffect": (question)=>{
                    if (question.inputType === "selectmany") {
                        const selectManyAnswers = submission.answers.filter({
                            "EditForm.useEffect.selectManyAnswers": (a)=>a.question.id === question.id
                        }["EditForm.useEffect.selectManyAnswers"]);
                        initialAnswers[question.id] = selectManyAnswers.map({
                            "EditForm.useEffect": (answer)=>answer.value
                        }["EditForm.useEffect"]).filter({
                            "EditForm.useEffect": (value)=>value != null && String(value).trim() !== ""
                        }["EditForm.useEffect"]);
                    } else {
                        const answer = submission.answers.find({
                            "EditForm.useEffect.answer": (a)=>a.question.id === question.id
                        }["EditForm.useEffect.answer"]);
                        initialAnswers[question.id] = answer?.value ?? "";
                    }
                }
            }["EditForm.useEffect"]);
            setAnswers(initialAnswers);
            // Store a deep copy of the original answers for persistence
            setOriginalAnswers(JSON.parse(JSON.stringify(initialAnswers)));
        }
    }["EditForm.useEffect"], [
        questions,
        submission
    ]);
    // Handle visibility and conditional logic
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditForm.useEffect": ()=>{
            if (!questions || Object.keys(originalAnswers).length === 0) {
                return;
            }
            // Create a hash of current answers to detect if this effect was triggered by our own setAnswers call
            const currentAnswersHash = JSON.stringify(answers);
            if (currentAnswersHash === lastAnswersHashRef.current) {
                return; // Skip if this is the same answers we just set
            }
            const newVisibleQuestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$conditionalQuestions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVisibleQuestions"])(questions, answers);
            const newVisibleQuestionIds = new Set(newVisibleQuestions.map({
                "EditForm.useEffect": (q)=>q.id
            }["EditForm.useEffect"]));
            // Always update visible questions and nested structure
            setVisibleQuestions(newVisibleQuestions);
            setNestedQuestions((0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$conditionalQuestions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNestedQuestions"])(questions, answers));
            // Initialize on first run
            if (!isInitializedRef.current) {
                previousVisibleQuestionIdsRef.current = newVisibleQuestionIds;
                isInitializedRef.current = true;
                lastAnswersHashRef.current = currentAnswersHash;
                return;
            }
            // Check if visible questions actually changed
            const visibleQuestionsChanged = newVisibleQuestionIds.size !== previousVisibleQuestionIdsRef.current.size || [
                ...newVisibleQuestionIds
            ].some({
                "EditForm.useEffect": (id)=>!previousVisibleQuestionIdsRef.current.has(id)
            }["EditForm.useEffect"]);
            if (!visibleQuestionsChanged) {
                lastAnswersHashRef.current = currentAnswersHash;
                return; // No visibility changes, no need to process further
            }
            // Detect newly visible questions (conditional questions that just became visible)
            const newlyVisibleQuestionIds = new Set([
                ...newVisibleQuestionIds
            ].filter({
                "EditForm.useEffect": (id)=>!previousVisibleQuestionIdsRef.current.has(id)
            }["EditForm.useEffect"]));
            // Only restore answers for newly visible conditional questions that haven't been edited by the user
            let shouldUpdateAnswers = false;
            const restoredAnswers = {
                ...answers
            };
            if (newlyVisibleQuestionIds.size > 0) {
                newlyVisibleQuestionIds.forEach({
                    "EditForm.useEffect": (questionId)=>{
                        const questionIdStr = questionId.toString();
                        // Only restore if:
                        // 1. The field hasn't been actively edited by the user
                        // 2. The field currently has no value
                        // 3. There's an original value to restore
                        if (!userEditedFields.has(questionId) && (answers[questionIdStr] === undefined || answers[questionIdStr] === "" || Array.isArray(answers[questionIdStr]) && answers[questionIdStr].length === 0) && originalAnswers[questionIdStr] !== undefined && originalAnswers[questionIdStr] !== "" && !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0)) {
                            restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];
                            shouldUpdateAnswers = true;
                        }
                    }
                }["EditForm.useEffect"]);
            }
            // Clean up answers for questions that are no longer visible
            const cleanedAnswers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$conditionalQuestions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanupHiddenAnswers"])(restoredAnswers, newVisibleQuestions);
            // Check if cleanup made changes
            const cleanupMadeChanges = Object.keys(cleanedAnswers).length !== Object.keys(restoredAnswers).length;
            // Update previous visible questions for next comparison
            previousVisibleQuestionIdsRef.current = newVisibleQuestionIds;
            // Only update answers if there are actual changes
            if (shouldUpdateAnswers || cleanupMadeChanges) {
                const newAnswersHash = JSON.stringify(cleanedAnswers);
                lastAnswersHashRef.current = newAnswersHash;
                setAnswers(cleanedAnswers);
            } else {
                lastAnswersHashRef.current = currentAnswersHash;
            }
        }
    }["EditForm.useEffect"], [
        questions,
        answers,
        originalAnswers
    ]);
    // Build nested group structure - memoized to prevent recalculation
    const nestedQuestionGroups = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditForm.useMemo[nestedQuestionGroups]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$nestedGroups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNestedGroups"])(questionGroups, questions);
        }
    }["EditForm.useMemo[nestedQuestionGroups]"], [
        questionGroups,
        questions
    ]);
    // Initialize all groups (including nested ones) as expanded when questionGroups change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditForm.useEffect": ()=>{
            if (nestedQuestionGroups.length > 0) {
                const initialExpandedState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$nestedGroups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initializeGroupExpansionState"])(nestedQuestionGroups, true);
                setExpandedGroups(initialExpandedState);
            }
        }
    }["EditForm.useEffect"], [
        nestedQuestionGroups.length
    ]); // Only depend on length to avoid infinite loops
    // Get ungrouped questions - memoized to prevent recalculation
    const ungroupedQuestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditForm.useMemo[ungroupedQuestions]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$nestedGroups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUngroupedQuestions"])(questions);
        }
    }["EditForm.useMemo[ungroupedQuestions]"], [
        questions
    ]);
    // Create a unified list of form items (groups and individual questions) for dynamic ordering
    const unifiedFormItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditForm.useMemo[unifiedFormItems]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$nestedGroups$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createUnifiedFormItems"])(nestedQuestionGroups, ungroupedQuestions);
        }
    }["EditForm.useMemo[unifiedFormItems]"], [
        nestedQuestionGroups,
        ungroupedQuestions
    ]);
    // Toggle group expansion - memoized to prevent unnecessary re-renders
    const toggleGroupExpansion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EditForm.useCallback[toggleGroupExpansion]": (groupId)=>{
            setExpandedGroups({
                "EditForm.useCallback[toggleGroupExpansion]": (prev)=>({
                        ...prev,
                        [groupId]: !prev[groupId]
                    })
            }["EditForm.useCallback[toggleGroupExpansion]"]);
        }
    }["EditForm.useCallback[toggleGroupExpansion]"], []);
    const handleInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "EditForm.useCallback[handleInputChange]": (questionId, value)=>{
            // Mark this field as user-edited to prevent automatic restoration
            setUserEditedFields({
                "EditForm.useCallback[handleInputChange]": (prev)=>new Set(prev).add(questionId)
            }["EditForm.useCallback[handleInputChange]"]);
            setAnswers({
                "EditForm.useCallback[handleInputChange]": (prev)=>({
                        ...prev,
                        [questionId]: value
                    })
            }["EditForm.useCallback[handleInputChange]"]);
            setErrors({
                "EditForm.useCallback[handleInputChange]": (prev)=>({
                        ...prev,
                        [questionId]: ""
                    })
            }["EditForm.useCallback[handleInputChange]"]);
        }
    }["EditForm.useCallback[handleInputChange]"], []);
    const validateForm = ()=>{
        // Only validate visible questions
        const newErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$conditionalQuestions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateVisibleQuestions"])(visibleQuestions, answers);
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const updateAnswersMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "EditForm.useMutation[updateAnswersMutation]": async (answers)=>{
                const formattedAnswers = questions.map({
                    "EditForm.useMutation[updateAnswersMutation].formattedAnswers": (question)=>{
                        const answerValue = answers[question.id];
                        const isSelectMany = question.inputType === "selectmany";
                        const submissionAnswer = submission.answers.find({
                            "EditForm.useMutation[updateAnswersMutation].formattedAnswers.submissionAnswer": (sa)=>sa.question.id === question.id
                        }["EditForm.useMutation[updateAnswersMutation].formattedAnswers.submissionAnswer"]);
                        // For existing answers, use the answer ID
                        // For new questions, we'll create a new answer
                        const isNewQuestion = !submissionAnswer?.id;
                        if (isNewQuestion) {}
                        if (isSelectMany && Array.isArray(answerValue)) {
                            if (answerValue.length > 0) {
                                let questionOptionIds = [];
                                if (question.questionOptions) {
                                    questionOptionIds = answerValue.map({
                                        "EditForm.useMutation[updateAnswersMutation].formattedAnswers": (val)=>{
                                            const option = question.questionOptions.find({
                                                "EditForm.useMutation[updateAnswersMutation].formattedAnswers.option": (opt)=>opt.label === val
                                            }["EditForm.useMutation[updateAnswersMutation].formattedAnswers.option"]);
                                            return option?.id;
                                        }
                                    }["EditForm.useMutation[updateAnswersMutation].formattedAnswers"]).filter({
                                        "EditForm.useMutation[updateAnswersMutation].formattedAnswers": (id)=>id !== undefined
                                    }["EditForm.useMutation[updateAnswersMutation].formattedAnswers"]);
                                }
                                const baseAnswer = {
                                    projectId,
                                    questionId: question.id,
                                    answerType: question.inputType,
                                    value: answerValue.join(", "),
                                    questionOptionId: questionOptionIds,
                                    isOtherOption: false,
                                    formSubmissionId: submissionId
                                };
                                // If it's a new question, just use the base answer
                                // If it's an existing answer, add the id
                                if (isNewQuestion) {
                                    return baseAnswer;
                                } else {
                                    return {
                                        ...baseAnswer,
                                        id: submissionAnswer.id
                                    };
                                }
                            }
                            return null;
                        } else {
                            let formattedValue;
                            if (question.inputType === "number" || question.inputType === "decimal") {
                                formattedValue = answerValue ? Number(answerValue) : undefined;
                            } else if (question.inputType === "date" || question.inputType === "dateandtime") {
                                formattedValue = answerValue || undefined;
                            } else if (question.inputType === "table") {
                                // For table input type, convert the array of cell values to JSON string
                                formattedValue = Array.isArray(answerValue) && answerValue.length > 0 ? JSON.stringify(answerValue) : undefined;
                            } else {
                                formattedValue = answerValue ? String(answerValue) : undefined;
                            }
                            if (formattedValue === undefined) {
                                return null;
                            }
                            let questionOptionId;
                            if (question.inputType === "selectone" && answerValue && question.questionOptions) {
                                const option = question.questionOptions.find({
                                    "EditForm.useMutation[updateAnswersMutation].formattedAnswers.option": (opt)=>opt.label === answerValue
                                }["EditForm.useMutation[updateAnswersMutation].formattedAnswers.option"]);
                                questionOptionId = option?.id;
                            }
                            const baseAnswer = {
                                projectId,
                                questionId: question.id,
                                answerType: question.inputType,
                                value: formattedValue,
                                questionOptionId,
                                isOtherOption: false,
                                formSubmissionId: submissionId
                            };
                            // If it's a new question, just use the base answer
                            // If it's an existing answer, add the id
                            if (isNewQuestion) {
                                return baseAnswer;
                            } else {
                                return {
                                    ...baseAnswer,
                                    id: submissionAnswer.id
                                };
                            }
                        }
                    }
                }["EditForm.useMutation[updateAnswersMutation].formattedAnswers"]);
                // Filter out null values
                const validAnswers = formattedAnswers.filter({
                    "EditForm.useMutation[updateAnswersMutation].validAnswers": (answer)=>answer !== null
                }["EditForm.useMutation[updateAnswersMutation].validAnswers"]);
                if (validAnswers.length === 0) {
                    throw new Error("No valid answers with IDs to submit");
                }
                const simplifiedAnswers = validAnswers.map({
                    "EditForm.useMutation[updateAnswersMutation].simplifiedAnswers": (answer)=>{
                        // For existing answers (with id)
                        if (answer.id) {
                            return {
                                id: answer.id,
                                questionId: answer.questionId,
                                projectId,
                                value: answer.value,
                                answerType: answer.answerType,
                                questionOptionId: answer.questionOptionId,
                                isOtherOption: answer.isOtherOption || false,
                                formSubmissionId: answer.formSubmissionId
                            };
                        } else if (answer.questionId) {
                            return {
                                questionId: answer.questionId,
                                projectId,
                                value: answer.value,
                                answerType: answer.answerType,
                                questionOptionId: answer.questionOptionId,
                                isOtherOption: answer.isOtherOption || false,
                                formSubmissionId: answer.formSubmissionId
                            };
                        }
                        return null;
                    }
                }["EditForm.useMutation[updateAnswersMutation].simplifiedAnswers"]).filter({
                    "EditForm.useMutation[updateAnswersMutation].simplifiedAnswers": (answer)=>answer !== null
                }["EditForm.useMutation[updateAnswersMutation].simplifiedAnswers"]);
                try {
                    // Cast the array to any to bypass TypeScript's strict checking
                    // The backend can handle both formats (with id or with questionId)
                    const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$submission$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateMultipleAnswersWithEndpoint"])(simplifiedAnswers, projectId);
                    return data;
                } catch (error) {
                    console.error("Error with /answers/multiple endpoint:", error);
                    if (error.response) {
                        console.error("Error response data:", JSON.stringify(error.response.data, null, 2));
                        console.error("Error response status:", error.response.status);
                        console.error("Error response headers:", error.response.headers);
                    }
                    const results = [];
                    const failedQuestions = [];
                    for (const answer of validAnswers){
                        try {
                            // For existing answers with ID, use PATCH
                            if (answer.id) {
                                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch(`/answers/${answer.id}?projectId=${projectId}`, {
                                    id: answer.id,
                                    questionId: answer.questionId,
                                    projectId,
                                    value: answer.value,
                                    answerType: answer.answerType,
                                    questionOptionId: answer.questionOptionId,
                                    isOtherOption: answer.isOtherOption || false,
                                    formSubmissionId: answer.formSubmissionId
                                });
                                results.push(data);
                            } else if (answer.questionId) {
                                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`/answers?projectId=${projectId}`, {
                                    submissionId: answer.formSubmissionId,
                                    questionId: answer.questionId,
                                    value: answer.value,
                                    answerType: answer.answerType,
                                    questionOptionId: answer.questionOptionId,
                                    isOtherOption: answer.isOtherOption || false
                                });
                                results.push(data);
                            }
                        } catch (individualError) {
                            const identifier = answer.id || answer.questionId;
                            console.error(`Error handling answer ${identifier}:`, individualError);
                            if (individualError.response) {
                                console.error("Individual error response data:", JSON.stringify(individualError.response.data, null, 2));
                            }
                            failedQuestions.push(identifier);
                        }
                    }
                    if (failedQuestions.length > 0) {
                        throw new Error(`Failed to update answers with IDs: ${failedQuestions.join(", ")}`);
                    }
                    if (results.length > 0) {
                        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                            message: "Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",
                            type: "warning"
                        }));
                        return results;
                    }
                    throw error;
                }
            }
        }["EditForm.useMutation[updateAnswersMutation]"],
        onSuccess: {
            "EditForm.useMutation[updateAnswersMutation]": ()=>{
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: "Submission updated successfully. You can continue editing if needed.",
                    type: "success"
                }));
                // Clear user-edited fields tracking after successful save
                setUserEditedFields(new Set());
                onSave();
            }
        }["EditForm.useMutation[updateAnswersMutation]"],
        onError: {
            "EditForm.useMutation[updateAnswersMutation]": (error)=>{
                const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message || "Failed to update submission. Please check your input and try again.";
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showNotification"])({
                    message: errorMessage,
                    type: "error"
                }));
                console.error("Update Error:", {
                    message: errorMessage,
                    status: error.response?.status,
                    data: JSON.stringify(error.response?.data, null, 2)
                });
            }
        }["EditForm.useMutation[updateAnswersMutation]"],
        onSettled: {
            "EditForm.useMutation[updateAnswersMutation]": ()=>{
                setIsSubmitting(false);
            }
        }["EditForm.useMutation[updateAnswersMutation]"]
    });
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        updateAnswersMutation.mutate(answers);
    };
    // Helper function to check if a question is a follow-up question
    const isFollowUpQuestion = (questionId)=>{
        return questions.some((q)=>q.questionOptions?.some((option)=>option.nextQuestionId === questionId));
    };
    // Helper function to check if a question has follow-up questions
    const hasFollowUpQuestions = (question)=>{
        return question.questionOptions?.some((option)=>option.nextQuestionId) || false;
    };
    // Render a single question with its input and visual indicators
    const renderQuestion = (question)=>{
        const isFollowUp = isFollowUpQuestion(question.id);
        const hasFollowUps = hasFollowUpQuestions(question);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `border rounded-md p-4 ${isFollowUp ? "border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20" : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    className: "text-base font-medium",
                                    children: [
                                        question.label,
                                        question.isRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-red-500 ml-1",
                                            children: "*"
                                        }, void 0, false, {
                                            fileName: "[project]/components/form-preview/editForm.tsx",
                                            lineNumber: 592,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 589,
                                    columnNumber: 13
                                }, this),
                                isFollowUp && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                            className: "w-3 h-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/components/form-preview/editForm.tsx",
                                            lineNumber: 598,
                                            columnNumber: 17
                                        }, this),
                                        "Follow-up"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 597,
                                    columnNumber: 15
                                }, this),
                                hasFollowUps && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",
                                    children: "Has conditions"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 603,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 588,
                            columnNumber: 11
                        }, this),
                        question.hint && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: `text-sm mt-1 ${isFollowUp ? "text-primary-700 dark:text-primary-300" : "text-muted-foreground"}`,
                            children: question.hint
                        }, void 0, false, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 609,
                            columnNumber: 13
                        }, this),
                        errors[question.id] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-red-500 mt-1",
                            children: errors[question.id]
                        }, void 0, false, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 618,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 587,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-2",
                    children: renderQuestionInput(question)
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 623,
                    columnNumber: 9
                }, this)
            ]
        }, question.id, true, {
            fileName: "[project]/components/form-preview/editForm.tsx",
            lineNumber: 579,
            columnNumber: 7
        }, this);
    };
    const renderQuestionInput = (question)=>{
        const value = answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");
        switch(question.inputType){
            case "text":
                if (question.hint?.includes("multiline")) {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.hint || "Your answer",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/components/form-preview/editForm.tsx",
                        lineNumber: 636,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.hint || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 647,
                    columnNumber: 11
                }, this);
            case "number":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    type: "number",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.hint || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 658,
                    columnNumber: 11
                }, this);
            case "decimal":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    type: "number",
                    step: "any",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.hint || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 670,
                    columnNumber: 11
                }, this);
            case "selectone":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroup"], {
                    value: value,
                    onValueChange: (val)=>handleInputChange(question.id, val),
                    required: question.isRequired,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: question.questionOptions?.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                        value: option.label,
                                        id: `option-${option.id}`
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-preview/editForm.tsx",
                                        lineNumber: 691,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                        htmlFor: `option-${option.id}`,
                                        className: "cursor-pointer",
                                        children: option.label
                                    }, void 0, false, {
                                        fileName: "[project]/components/form-preview/editForm.tsx",
                                        lineNumber: 695,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/components/form-preview/editForm.tsx",
                                lineNumber: 690,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/components/form-preview/editForm.tsx",
                        lineNumber: 688,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 683,
                    columnNumber: 11
                }, this);
            case "selectmany":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: question.questionOptions?.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Checkbox"], {
                                    id: `option-${option.id}`,
                                    checked: (value || []).includes(option.label),
                                    onCheckedChange: (checked)=>{
                                        const currentValues = value || [];
                                        const newValues = checked ? [
                                            ...currentValues,
                                            option.label
                                        ] : currentValues.filter((v)=>v !== option.label);
                                        handleInputChange(question.id, newValues);
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 712,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    htmlFor: `option-${option.id}`,
                                    className: "cursor-pointer",
                                    children: option.label
                                }, void 0, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 723,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, option.id, true, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 711,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 709,
                    columnNumber: 11
                }, this);
            case "date":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        className: "input-field w-full",
                        type: "date",
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.hint || "Select date",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/components/form-preview/editForm.tsx",
                        lineNumber: 737,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 736,
                    columnNumber: 11
                }, this);
            case "dateandtime":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        className: "input-field w-full",
                        type: "time",
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.hint || "Select time",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/components/form-preview/editForm.tsx",
                        lineNumber: 751,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 750,
                    columnNumber: 11
                }, this);
            case "table":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$TableInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableInput"], {
                    questionId: question.id,
                    value: value,
                    onChange: (cellValues)=>handleInputChange(question.id, cellValues),
                    required: question.isRequired,
                    tableLabel: question.label
                }, void 0, false, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 764,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",
                children: [
                    "Edit Submission",
                    projectData?.name ? ` for ${projectData.name}` : ""
                ]
            }, void 0, true, {
                fileName: "[project]/components/form-preview/editForm.tsx",
                lineNumber: 782,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSubmit,
                className: "p-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        questions.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center py-12",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground",
                                children: "This form has no questions yet."
                            }, void 0, false, {
                                fileName: "[project]/components/form-preview/editForm.tsx",
                                lineNumber: 789,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 788,
                            columnNumber: 13
                        }, this) : // Render unified form items (groups and individual questions) in order
                        unifiedFormItems.map((item)=>{
                            if (item.type === 'group') {
                                const group = item.data;
                                const isExpanded = expandedGroups[group.id];
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedGroupRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    group: group,
                                    nestingLevel: 0,
                                    visibleQuestions: visibleQuestions,
                                    nestedQuestions: nestedQuestions,
                                    renderQuestionInput: renderQuestionInput,
                                    errors: errors,
                                    onToggleExpansion: toggleGroupExpansion,
                                    isExpanded: isExpanded,
                                    expandedGroups: expandedGroups,
                                    className: ""
                                }, `group-${group.id}`, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 799,
                                    columnNumber: 19
                                }, this);
                            } else {
                                const question = item.data;
                                // Only render ungrouped questions that are visible
                                if (!visibleQuestions.some((vq)=>vq.id === question.id)) {
                                    return null;
                                }
                                // Find the nested question structure for this question
                                const nestedQuestion = nestedQuestions.find((nq)=>nq.question.id === question.id);
                                if (nestedQuestion) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$NestedQuestionRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        questionGroup: nestedQuestion,
                                        renderQuestionInput: renderQuestionInput,
                                        errors: errors,
                                        className: ""
                                    }, question.id, false, {
                                        fileName: "[project]/components/form-preview/editForm.tsx",
                                        lineNumber: 827,
                                        columnNumber: 21
                                    }, this);
                                }
                                return renderQuestion(question);
                            }
                        }),
                        questions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-6 flex justify-end gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "btn-primary bg-neutral-500 hover:bg-neutral-600",
                                    type: "button",
                                    onClick: onClose,
                                    disabled: isSubmitting,
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 844,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "btn-primary",
                                    type: "submit",
                                    disabled: isSubmitting,
                                    children: isSubmitting ? "Saving..." : "Save Changes"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-preview/editForm.tsx",
                                    lineNumber: 852,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/form-preview/editForm.tsx",
                            lineNumber: 843,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/form-preview/editForm.tsx",
                    lineNumber: 786,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/form-preview/editForm.tsx",
                lineNumber: 785,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/form-preview/editForm.tsx",
        lineNumber: 781,
        columnNumber: 5
    }, this);
}
_s(EditForm, "M7mJiGefFj3cbCBJg6bt1eZaVqY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
_c = EditForm;
var _c;
__turbopack_context__.k.register(_c, "EditForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EditSubmissionPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/encodeDecode.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/form-builder.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$general$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/general/Spinner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$preview$2f$editForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-preview/editForm.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
const fetchSubmissions = async (projectId, submissionId)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/form-submissions/${projectId}`);
    const submissions = data.data.formSubmissions;
    const submission = submissions.find((s)=>s.id === submissionId);
    if (!submission) {
        throw new Error("Submission not found");
    }
    return submission;
};
function EditSubmissionPage() {
    _s();
    const { hashedId, submissionId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const projectId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decode"])(hashedId);
    const parsedSubmissionId = Number(submissionId);
    // Validate projectId and submissionId
    if (projectId === null || isNaN(parsedSubmissionId)) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Error: Invalid project or submission ID."
        }, void 0, false, {
            fileName: "[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx",
            lineNumber: 30,
            columnNumber: 12
        }, this);
    }
    const { data: questions = [], isLoading: questionsLoading, isError: questionsError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "questions",
            projectId
        ],
        queryFn: {
            "EditSubmissionPage.useQuery": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchQuestions"])({
                    projectId
                })
        }["EditSubmissionPage.useQuery"],
        enabled: !!projectId
    });
    const { data: submission, isLoading: submissionLoading, isError: submissionError, refetch: refetchSubmission } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "submission",
            projectId,
            parsedSubmissionId
        ],
        queryFn: {
            "EditSubmissionPage.useQuery": ()=>fetchSubmissions(projectId, parsedSubmissionId)
        }["EditSubmissionPage.useQuery"],
        enabled: !!projectId && !!parsedSubmissionId
    });
    if (questionsLoading || submissionLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$general$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx",
            lineNumber: 55,
            columnNumber: 12
        }, this);
    }
    if (questionsError || submissionError || !questions || !submission) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            className: "text-sm text-red-500",
            children: "Error loading submission or form. Please try again."
        }, void 0, false, {
            fileName: "[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx",
            lineNumber: 60,
            columnNumber: 7
        }, this);
    }
    const handleSave = ()=>{
        // Notify table to refetch if there's an opener window
        if (window.opener) {
            window.opener.postMessage({
                type: "REFETCH_SUBMISSIONS"
            }, "*");
        }
        // Refetch the submission data to update the UI
        refetchSubmission();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$preview$2f$editForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EditForm"], {
            questions: questions,
            submission: submission,
            projectId: projectId,
            submissionId: parsedSubmissionId,
            onSave: handleSave
        }, void 0, false, {
            fileName: "[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx",
            lineNumber: 77,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_s(EditSubmissionPage, "a89niGtsI1Ff61hhvY92JPyvsZ4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
_c = EditSubmissionPage;
var _c;
__turbopack_context__.k.register(_c, "EditSubmissionPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_d97ff90d._.js.map